"""
Thème Fluent personnalisé pour GSlim
Styles CSS modernes avec animations et effets Fluent Design
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QPalette


class FluentTheme:
    """Gestionnaire de thème Fluent avec styles personnalisés"""
    
    # === PALETTE SOMBRE ===
    DARK_COLORS = {
        'background_primary': '#121212',
        'background_secondary': '#1F1F1F',
        'background_tertiary': '#252525',
        'surface': '#2C2C2C',
        'surface_variant': '#3C3C3C',
        
        'text_primary': '#F0F0F0',
        'text_secondary': '#B0B0B0',
        'text_tertiary': '#808080',
        'text_disabled': '#606060',
        
        'primary': '#2196F3',
        'primary_variant': '#1976D2',
        'secondary': '#00BCD4',
        'secondary_variant': '#00ACC1',
        
        'success': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'info': '#2196F3',
        
        'border': '#2C2C2C',
        'border_variant': '#3C3C3C',
        'divider': '#1F1F1F'
    }
    
    # === PALETTE CLAIRE ===
    LIGHT_COLORS = {
        'background_primary': '#FFFFFF',
        'background_secondary': '#F5F5F5',
        'background_tertiary': '#FAFAFA',
        'surface': '#FFFFFF',
        'surface_variant': '#F0F0F0',
        
        'text_primary': '#212121',
        'text_secondary': '#757575',
        'text_tertiary': '#9E9E9E',
        'text_disabled': '#BDBDBD',
        
        'primary': '#2196F3',
        'primary_variant': '#1976D2',
        'secondary': '#00BCD4',
        'secondary_variant': '#00ACC1',
        
        'success': '#4CAF50',
        'warning': '#FF9800',
        'error': '#F44336',
        'info': '#2196F3',
        
        'border': '#E0E0E0',
        'border_variant': '#BDBDBD',
        'divider': '#F5F5F5'
    }
    
    @classmethod
    def get_fluent_dark_stylesheet(cls):
        """Retourner le stylesheet Fluent sombre"""
        colors = cls.DARK_COLORS
        
        return f"""
        /* === STYLES GÉNÉRAUX === */
        QWidget {{
            background-color: {colors['background_primary']}; */ */ */
            color: {colors['text_primary']}; */ */ */
            font-family: 'Segoe UI', 'Roboto', 'Poppins', sans-serif; */ */ */
            font-size: 14px; */ */ */
            selection-background-color: {colors['primary']}; */ */ */
            selection-color: white; */ */ */
        }}
        
        /* === FENÊTRE PRINCIPALE === */
        QMainWindow {{
            background-color: {colors['background_primary']}; */ */ */
            border: 1px solid {colors['border']}; */ */ */
        }}
        
        /* === CARTES FLUENT === */
        QFrame[class="fluent-card"] {{
            background-color: {colors['background_secondary']}; */ */ */
            border: 1px solid {colors['border']}; */ */ */
            border-radius: 12px; */ */ */
            padding: 20px; */ */ */
            margin: 8px; */ */ */
        }}
        
        QFrame[class="fluent-card"]:hover {{
            background-color: {colors['background_tertiary']}; */ */ */
            border-color: {colors['primary']}; */ */ */
            /* transform: translateY(-2px); */ */ */
            /* transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */ */ */
        }}
        
        /* === CARTES DE STATISTIQUES === */
        QFrame[class="stat-card"] {{
            background-color: {colors['background_secondary']}; */ */ */
            border: 1px solid {colors['border']}; */ */ */
            border-radius: 16px; */ */ */
            padding: 24px; */ */ */
            margin: 12px; */ */ */
            min-height: 140px; */ */ */
        }}
        
        QFrame[class="stat-card"]:hover {{
            background-color: {colors['background_tertiary']}; */ */ */
            border-color: {colors['primary']}; */ */ */
            /* transform: translateY(-4px); */ */ */
            /* box-shadow: 0 8px 32px rgba(33, 150, 243, 0.2); */ */ */
            /* transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */ */ */
        }}
        
        QFrame[class="stat-card-primary"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['primary']}, stop:1 {colors['primary_variant']}); */ */ */
            color: white; */ */ */
            border: none; */ */ */
        }}
        
        QFrame[class="stat-card-success"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['success']}, stop:1 #388E3C); */ */ */
            color: white; */ */ */
            border: none; */ */ */
        }}
        
        QFrame[class="stat-card-warning"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['warning']}, stop:1 #F57C00); */ */ */
            color: white; */ */ */
            border: none; */ */ */
        }}
        
        QFrame[class="stat-card-error"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['error']}, stop:1 #D32F2F); */ */ */
            color: white; */ */ */
            border: none; */ */ */
        }}
        
        QFrame[class="stat-card-info"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors['info']}, stop:1 {colors['secondary']}); */ */ */
            color: white; */ */ */
            border: none; */ */ */
        }}
        
        /* === BOUTONS FLUENT === */
        QPushButton {{
            background-color: {colors['primary']}; */ */ */
            color: white; */ */ */
            border: none; */ */ */
            border-radius: 8px; */ */ */
            padding: 12px 24px; */ */ */
            font-size: 14px; */ */ */
            font-weight: 500; */ */ */
            min-height: 20px; */ */ */
        }}
        
        QPushButton:hover {{
            background-color: {colors['primary_variant']}; */ */ */
            /* transform: translateY(-1px); */ */ */
            /* box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3); */ */ */
            /* transition: all 0.2s ease-out; */ */ */
        }}
        
        QPushButton:pressed {{
            background-color: #0D47A1; */ */ */
            /* transform: translateY(0px); */ */ */
            /* box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2); */ */ */
        }}
        
        QPushButton:disabled {{
            background-color: {colors['text_disabled']}; */ */ */
            color: {colors['text_tertiary']}; */ */ */
            /* transform: none; */ */ */
            /* box-shadow: none; */ */ */
        }}
        
        QPushButton[class="secondary"] {{
            background-color: {colors['background_tertiary']}; */ */ */
            color: {colors['text_primary']}; */ */ */
            border: 1px solid {colors['border_variant']}; */ */ */
        }}
        
        QPushButton[class="secondary"]:hover {{
            background-color: {colors['surface_variant']}; */ */ */
            border-color: {colors['primary']}; */ */ */
        }}
        
        QPushButton[class="success"] {{
            background-color: {colors['success']}; */ */ */
        }}
        
        QPushButton[class="success"]:hover {{
            background-color: #388E3C; */ */ */
        }}
        
        QPushButton[class="warning"] {{
            background-color: {colors['warning']}; */ */ */
        }}
        
        QPushButton[class="warning"]:hover {{
            background-color: #F57C00; */ */ */
        }}
        
        QPushButton[class="danger"] {{
            background-color: {colors['error']}; */ */ */
        }}
        
        QPushButton[class="danger"]:hover {{
            background-color: #D32F2F; */ */ */
        }}
        
        /* === LABELS ET TEXTE === */
        QLabel[class="title"] {{
            color: {colors['text_primary']}; */ */ */
            font-size: 28px; */ */ */
            font-weight: 600; */ */ */
            margin-bottom: 8px; */ */ */
        }}
        
        QLabel[class="subtitle"] {{
            color: {colors['text_secondary']}; */ */ */
            font-size: 18px; */ */ */
            font-weight: 500; */ */ */
            margin-bottom: 16px; */ */ */
        }}
        
        QLabel[class="body"] {{
            color: {colors['text_primary']}; */ */ */
            font-size: 14px; */ */ */
            font-weight: 400; */ */ */
            line-height: 1.5; */ */ */
        }}
        
        QLabel[class="caption"] {{
            color: {colors['text_secondary']}; */ */ */
            font-size: 12px; */ */ */
            font-weight: 400; */ */ */
        }}
        
        QLabel[class="stat-value"] {{
            color: inherit; */ */ */
            font-size: 32px; */ */ */
            font-weight: 700; */ */ */
            margin-bottom: 8px; */ */ */
        }}
        
        QLabel[class="stat-label"] {{
            color: inherit; */ */ */
            font-size: 14px; */ */ */
            font-weight: 500; */ */ */
            opacity: 0.9; */ */ */
        }}
        
        /* === CHAMPS DE SAISIE === */
        QLineEdit, QTextEdit {{
            background-color: {colors['background_secondary']}; */ */ */
            border: 2px solid {colors['border']}; */ */ */
            border-radius: 8px; */ */ */
            padding: 12px 16px; */ */ */
            font-size: 14px; */ */ */
            color: {colors['text_primary']}; */ */ */
        }}
        
        QLineEdit:focus, QTextEdit:focus {{
            border-color: {colors['primary']}; */ */ */
            background-color: {colors['background_tertiary']}; */ */ */
            outline: none; */ */ */
        }}
        
        QLineEdit:hover, QTextEdit:hover {{
            border-color: {colors['border_variant']}; */ */ */
        }}
        
        /* === SCROLLBARS === */
        QScrollBar:vertical {{
            background-color: {colors['background_secondary']}; */ */ */
            width: 12px; */ */ */
            border-radius: 6px; */ */ */
            margin: 0; */ */ */
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['surface_variant']}; */ */ */
            border-radius: 6px; */ */ */
            min-height: 20px; */ */ */
            margin: 2px; */ */ */
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['primary']}; */ */ */
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px; */ */ */
        }}
        
        /* === TABLES === */
        QTableWidget {{
            background-color: {colors['background_secondary']}; */ */ */
            border: 1px solid {colors['border']}; */ */ */
            border-radius: 12px; */ */ */
            gridline-color: {colors['border']}; */ */ */
            selection-background-color: rgba(33, 150, 243, 0.2); */ */ */
        }}
        
        QTableWidget::item {{
            padding: 12px; */ */ */
            border-bottom: 1px solid {colors['border']}; */ */ */
            color: {colors['text_primary']}; */ */ */
        }}
        
        QTableWidget::item:selected {{
            background-color: rgba(33, 150, 243, 0.3); */ */ */
            color: {colors['text_primary']}; */ */ */
        }}
        
        QTableWidget::item:hover {{
            background-color: {colors['background_tertiary']}; */ */ */
        }}
        
        QHeaderView::section {{
            background-color: {colors['surface']}; */ */ */
            color: {colors['text_secondary']}; */ */ */
            padding: 16px 12px; */ */ */
            border: none; */ */ */
            border-bottom: 2px solid {colors['border']}; */ */ */
            font-weight: 600; */ */ */
            font-size: 13px; */ */ */
        }}
        
        /* === TOOLTIPS === */
        QToolTip {{
            background-color: {colors['surface_variant']}; */ */ */
            color: {colors['text_primary']}; */ */ */
            border: 1px solid {colors['border_variant']}; */ */ */
            border-radius: 6px; */ */ */
            padding: 8px 12px; */ */ */
            font-size: 12px; */ */ */
        }}
        
        /* === MENU ET NAVIGATION === */
        QMenuBar {{
            background-color: {colors['background_secondary']}; */ */ */
            color: {colors['text_primary']}; */ */ */
            border-bottom: 1px solid {colors['border']}; */ */ */
        }}
        
        QMenuBar::item {{
            background-color: transparent; */ */ */
            padding: 8px 16px; */ */ */
        }}
        
        QMenuBar::item:selected {{
            background-color: {colors['primary']}; */ */ */
            color: white; */ */ */
        }}
        
        QMenu {{
            background-color: {colors['background_secondary']}; */ */ */
            border: 1px solid {colors['border']}; */ */ */
            border-radius: 8px; */ */ */
            padding: 4px; */ */ */
        }}
        
        QMenu::item {{
            padding: 8px 16px; */ */ */
            border-radius: 4px; */ */ */
        }}
        
        QMenu::item:selected {{
            background-color: {colors['primary']}; */ */ */
            color: white; */ */ */
        }}
        
        /* === ANIMATIONS ET TRANSITIONS === */
        * {{
            /* transition: all 0.2s ease-out; */ */ */
        }}
        
        *:hover {{
            /* transition: all 0.2s ease-out; */ */ */
        }}
        """
    
    @classmethod
    def get_fluent_light_stylesheet(cls):
        """Retourner le stylesheet Fluent clair"""
        colors = cls.LIGHT_COLORS
        
        # Utiliser la même structure mais avec les couleurs claires
        dark_stylesheet = cls.get_fluent_dark_stylesheet()
        
        # Remplacer les couleurs sombres par les couleurs claires
        for dark_key, dark_value in cls.DARK_COLORS.items():
            if dark_key in cls.LIGHT_COLORS:
                dark_stylesheet = dark_stylesheet.replace(dark_value, cls.LIGHT_COLORS[dark_key])
        
        return dark_stylesheet
    
    @classmethod
    def get_current_theme_colors(cls, is_dark=True):
        """Obtenir les couleurs du thème actuel"""
        return cls.DARK_COLORS if is_dark else cls.LIGHT_COLORS
