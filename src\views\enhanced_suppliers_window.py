"""
Fenêtre Fournisseurs Améliorée - GSlim
Interface moderne pour la gestion des fournisseurs
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QFrame, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from controllers.supplier_controller import SupplierController
    from widgets.enhanced_widgets import EnhancedCard, EnhancedTable, StatusIndicator
    from utils.logger import setup_logger
except ImportError as e:
    print(f"Import error: {e}")


class EnhancedSuppliersWindow(QWidget):
    """Fenêtre fournisseurs améliorée"""
    
    def __init__(self, app_instance, parent=None):
        super().__init__(parent)
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        
        try:
            # Initialiser le contrôleur
            db_manager = app_instance.get_database_manager() if hasattr(app_instance, 'get_database_manager') else None
            if db_manager:
                self.controller = SupplierController(db_manager)
            else:
                self.controller = None
                self.logger.warning("Aucun gestionnaire de base de données disponible")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation du contrôleur: {e}")
            self.controller = None
        
        self.setup_ui()
        self.load_suppliers()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setWindowTitle("Gestion des Fournisseurs")
        self.setMinimumSize(1000, 700)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title = QLabel("Gestion des Fournisseurs")
        title.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Bouton nouveau fournisseur
        new_btn = QPushButton("➕ Nouveau Fournisseur")
        new_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        new_btn.clicked.connect(self.show_new_supplier_dialog)
        header_layout.addWidget(new_btn)
        
        main_layout.addLayout(header_layout)
        
        # Statistiques
        stats_layout = QHBoxLayout()
        
        try:
            if self.controller:
                stats = self.controller.get_statistics()
                total_suppliers = stats.get('total_suppliers', 0)
            else:
                total_suppliers = 0
            
            # Carte total fournisseurs
            total_card = self.create_stat_card("Total Fournisseurs", str(total_suppliers), "#3498db")
            stats_layout.addWidget(total_card)
            
            # Carte fournisseurs actifs
            active_card = self.create_stat_card("Fournisseurs Actifs", str(total_suppliers), "#27ae60")
            stats_layout.addWidget(active_card)
            
            # Carte nouvelles commandes
            orders_card = self.create_stat_card("Commandes en Cours", "0", "#f39c12")
            stats_layout.addWidget(orders_card)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création des statistiques: {e}")
            # Carte d'erreur
            error_card = self.create_stat_card("Erreur", "N/A", "#e74c3c")
            stats_layout.addWidget(error_card)
        
        stats_layout.addStretch()
        main_layout.addLayout(stats_layout)
        
        # Barre de recherche
        search_layout = QHBoxLayout()
        
        search_label = QLabel("Rechercher:")
        search_label.setFont(QFont("Segoe UI", 12))
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Nom du fournisseur, contact, email...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        self.search_input.textChanged.connect(self.filter_suppliers)
        search_layout.addWidget(self.search_input)
        
        search_layout.addStretch()
        main_layout.addLayout(search_layout)
        
        # Table des fournisseurs
        self.create_suppliers_table()
        main_layout.addWidget(self.suppliers_table)
        
        # Appliquer le style général
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
    
    def create_stat_card(self, title, value, color):
        """Créer une carte de statistique"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                padding: 20px;
            }}
        """)
        card.setFixedSize(200, 100)
        
        layout = QVBoxLayout(card)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 28, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 12))
        title_label.setStyleSheet("color: #7f8c8d;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        return card
    
    def create_suppliers_table(self):
        """Créer la table des fournisseurs"""
        self.suppliers_table = QTableWidget()
        
        # Colonnes
        headers = ["ID", "Nom", "Contact", "Téléphone", "Email", "Adresse", "Actions"]
        self.suppliers_table.setColumnCount(len(headers))
        self.suppliers_table.setHorizontalHeaderLabels(headers)
        
        # Style de la table
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                gridline-color: #e1e8ed;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #e1e8ed;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 12px;
                border: none;
                border-bottom: 2px solid #3498db;
                font-weight: bold;
            }
        """)
        
        # Configuration de la table
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.horizontalHeader().setStretchLastSection(True)
        self.suppliers_table.verticalHeader().setVisible(False)
        
        # Ajuster les colonnes
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Contact
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Téléphone
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # Email
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions
    
    def load_suppliers(self):
        """Charger les fournisseurs"""
        try:
            if not self.controller:
                self.show_error("Contrôleur non disponible")
                return
            
            suppliers = self.controller.get_all_suppliers()
            self.populate_table(suppliers)
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des fournisseurs: {e}")
            self.show_error(f"Erreur de chargement: {e}")
    
    def populate_table(self, suppliers):
        """Remplir la table avec les fournisseurs"""
        self.suppliers_table.setRowCount(len(suppliers))
        
        for row, supplier in enumerate(suppliers):
            # ID
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier.get('id', ''))))
            
            # Nom
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.get('nom', '')))
            
            # Contact
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.get('contact', '')))
            
            # Téléphone
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.get('telephone', '')))
            
            # Email
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier.get('email', '')))
            
            # Adresse
            self.suppliers_table.setItem(row, 5, QTableWidgetItem(supplier.get('adresse', '')))
            
            # Actions
            actions_btn = QPushButton("⚙️ Actions")
            actions_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            self.suppliers_table.setCellWidget(row, 6, actions_btn)
    
    def filter_suppliers(self):
        """Filtrer les fournisseurs selon la recherche"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.suppliers_table.rowCount()):
            show_row = False
            
            for col in range(self.suppliers_table.columnCount() - 1):  # Exclure la colonne Actions
                item = self.suppliers_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            
            self.suppliers_table.setRowHidden(row, not show_row)
    
    def show_new_supplier_dialog(self):
        """Afficher le dialogue de nouveau fournisseur"""
        QMessageBox.information(self, "Nouveau Fournisseur", 
                              "Fonctionnalité de création de fournisseur à implémenter.")
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        QMessageBox.critical(self, "Erreur", message)
