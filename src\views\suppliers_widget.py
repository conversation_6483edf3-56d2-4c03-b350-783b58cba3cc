"""
Widget de gestion des fournisseurs conforme au cahier des charges
Gestion avec évaluation selon les spécifications
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QHeaderView, QAbstractItemView,
    QMessageBox, QFrame
)
from PyQt5.QtCore import pyqtSignal, QTimer
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        TitleLabel, PushButton, LineEdit, TableWidget, FluentIcon,
        CardWidget
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class SuppliersWidget(QWidget):
    """
    Widget de gestion des fournisseurs selon les spécifications du cahier des charges
    Fonctionnalités: CRUD complet, évaluation 1-5 étoiles, recherche
    """

    error_occurred = pyqtSignal(str)
    success_message = pyqtSignal(str)
    info_message = pyqtSignal(str)

    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.current_user = None

        # Variables d'instance
        self.suppliers_data = []
        self.filtered_suppliers = []

        # Initialiser l'interface
        self._init_ui()

        # Charger les données initiales
        self.refresh_data()

        self.logger.info("Widget fournisseurs initialisé")

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # En-tête
        self._create_header(main_layout)

        # Barre de recherche et actions
        self._create_search_bar(main_layout)

        # Tableau des fournisseurs
        self._create_suppliers_table(main_layout)

        # Barre d'actions
        self._create_action_bar(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du widget"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Fournisseurs")
        else:
            title = QLabel("Gestion des Fournisseurs")
            title.setFont(QFont("Segoe UI", 20, QFont.Bold))

        subtitle = QLabel("Gérez vos fournisseurs avec système d'évaluation")
        subtitle.setFont(QFont("Segoe UI", 10))
        subtitle.setStyleSheet("color: gray;")

        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_search_bar(self, layout):
        """Créer la barre de recherche"""
        search_layout = QHBoxLayout()

        # Champ de recherche
        if FLUENT_AVAILABLE:
            self.search_input = LineEdit()
        else:
            self.search_input = QLineEdit()

        self.search_input.setPlaceholderText("Rechercher par nom, contact ou email...")
        self.search_input.textChanged.connect(self._on_search_changed)

        # Bouton d'actualisation
        if FLUENT_AVAILABLE:
            refresh_btn = PushButton("Actualiser")
            refresh_btn.setIcon(FluentIcon.SYNC)
        else:
            refresh_btn = QPushButton("Actualiser")

        refresh_btn.clicked.connect(self.refresh_data)

        search_layout.addWidget(QLabel("Recherche:"))
        search_layout.addWidget(self.search_input, 1)
        search_layout.addWidget(refresh_btn)

        layout.addLayout(search_layout)

    def _create_suppliers_table(self, layout):
        """Créer le tableau des fournisseurs"""
        if FLUENT_AVAILABLE:
            self.suppliers_table = TableWidget()
        else:
            self.suppliers_table = QTableWidget()

        # Configuration du tableau selon spécifications
        columns = ["ID", "Nom", "Contact", "Email", "Téléphone", "Évaluation", "Notes"]

        self.suppliers_table.setColumnCount(len(columns))
        self.suppliers_table.setHorizontalHeaderLabels(columns)

        # Configuration de l'affichage
        self.suppliers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.suppliers_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.suppliers_table.setAlternatingRowColors(True)

        # Ajustement des colonnes
        header = self.suppliers_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(3, QHeaderView.Stretch)  # Email

        # Double-clic pour modifier
        self.suppliers_table.doubleClicked.connect(self._on_supplier_double_click)

        layout.addWidget(self.suppliers_table, 1)

    def _create_action_bar(self, layout):
        """Créer la barre d'actions"""
        actions_layout = QHBoxLayout()

        # Boutons d'action selon spécifications
        if FLUENT_AVAILABLE:
            self.add_btn = PushButton("Ajouter")
            self.add_btn.setIcon(FluentIcon.ADD)

            self.edit_btn = PushButton("Modifier")
            self.edit_btn.setIcon(FluentIcon.EDIT)

            self.delete_btn = PushButton("Supprimer")
            self.delete_btn.setIcon(FluentIcon.DELETE)
        else:
            self.add_btn = QPushButton("Ajouter")
            self.edit_btn = QPushButton("Modifier")
            self.delete_btn = QPushButton("Supprimer")

        # État initial des boutons
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)

        # Connecter les signaux
        self.add_btn.clicked.connect(self._on_add_supplier)
        self.edit_btn.clicked.connect(self._on_edit_supplier)
        self.delete_btn.clicked.connect(self._on_delete_supplier)

        # Gestion de la sélection
        self.suppliers_table.itemSelectionChanged.connect(self._on_selection_changed)

        # Assemblage
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addStretch()

        layout.addLayout(actions_layout)

    def set_current_user(self, user_data):
        """Définir l'utilisateur actuel"""
        self.current_user = user_data

    def _on_search_changed(self):
        """Gérer le changement de recherche"""
        self._apply_filters()

    def _apply_filters(self):
        """Appliquer les filtres de recherche"""
        search_term = self.search_input.text().lower()

        if not search_term:
            self.filtered_suppliers = self.suppliers_data.copy()
        else:
            self.filtered_suppliers = [
                supplier for supplier in self.suppliers_data
                if (search_term in supplier.get('name', '').lower() or
                    search_term in supplier.get('contact_person', '').lower() or
                    search_term in supplier.get('email', '').lower())
            ]

        self._update_table_display()

    def _update_table_display(self):
        """Mettre à jour l'affichage du tableau"""
        self.suppliers_table.setRowCount(len(self.filtered_suppliers))

        for row, supplier in enumerate(self.filtered_suppliers):
            # Colonnes selon spécifications
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier.get('id', ''))))
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.get('name', '')))
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.get('contact_person', '')))
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.get('email', '')))
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier.get('phone', '')))

            # Évaluation avec étoiles
            rating = int(supplier.get('rating', 0))
            stars = "★" * rating + "☆" * (5 - rating)
            rating_text = f"{stars} ({rating}/5)"
            self.suppliers_table.setItem(row, 5, QTableWidgetItem(rating_text))

            self.suppliers_table.setItem(row, 6, QTableWidgetItem(supplier.get('notes', '')))

    def _on_selection_changed(self):
        """Gérer le changement de sélection"""
        has_selection = len(self.suppliers_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _on_supplier_double_click(self):
        """Gérer le double-clic sur un fournisseur"""
        self._on_edit_supplier()

    def _on_add_supplier(self):
        """Ajouter un nouveau fournisseur"""
        try:
            from views.dialogs.supplier_dialog import SupplierDialog

            dialog = SupplierDialog(self)
            if dialog.exec_() == dialog.Accepted:
                supplier_data = dialog.get_supplier_data()

                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                supplier_id = db_manager.create_supplier(supplier_data)

                if supplier_id:
                    self.refresh_data()
                    self.success_message.emit("Fournisseur ajouté avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la sauvegarde du fournisseur")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout: {e}")
            self.error_occurred.emit(f"Erreur lors de l'ajout: {e}")

    def _on_edit_supplier(self):
        """Modifier le fournisseur sélectionné"""
        try:
            selected_row = self.suppliers_table.currentRow()
            if selected_row < 0:
                return

            supplier = self.filtered_suppliers[selected_row]

            from views.dialogs.supplier_dialog import SupplierDialog

            dialog = SupplierDialog(self, supplier)
            if dialog.exec_() == dialog.Accepted:
                supplier_data = dialog.get_supplier_data()

                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.update_supplier(supplier['id'], supplier_data)

                if success:
                    self.refresh_data()
                    self.success_message.emit("Fournisseur modifié avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la mise à jour du fournisseur")

        except Exception as e:
            self.logger.error(f"Erreur lors de la modification: {e}")
            self.error_occurred.emit(f"Erreur lors de la modification: {e}")

    def _on_delete_supplier(self):
        """Supprimer le fournisseur sélectionné"""
        try:
            selected_row = self.suppliers_table.currentRow()
            if selected_row < 0:
                return

            supplier = self.filtered_suppliers[selected_row]

            # Dialogue de confirmation selon spécifications
            rating = int(supplier.get('rating', 0))
            stars = "★" * rating + "☆" * (5 - rating)

            reply = QMessageBox.question(
                self,
                "Confirmer la suppression",
                f"Êtes-vous sûr de vouloir supprimer le fournisseur '{supplier.get('name', '')}'?\n\n"
                f"Évaluation: {stars} ({rating}/5)\n"
                "Les produits de ce fournisseur passeront en 'Aucun fournisseur'.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Supprimer de la base de données avec gestion des produits liés
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.delete_supplier(supplier['id'])

                if success:
                    self.refresh_data()
                    self.success_message.emit("Fournisseur supprimé avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la suppression du fournisseur")

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression: {e}")
            self.error_occurred.emit(f"Erreur lors de la suppression: {e}")

    def refresh_data(self):
        """Actualiser les données depuis la base de données"""
        try:
            db_manager = self.app_instance.get_database_manager()
            self.suppliers_data = self._load_suppliers(db_manager)
            self._apply_filters()

            self.logger.info(f"{len(self.suppliers_data)} fournisseurs chargés")

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement: {e}")
            self.error_occurred.emit(f"Erreur de chargement: {e}")

    def _load_suppliers(self, db_manager):
        """Charger les fournisseurs depuis la base de données"""
        try:
            # Récupérer le terme de recherche actuel
            search_term = self.search_input.text() if hasattr(self, 'search_input') else ""

            # Charger les fournisseurs avec filtre de recherche
            return db_manager.get_suppliers(search_term)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des fournisseurs: {e}")
            return []
