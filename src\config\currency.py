"""
Configuration et gestion des devises pour GSlim
"""

from typing import Dict, List, Tuple
from .settings import config


class CurrencyConfig:
    """Configuration des devises supportées"""
    
    # Dictionnaire des devises supportées
    CURRENCIES = {
        'DZD': {
            'name': 'Dinar Algérien',
            'symbol': 'د.ج',
            'code': 'DZD',
            'decimal_places': 2,
            'symbol_position': 'after',  # 'before' ou 'after'
            'thousands_separator': ' ',
            'decimal_separator': ',',
            'country': 'Algérie'
        },
        'EUR': {
            'name': 'Euro',
            'symbol': '€',
            'code': 'EUR',
            'decimal_places': 2,
            'symbol_position': 'after',
            'thousands_separator': ' ',
            'decimal_separator': ',',
            'country': 'Zone Euro'
        },
        'USD': {
            'name': 'Dollar Américain',
            'symbol': '$',
            'code': 'USD',
            'decimal_places': 2,
            'symbol_position': 'before',
            'thousands_separator': ',',
            'decimal_separator': '.',
            'country': 'États-Unis'
        },
        'GBP': {
            'name': 'Livre Sterling',
            'symbol': '£',
            'code': 'GBP',
            'decimal_places': 2,
            'symbol_position': 'before',
            'thousands_separator': ',',
            'decimal_separator': '.',
            'country': 'Royaume-Uni'
        },
        'CHF': {
            'name': 'Franc Suisse',
            'symbol': 'CHF',
            'code': 'CHF',
            'decimal_places': 2,
            'symbol_position': 'after',
            'thousands_separator': ' ',
            'decimal_separator': ',',
            'country': 'Suisse'
        },
        'MAD': {
            'name': 'Dirham Marocain',
            'symbol': 'د.م.',
            'code': 'MAD',
            'decimal_places': 2,
            'symbol_position': 'after',
            'thousands_separator': ' ',
            'decimal_separator': ',',
            'country': 'Maroc'
        },
        'TND': {
            'name': 'Dinar Tunisien',
            'symbol': 'د.ت',
            'code': 'TND',
            'decimal_places': 3,
            'symbol_position': 'after',
            'thousands_separator': ' ',
            'decimal_separator': ',',
            'country': 'Tunisie'
        }
    }
    
    @classmethod
    def get_default_currency(cls) -> str:
        """Obtenir la devise par défaut"""
        return config.DEFAULT_CURRENCY
    
    @classmethod
    def get_currency_info(cls, currency_code: str) -> Dict:
        """Obtenir les informations d'une devise"""
        return cls.CURRENCIES.get(currency_code, cls.CURRENCIES['DZD'])
    
    @classmethod
    def get_currency_symbol(cls, currency_code: str = None) -> str:
        """Obtenir le symbole d'une devise"""
        if currency_code is None:
            currency_code = cls.get_default_currency()
        return cls.get_currency_info(currency_code)['symbol']
    
    @classmethod
    def get_currency_name(cls, currency_code: str = None) -> str:
        """Obtenir le nom d'une devise"""
        if currency_code is None:
            currency_code = cls.get_default_currency()
        return cls.get_currency_info(currency_code)['name']
    
    @classmethod
    def get_available_currencies(cls) -> List[str]:
        """Obtenir la liste des codes de devises disponibles"""
        return list(cls.CURRENCIES.keys())
    
    @classmethod
    def get_currency_choices(cls) -> List[Tuple[str, str]]:
        """Obtenir les choix de devises pour les ComboBox"""
        return [(code, f"{info['name']} ({info['symbol']})") 
                for code, info in cls.CURRENCIES.items()]
    
    @classmethod
    def format_amount(cls, amount: float, currency_code: str = None, 
                     show_symbol: bool = True) -> str:
        """Formater un montant selon les règles de la devise"""
        if currency_code is None:
            currency_code = cls.get_default_currency()
        
        currency_info = cls.get_currency_info(currency_code)
        
        # Formater le nombre
        decimal_places = currency_info['decimal_places']
        thousands_sep = currency_info['thousands_separator']
        decimal_sep = currency_info['decimal_separator']
        
        # Arrondir selon le nombre de décimales
        rounded_amount = round(amount, decimal_places)
        
        # Formater avec les séparateurs
        if decimal_places > 0:
            formatted = f"{rounded_amount:,.{decimal_places}f}"
        else:
            formatted = f"{int(rounded_amount):,}"
        
        # Remplacer les séparateurs par ceux de la devise
        formatted = formatted.replace(',', 'TEMP_THOUSANDS')
        formatted = formatted.replace('.', decimal_sep)
        formatted = formatted.replace('TEMP_THOUSANDS', thousands_sep)
        
        # Ajouter le symbole si demandé
        if show_symbol:
            symbol = currency_info['symbol']
            if currency_info['symbol_position'] == 'before':
                formatted = f"{symbol} {formatted}"
            else:
                formatted = f"{formatted} {symbol}"
        
        return formatted
    
    @classmethod
    def parse_amount(cls, amount_str: str, currency_code: str = None) -> float:
        """Parser un montant formaté en float"""
        if currency_code is None:
            currency_code = cls.get_default_currency()
        
        currency_info = cls.get_currency_info(currency_code)
        
        # Nettoyer la chaîne
        cleaned = amount_str.strip()
        
        # Retirer le symbole
        symbol = currency_info['symbol']
        cleaned = cleaned.replace(symbol, '').strip()
        
        # Remplacer les séparateurs
        thousands_sep = currency_info['thousands_separator']
        decimal_sep = currency_info['decimal_separator']
        
        cleaned = cleaned.replace(thousands_sep, '')
        cleaned = cleaned.replace(decimal_sep, '.')
        
        try:
            return float(cleaned)
        except ValueError:
            return 0.0


# Instance globale pour faciliter l'utilisation
currency_config = CurrencyConfig()
