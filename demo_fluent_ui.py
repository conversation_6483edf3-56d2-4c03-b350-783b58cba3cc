#!/usr/bin/env python3
"""
Démonstration de l'interface Fluent moderne pour GSlim
Application de test avec tous les composants Fluent Widgets
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import setTheme, Theme, setThemeColor
    FLUENT_AVAILABLE = True
    print("✅ Fluent Widgets disponible")
except ImportError:
    FLUENT_AVAILABLE = False
    print("❌ Fluent Widgets non disponible")
    print("💡 Installation: pip install PyQt-Fluent-Widgets")

from views.fluent_dashboard import FluentDashboard
from styles.fluent_theme import FluentTheme


class FluentDemoApp:
    """Application de démonstration Fluent"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_app()
        self.create_window()
    
    def setup_app(self):
        """Configurer l'application"""
        self.app.setApplicationName("GSlim Fluent Demo")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("GSlim")
        
        # Configuration des polices
        font = QFont("Segoe UI", 10)
        self.app.setFont(font)
        
        # Configuration du thème Fluent
        if FLUENT_AVAILABLE:
            setTheme(Theme.DARK)
            setThemeColor('#2196F3')  # Bleu Material
            print("🎨 Thème Fluent sombre appliqué")
        else:
            # Appliquer le thème personnalisé
            self.app.setStyleSheet(FluentTheme.get_fluent_dark_stylesheet())
            print("🎨 Thème personnalisé appliqué")
    
    def create_window(self):
        """Créer la fenêtre principale"""
        self.window = FluentDashboard()
        
        # Appliquer des styles supplémentaires si nécessaire
        if not FLUENT_AVAILABLE:
            self.apply_fallback_styles()
        
        self.window.show()
        print("🚀 Interface Fluent lancée")
    
    def apply_fallback_styles(self):
        """Appliquer des styles de secours sans Fluent Widgets"""
        # Charger le fichier QSS
        qss_file = os.path.join("src", "styles", "fluent_modern.qss")
        if os.path.exists(qss_file):
            with open(qss_file, 'r', encoding='utf-8') as f:
                qss_content = f.read()
            self.app.setStyleSheet(qss_content)
            print("📄 Styles QSS chargés")
    
    def run(self):
        """Lancer l'application"""
        print("\n" + "="*60)
        print("🎨 DÉMONSTRATION FLUENT UI - GSLIM v1.0.0")
        print("="*60)
        
        if FLUENT_AVAILABLE:
            print("✨ Fonctionnalités Fluent Widgets:")
            print("   • FluentWindow avec effet Mica")
            print("   • NavigationInterface moderne")
            print("   • CardWidget avec animations")
            print("   • PushButton avec icônes Fluent")
            print("   • InfoBadge et notifications")
            print("   • Thème clair/sombre dynamique")
        else:
            print("🔧 Mode de compatibilité:")
            print("   • Styles CSS modernes")
            print("   • Animations PyQt5 natives")
            print("   • Dégradés et effets visuels")
            print("   • Design Fluent simulé")
        
        print("\n🎯 Fonctionnalités de l'interface:")
        print("   • Cartes de statistiques interactives")
        print("   • Boutons avec animations de survol")
        print("   • Navigation fluide et moderne")
        print("   • Changement de thème en temps réel")
        print("   • Notifications et feedback utilisateur")
        print("   • Design responsive et adaptatif")
        
        print("\n🚀 Lancement de l'interface...")
        print("💡 Testez les interactions et animations!")
        
        return self.app.exec_()


def check_dependencies():
    """Vérifier les dépendances"""
    print("🔍 Vérification des dépendances...")
    
    dependencies = {
        "PyQt5": True,
        "PyQt-Fluent-Widgets": FLUENT_AVAILABLE
    }
    
    for dep, available in dependencies.items():
        status = "✅" if available else "❌"
        print(f"   {status} {dep}")
    
    if not FLUENT_AVAILABLE:
        print("\n💡 Pour une expérience optimale, installez Fluent Widgets:")
        print("   pip install PyQt-Fluent-Widgets")
        print("   ou")
        print("   pip install PyQt6-Fluent-Widgets")
    
    return True


def main():
    """Fonction principale"""
    try:
        print("🎨 GSlim Fluent UI Demo")
        print("="*30)
        
        # Vérifier les dépendances
        if not check_dependencies():
            return 1
        
        # Créer et lancer l'application
        demo = FluentDemoApp()
        return demo.run()
        
    except KeyboardInterrupt:
        print("\n⏹️  Application interrompue par l'utilisateur")
        return 0
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
