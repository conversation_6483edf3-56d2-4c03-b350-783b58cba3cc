#!/usr/bin/env python3
"""
Test d'Intégration Complète - GSlim
Vérifie que tous les thèmes et composants fonctionnent correctement
"""

import sys
import os
import traceback

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Tester tous les imports"""
    print("🔍 Test des imports...")
    
    tests = []
    
    # Test du gestionnaire de thèmes
    try:
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        tests.append(("✅", "Gestionnaire de thèmes"))
    except Exception as e:
        tests.append(("❌", f"Gestionnaire de thèmes: {e}"))
    
    # Test des thèmes
    themes_to_test = [
        ("styles.modern_theme", "ModernTheme", "Thème moderne"),
        ("styles.professional_theme", "ProfessionalTheme", "Thème professionnel"),
        ("styles.fluent_theme", "FluentTheme", "Thème Fluent"),
        ("styles.futuristic_theme", "FuturisticTheme", "Thème cyberpunk")
    ]
    
    for module_name, class_name, display_name in themes_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            tests.append(("✅", display_name))
        except Exception as e:
            tests.append(("❌", f"{display_name}: {e}"))
    
    # Test des widgets
    widgets_to_test = [
        ("widgets.theme_selector", "ThemeSelector", "Sélecteur de thèmes"),
        ("widgets.modern_widgets", "ModernStatCard", "Widgets modernes"),
        ("widgets.fluent_components", "FluentStatCard", "Composants Fluent"),
        ("widgets.cyberpunk_components", "CyberpunkStatCard", "Composants cyberpunk")
    ]
    
    for module_name, class_name, display_name in widgets_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            tests.append(("✅", display_name))
        except Exception as e:
            tests.append(("❌", f"{display_name}: {e}"))
    
    # Test des vues
    views_to_test = [
        ("views.unified_demo", "UnifiedDemoWindow", "Démonstration unifiée"),
        ("views.cyberpunk_dashboard", "CyberpunkDashboard", "Dashboard cyberpunk"),
        ("views.fluent_dashboard", "FluentDashboard", "Dashboard Fluent")
    ]
    
    for module_name, class_name, display_name in views_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            tests.append(("✅", display_name))
        except Exception as e:
            tests.append(("❌", f"{display_name}: {e}"))
    
    # Afficher les résultats
    for status, message in tests:
        print(f"   {status} {message}")
    
    success_count = sum(1 for status, _ in tests if status == "✅")
    total_count = len(tests)
    
    print(f"\n📊 Résultat: {success_count}/{total_count} imports réussis")
    return success_count == total_count


def test_theme_manager():
    """Tester le gestionnaire de thèmes"""
    print("\n🎨 Test du gestionnaire de thèmes...")
    
    try:
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        
        theme_manager = get_theme_manager()
        
        # Test des thèmes disponibles
        themes = theme_manager.get_available_themes()
        print(f"   ✅ {len(themes)} thèmes disponibles")
        
        # Test de changement de thème
        for theme_type in [ThemeType.MODERN, ThemeType.PROFESSIONAL, ThemeType.CYBERPUNK]:
            if theme_manager.is_theme_available(theme_type):
                theme_manager.set_theme(theme_type, ThemeMode.DARK)
                current_theme, current_mode = theme_manager.get_current_theme()
                if current_theme == theme_type and current_mode == ThemeMode.DARK:
                    theme_info = theme_manager.get_theme_info(theme_type)
                    print(f"   ✅ {theme_info.get('name', theme_type.value)} appliqué")
                else:
                    print(f"   ❌ Échec application {theme_type.value}")
                    return False
            else:
                print(f"   ⚠️  {theme_type.value} non disponible")
        
        # Test des couleurs de prévisualisation
        colors = theme_manager.get_theme_preview_colors(ThemeType.CYBERPUNK, ThemeMode.DARK)
        if colors:
            print(f"   ✅ Couleurs de prévisualisation: {len(colors)} couleurs")
        
        print("   ✅ Gestionnaire de thèmes fonctionnel")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur gestionnaire de thèmes: {e}")
        traceback.print_exc()
        return False


def test_widgets():
    """Tester les widgets"""
    print("\n🧩 Test des widgets...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        
        # Créer une application temporaire si nécessaire
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test des widgets modernes
        try:
            from widgets.modern_widgets import ModernStatCard
            card = ModernStatCard("Test", "123", "Sous-titre", "primary")
            print("   ✅ ModernStatCard créé")
        except Exception as e:
            print(f"   ❌ ModernStatCard: {e}")
        
        # Test des widgets Fluent
        try:
            from widgets.fluent_components import FluentStatCard
            card = FluentStatCard("Test", "123", "🎯", "primary")
            print("   ✅ FluentStatCard créé")
        except Exception as e:
            print(f"   ❌ FluentStatCard: {e}")
        
        # Test des widgets cyberpunk
        try:
            from widgets.cyberpunk_components import CyberpunkStatCard
            card = CyberpunkStatCard("Test", "123", "Sous-titre", "primary", "⚡")
            print("   ✅ CyberpunkStatCard créé")
        except Exception as e:
            print(f"   ❌ CyberpunkStatCard: {e}")
        
        # Test du sélecteur de thèmes
        try:
            from widgets.theme_selector import ThemeSelector
            selector = ThemeSelector()
            print("   ✅ ThemeSelector créé")
        except Exception as e:
            print(f"   ❌ ThemeSelector: {e}")
        
        print("   ✅ Widgets testés avec succès")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur test widgets: {e}")
        return False


def test_stylesheets():
    """Tester les stylesheets"""
    print("\n🎨 Test des stylesheets...")
    
    try:
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        
        theme_manager = get_theme_manager()
        
        # Tester chaque thème
        themes_to_test = [
            (ThemeType.MODERN, "Moderne"),
            (ThemeType.PROFESSIONAL, "Professionnel"),
            (ThemeType.FLUENT, "Fluent"),
            (ThemeType.CYBERPUNK, "Cyberpunk"),
            (ThemeType.CLASSIC, "Classique")
        ]
        
        for theme_type, name in themes_to_test:
            if theme_manager.is_theme_available(theme_type):
                try:
                    stylesheet = theme_manager._get_theme_stylesheet(theme_type, ThemeMode.DARK)
                    if stylesheet and len(stylesheet) > 100:  # Vérifier que le stylesheet n'est pas vide
                        print(f"   ✅ Stylesheet {name}: {len(stylesheet)} caractères")
                    else:
                        print(f"   ❌ Stylesheet {name}: vide ou trop court")
                except Exception as e:
                    print(f"   ❌ Stylesheet {name}: {e}")
            else:
                print(f"   ⚠️  Thème {name} non disponible")
        
        print("   ✅ Stylesheets testés")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur test stylesheets: {e}")
        return False


def test_demos():
    """Tester les démonstrations"""
    print("\n🚀 Test des démonstrations...")
    
    try:
        # Test de la démonstration unifiée
        try:
            from views.unified_demo import UnifiedDemoWindow
            print("   ✅ Démonstration unifiée importée")
        except Exception as e:
            print(f"   ❌ Démonstration unifiée: {e}")
        
        # Test du dashboard cyberpunk
        try:
            from views.cyberpunk_dashboard import CyberpunkDashboard
            print("   ✅ Dashboard cyberpunk importé")
        except Exception as e:
            print(f"   ❌ Dashboard cyberpunk: {e}")
        
        # Test du dashboard Fluent
        try:
            from views.fluent_dashboard import FluentDashboard
            print("   ✅ Dashboard Fluent importé")
        except Exception as e:
            print(f"   ❌ Dashboard Fluent: {e}")
        
        print("   ✅ Démonstrations testées")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur test démonstrations: {e}")
        return False


def test_files_exist():
    """Vérifier que tous les fichiers existent"""
    print("\n📁 Vérification des fichiers...")
    
    required_files = [
        "src/styles/theme_manager.py",
        "src/styles/modern_theme.py",
        "src/styles/professional_theme.py",
        "src/styles/fluent_theme.py",
        "src/styles/futuristic_theme.py",
        "src/widgets/theme_selector.py",
        "src/widgets/modern_widgets.py",
        "src/widgets/fluent_components.py",
        "src/widgets/cyberpunk_components.py",
        "src/views/unified_demo.py",
        "src/views/cyberpunk_dashboard.py",
        "src/views/fluent_dashboard.py",
        "demo_launcher.py",
        "integrate_all_themes.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - MANQUANT")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  {len(missing_files)} fichiers manquants")
        return False
    else:
        print(f"\n✅ Tous les {len(required_files)} fichiers présents")
        return True


def run_all_tests():
    """Exécuter tous les tests"""
    print("🧪 TESTS D'INTÉGRATION COMPLÈTE - GSLIM")
    print("="*50)
    
    tests = [
        ("Vérification des fichiers", test_files_exist),
        ("Test des imports", test_imports),
        ("Test du gestionnaire de thèmes", test_theme_manager),
        ("Test des widgets", test_widgets),
        ("Test des stylesheets", test_stylesheets),
        ("Test des démonstrations", test_demos)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "="*50)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*50)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{status} - {test_name}")
        if result:
            success_count += 1
    
    print(f"\n📈 Score: {success_count}/{len(results)} tests réussis")
    
    if success_count == len(results):
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✨ L'intégration est complète et fonctionnelle")
        print("\n🚀 Prochaines étapes:")
        print("1. Lancer: python demo_launcher.py")
        print("2. Tester: python src/app.py")
        print("3. Profiter de votre nouvelle interface !")
    else:
        print(f"\n⚠️  {len(results) - success_count} tests échoués")
        print("💡 Vérifiez les erreurs ci-dessus")
        print("🔧 Relancez l'intégration si nécessaire")
    
    return success_count == len(results)


def main():
    """Fonction principale"""
    try:
        success = run_all_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrompus par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
