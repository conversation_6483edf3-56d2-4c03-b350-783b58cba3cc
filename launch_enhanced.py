#!/usr/bin/env python3
"""
Lanceur Interface Améliorée - GSlim
Lance l'application avec tous les modules améliorés
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QSplashScreen, QMessageBox
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

from views.integrated_dashboard import IntegratedDashboard
from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode


class EnhancedSplashScreen(QSplashScreen):
    """Écran de démarrage amélioré"""
    
    def __init__(self):
        # Créer un pixmap pour l'écran de démarrage
        pixmap = QPixmap(600, 400)
        pixmap.fill(QColor("#1a1a2e"))
        
        super().__init__(pixmap)
        self.setup_splash()
    
    def setup_splash(self):
        """Configurer l'écran de démarrage"""
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # Messages de chargement
        self.loading_messages = [
            "🔧 Initialisation des modules améliorés...",
            "🎨 Chargement des thèmes modernes...",
            "📦 Préparation des widgets avancés...",
            "🚀 Lancement de l'interface intégrée...",
            "✨ Prêt à démarrer!"
        ]
        
        self.current_message_index = 0
        
        # Timer pour les messages
        self.message_timer = QTimer()
        self.message_timer.timeout.connect(self.update_message)
        self.message_timer.start(800)
    
    def update_message(self):
        """Mettre à jour le message de chargement"""
        if self.current_message_index < len(self.loading_messages):
            message = self.loading_messages[self.current_message_index]
            self.showMessage(
                message,
                Qt.AlignBottom | Qt.AlignCenter,
                QColor("#00FFFF")
            )
            self.current_message_index += 1
        else:
            self.message_timer.stop()
            QTimer.singleShot(500, self.close)
    
    def drawContents(self, painter):
        """Dessiner le contenu de l'écran de démarrage"""
        super().drawContents(painter)
        
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Titre principal
        painter.setPen(QColor("#00FFFF"))
        font = QFont("Segoe UI", 28, QFont.Bold)
        painter.setFont(font)
        painter.drawText(self.rect(), Qt.AlignCenter, "GSLIM\nINTERFACE AMÉLIORÉE")
        
        # Version
        painter.setPen(QColor("#FF6EC7"))
        font = QFont("Segoe UI", 12)
        painter.setFont(font)
        painter.drawText(50, 350, "Version 2.0.0 - Enhanced Edition")


class EnhancedApp:
    """Application améliorée"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_app()
        self.show_splash()
    
    def setup_app(self):
        """Configurer l'application"""
        self.app.setApplicationName("GSlim Enhanced")
        self.app.setApplicationVersion("2.0.0")
        
        # Police moderne
        font = QFont("Segoe UI", 10)
        self.app.setFont(font)
        
        # Appliquer le thème moderne par défaut
        theme_manager = get_theme_manager()
        theme_manager.set_theme(ThemeType.MODERN, ThemeMode.DARK)
    
    def show_splash(self):
        """Afficher l'écran de démarrage"""
        self.splash = EnhancedSplashScreen()
        self.splash.show()
        
        # Créer la fenêtre principale après le splash
        QTimer.singleShot(4000, self.create_main_window)
    
    def create_main_window(self):
        """Créer la fenêtre principale"""
        try:
            # Simuler une instance d'application
            class MockAppInstance:
                def get_database_manager(self):
                    return None
            
            mock_app = MockAppInstance()
            
            self.main_window = IntegratedDashboard(mock_app)
            self.main_window.setWindowTitle("GSlim - Interface Améliorée")
            self.main_window.resize(1200, 800)
            
            # Connecter la fermeture du splash
            self.splash.finished.connect(self.main_window.show)
            
            # Fermer le splash
            self.splash.close()
            
        except Exception as e:
            QMessageBox.critical(None, "Erreur", f"Erreur lors du démarrage: {e}")
            sys.exit(1)
    
    def run(self):
        """Lancer l'application"""
        print("🚀 GSLIM INTERFACE AMÉLIORÉE")
        print("="*50)
        print("✨ Modules intégrés:")
        print("   📦 Articles améliorés")
        print("   🏢 Fournisseurs modernisés")
        print("   💰 Ventes optimisées")
        print("   📋 Commandes avancées")
        print("   📈 Mouvements intelligents")
        print("   📊 Rapports interactifs")
        print()
        print("🎨 Thèmes disponibles:")
        print("   ✨ Moderne (par défaut)")
        print("   💼 Professionnel")
        print("   🌊 Fluent Design")
        print("   🚀 Cyberpunk")
        print("   🎨 Classique")
        print()
        print("🎯 Fonctionnalités:")
        print("   • Interface intégrée moderne")
        print("   • Widgets avancés avec animations")
        print("   • Statistiques en temps réel")
        print("   • Actions rapides")
        print("   • Système de notifications")
        print("   • Thèmes interchangeables")
        print()
        print("🚀 Lancement de l'interface améliorée...")
        
        return self.app.exec_()


def main():
    """Fonction principale"""
    try:
        app = EnhancedApp()
        return app.run()
    except KeyboardInterrupt:
        print("\n⏹️  Application fermée par l'utilisateur")
        return 0
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
