"""
Contrôleur pour la gestion des commandes
"""

from utils.logger import setup_logger
from datetime import datetime


class OrderController:
    """Contrôleur pour la gestion des commandes"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
        
        self.logger.info("OrderController initialisé")
    
    def get_statistics(self):
        """Obtenir les statistiques des commandes"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'total_orders': 0, 'pending': 0, 'completed': 0}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'total_orders': 0, 'pending': 0, 'completed': 0}
            
            # Total des commandes
            cursor.execute("SELECT COUNT(*) FROM orders")
            result = cursor.fetchone()
            total = result[0] if result else 0
            
            # En attente
            cursor.execute("SELECT COUNT(*) FROM orders WHERE statut = 'en_attente'")
            result = cursor.fetchone()
            pending = result[0] if result else 0
            
            # Terminées
            cursor.execute("SELECT COUNT(*) FROM orders WHERE statut = 'terminée'")
            result = cursor.fetchone()
            completed = result[0] if result else 0
            
            return {
                'total_orders': total,
                'pending': pending,
                'completed': completed
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {'total_orders': 0, 'pending': 0, 'completed': 0}
    
    def get_all_orders(self):
        """Récupérer toutes les commandes"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT o.*, s.nom as supplier_name
                FROM orders o
                LEFT JOIN suppliers s ON o.supplier_id = s.id
                ORDER BY o.date_commande DESC
            """)
            
            orders = cursor.fetchall()
            if orders:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in orders]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des commandes: {e}")
            return []
    
    def create_order(self, supplier_id, total=0.0):
        """Créer une nouvelle commande"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return None
            
            cursor = self.db_manager.cursor
            if not cursor:
                return None
            
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO orders (supplier_id, statut, date_commande, total)
                VALUES (?, 'en_attente', ?, ?)
            """, (supplier_id, now, total))
            
            self.db_manager.connection.commit()
            return cursor.lastrowid
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de la commande: {e}")
            return None
    
    def update_order_status(self, order_id, status):
        """Mettre à jour le statut d'une commande"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return False
            
            cursor = self.db_manager.cursor
            if not cursor:
                return False
            
            cursor.execute("""
                UPDATE orders SET statut = ? WHERE id = ?
            """, (status, order_id))
            
            self.db_manager.connection.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du statut: {e}")
            return False
