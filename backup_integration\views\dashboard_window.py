"""
Fenêtre du tableau de bord
Interface principale avec statistiques, graphiques et indicateurs clés
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGridLayout, QScrollArea, QSizePolicy, QProgressBar,
    QTableWidget, QTableWidgetItem, QHeaderView, QSpacerItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

try:
    from qfluentwidgets import (
        CardWidget, TitleLabel, CaptionLabel, PushButton,
        FluentIcon, InfoBar, InfoBarPosition, ProgressBar
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from controllers.article_controller import ArticleController
from controllers.supplier_controller import SupplierController
from controllers.stock_movement_controller import StockMovementController
from controllers.order_controller import OrderController
from controllers.report_controller import ReportController
from widgets.modern_widgets import (
    ModernStatCard, ModernActionCard, ModernProgressCard, ModernAlertCard
)
from styles.modern_theme import ModernTheme
from utils.logger import setup_logger


class DashboardWindow(QWidget):
    """Fenêtre du tableau de bord principal"""
    
    # Signaux
    navigate_to_articles = pyqtSignal()
    navigate_to_suppliers = pyqtSignal()
    navigate_to_movements = pyqtSignal()
    navigate_to_orders = pyqtSignal()
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.logger = setup_logger(__name__)
        
        # Contrôleurs
        self.article_controller = ArticleController(db_manager)
        self.supplier_controller = SupplierController(db_manager)
        self.movement_controller = StockMovementController(db_manager)
        self.order_controller = OrderController(db_manager)
        self.report_controller = ReportController(db_manager)
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_data)
        self.refresh_timer.start(300000)  # Actualiser toutes les 5 minutes

        # Initialiser les données
        self._init_data()

        self._init_ui()
        self._load_data()
        
        self.logger.info("DashboardWindow initialisé")

    def _init_data(self):
        """Initialiser les données par défaut"""
        # Données par défaut pour éviter les erreurs
        self.stats_data = {
            'total_articles': 0,
            'out_of_stock': 0,
            'low_stock': 0,
            'total_suppliers': 0,
            'active_suppliers': 0,
            'total_value': 0,
            'pending_orders': 0,
            'overdue_orders': 0,
            'average_rating': 0
        }

        # Liste des mouvements récents
        self.recent_movements = []

        # Charger les vraies données
        try:
            # Statistiques des articles
            article_stats = self.article_controller.get_stock_statistics()
            self.stats_data.update(article_stats)

            # Statistiques des fournisseurs
            supplier_stats = self.supplier_controller.get_supplier_statistics()
            self.stats_data.update(supplier_stats)

            # Statistiques des commandes
            order_stats = self.order_controller.get_order_statistics()
            self.stats_data.update(order_stats)

            # Mouvements récents
            self.recent_movements = self.movement_controller.get_recent_movements(20)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des données: {e}")

    def _init_ui(self):
        """Initialiser l'interface utilisateur moderne"""
        # Appliquer le thème moderne
        self.setStyleSheet(ModernTheme.get_modern_stylesheet())

        layout = QVBoxLayout()
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(24)

        # En-tête moderne
        self._create_modern_header(layout)

        # Zone de contenu avec scroll moderne
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setStyleSheet("QScrollArea { border: none; background: transparent; }")

        content_widget = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(32)

        # Sections modernes
        self._create_modern_stats_grid(content_layout)
        self._create_modern_alerts_section(content_layout)
        self._create_modern_overview_section(content_layout)
        self._create_modern_quick_actions(content_layout)

        # Espacement en bas
        content_layout.addStretch()

        content_widget.setLayout(content_layout)
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)

        self.setLayout(layout)
    
    def _create_modern_header(self, layout):
        """Créer l'en-tête moderne du tableau de bord"""
        header_widget = QWidget()
        header_widget.setProperty("class", "card")
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(24, 20, 24, 20)

        # Section titre avec informations
        title_section = QVBoxLayout()
        title_section.setSpacing(4)

        # Titre principal
        title = QLabel("Tableau de Bord")
        title.setProperty("class", "title")
        title_section.addWidget(title)

        # Sous-titre avec date/heure
        from datetime import datetime
        subtitle = QLabel(f"Dernière mise à jour: {datetime.now().strftime('%d/%m/%Y à %H:%M')}")
        subtitle.setProperty("class", "subtitle")
        subtitle.setStyleSheet("color: #64748b; font-size: 14px;")
        title_section.addWidget(subtitle)

        header_layout.addLayout(title_section)
        header_layout.addStretch()

        # Boutons d'action
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(12)

        # Bouton d'actualisation
        if FLUENT_AVAILABLE:
            self.refresh_button = PushButton("Actualiser")
            self.refresh_button.setIcon(FluentIcon.SYNC)
        else:
            self.refresh_button = QPushButton("Actualiser")
            self.refresh_button.setProperty("class", "secondary")

        self.refresh_button.clicked.connect(self._refresh_data)
        actions_layout.addWidget(self.refresh_button)

        # Bouton de rapport rapide
        if FLUENT_AVAILABLE:
            self.quick_report_button = PushButton("Rapport Rapide")
            self.quick_report_button.setIcon(FluentIcon.DOCUMENT)
        else:
            self.quick_report_button = QPushButton("Rapport Rapide")

        self.quick_report_button.clicked.connect(self._generate_quick_report)
        actions_layout.addWidget(self.quick_report_button)

        header_layout.addLayout(actions_layout)
        header_widget.setLayout(header_layout)
        layout.addWidget(header_widget)

    def _create_modern_stats_grid(self, layout):
        """Créer la grille moderne des statistiques"""
        # Titre de section
        section_title = QLabel("Vue d'ensemble")
        section_title.setProperty("class", "subtitle")
        section_title.setStyleSheet("font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 16px;")
        layout.addWidget(section_title)

        # Grille des cartes de statistiques
        stats_grid = QGridLayout()
        stats_grid.setSpacing(20)

        # Carte Articles
        articles_card = ModernStatCard(
            title="Articles",
            value=str(self.stats_data.get('total_articles', 0)),
            subtitle=f"{self.stats_data.get('out_of_stock', 0)} en rupture",
            card_type="primary",
            icon="PACKAGE" if FLUENT_AVAILABLE else None
        )
        articles_card.clicked.connect(self.navigate_to_articles.emit)
        stats_grid.addWidget(articles_card, 0, 0)

        # Carte Fournisseurs
        suppliers_card = ModernStatCard(
            title="Fournisseurs",
            value=str(self.stats_data.get('total_suppliers', 0)),
            subtitle=f"{self.stats_data.get('active_suppliers', 0)} actifs",
            card_type="success",
            icon="PEOPLE" if FLUENT_AVAILABLE else None
        )
        suppliers_card.clicked.connect(self.navigate_to_suppliers.emit)
        stats_grid.addWidget(suppliers_card, 0, 1)

        # Carte Valeur du Stock
        stock_value = self.stats_data.get('total_value', 0)
        stock_card = ModernStatCard(
            title="Valeur du Stock",
            value=f"{stock_value:,.0f} €",
            subtitle="Valeur totale",
            card_type="warning",
            icon="MONEY" if FLUENT_AVAILABLE else None
        )
        stats_grid.addWidget(stock_card, 0, 2)

        # Carte Commandes
        orders_card = ModernStatCard(
            title="Commandes",
            value=str(self.stats_data.get('pending_orders', 0)),
            subtitle="En attente",
            card_type="error" if self.stats_data.get('overdue_orders', 0) > 0 else "default",
            icon="SHOPPING_CART" if FLUENT_AVAILABLE else None
        )
        orders_card.clicked.connect(self.navigate_to_orders.emit)
        stats_grid.addWidget(orders_card, 0, 3)

        # Cartes de progression
        # Stock bas
        low_stock_count = self.stats_data.get('low_stock', 0)
        total_articles = self.stats_data.get('total_articles', 1)
        low_stock_progress = ModernProgressCard(
            title="Articles en Stock Bas",
            current=low_stock_count,
            total=total_articles,
            color="#f59e0b"
        )
        stats_grid.addWidget(low_stock_progress, 1, 0)

        # Mouvements récents
        recent_movements = len(self.recent_movements)
        movements_progress = ModernProgressCard(
            title="Mouvements Récents",
            current=recent_movements,
            total=50,  # Limite affichée
            color="#06b6d4"
        )
        stats_grid.addWidget(movements_progress, 1, 1)

        # Commandes en retard
        overdue_orders = self.stats_data.get('overdue_orders', 0)
        pending_orders = self.stats_data.get('pending_orders', 1)
        overdue_progress = ModernProgressCard(
            title="Commandes en Retard",
            current=overdue_orders,
            total=pending_orders,
            color="#ef4444"
        )
        stats_grid.addWidget(overdue_progress, 1, 2)

        # Taux de satisfaction fournisseurs
        avg_rating = self.stats_data.get('average_rating', 0)
        rating_progress = ModernProgressCard(
            title="Satisfaction Fournisseurs",
            current=int(avg_rating * 20) if avg_rating else 0,  # Convertir sur 100
            total=100,
            color="#10b981"
        )
        stats_grid.addWidget(rating_progress, 1, 3)

        layout.addLayout(stats_grid)

    def _create_modern_alerts_section(self, layout):
        """Créer la section moderne des alertes"""
        # Titre de section
        section_title = QLabel("Alertes et Notifications")
        section_title.setProperty("class", "subtitle")
        section_title.setStyleSheet("font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 16px;")
        layout.addWidget(section_title)

        # Container des alertes
        alerts_container = QHBoxLayout()
        alerts_container.setSpacing(16)

        # Alerte stock bas
        low_stock_count = self.stats_data.get('low_stock', 0)
        if low_stock_count > 0:
            low_stock_alert = ModernAlertCard(
                title="Stock Bas Détecté",
                message=f"{low_stock_count} article(s) ont un stock inférieur au seuil minimum. Vérifiez vos approvisionnements.",
                alert_type="warning"
            )
            alerts_container.addWidget(low_stock_alert)

        # Alerte rupture de stock
        out_of_stock_count = self.stats_data.get('out_of_stock', 0)
        if out_of_stock_count > 0:
            out_of_stock_alert = ModernAlertCard(
                title="Ruptures de Stock",
                message=f"{out_of_stock_count} article(s) sont en rupture de stock. Action immédiate requise.",
                alert_type="error"
            )
            alerts_container.addWidget(out_of_stock_alert)

        # Alerte commandes en retard
        overdue_orders = self.stats_data.get('overdue_orders', 0)
        if overdue_orders > 0:
            overdue_alert = ModernAlertCard(
                title="Commandes en Retard",
                message=f"{overdue_orders} commande(s) sont en retard. Contactez vos fournisseurs.",
                alert_type="warning"
            )
            alerts_container.addWidget(overdue_alert)

        # Message si aucune alerte
        if low_stock_count == 0 and out_of_stock_count == 0 and overdue_orders == 0:
            success_alert = ModernAlertCard(
                title="Tout va bien !",
                message="Aucune alerte critique. Votre stock est bien géré.",
                alert_type="success"
            )
            alerts_container.addWidget(success_alert)

        # Ajouter un stretch pour équilibrer
        alerts_container.addStretch()

        layout.addLayout(alerts_container)

    def _create_modern_overview_section(self, layout):
        """Créer la section moderne de vue d'ensemble"""
        # Titre de section
        section_title = QLabel("Activités Récentes")
        section_title.setProperty("class", "subtitle")
        section_title.setStyleSheet("font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 16px;")
        layout.addWidget(section_title)

        # Container pour les activités
        overview_container = QHBoxLayout()
        overview_container.setSpacing(20)

        # Carte des mouvements récents
        movements_actions = [
            {
                'text': 'Voir tous les mouvements',
                'callback': self.navigate_to_movements.emit,
                'style': 'secondary'
            }
        ]

        movements_description = f"Derniers mouvements de stock enregistrés"
        if self.recent_movements:
            movements_description += f"\n• {len(self.recent_movements)} mouvements récents"

        movements_card = ModernActionCard(
            title="Mouvements de Stock",
            description=movements_description,
            actions=movements_actions
        )
        overview_container.addWidget(movements_card)

        # Carte des fournisseurs top
        suppliers_actions = [
            {
                'text': 'Gérer les fournisseurs',
                'callback': self.navigate_to_suppliers.emit,
                'style': 'secondary'
            }
        ]

        avg_rating = self.stats_data.get('average_rating', 0)
        suppliers_description = f"Gestion de vos partenaires fournisseurs"
        if avg_rating > 0:
            suppliers_description += f"\n• Note moyenne: {avg_rating:.1f}/5"

        suppliers_card = ModernActionCard(
            title="Fournisseurs",
            description=suppliers_description,
            actions=suppliers_actions
        )
        overview_container.addWidget(suppliers_card)

        layout.addLayout(overview_container)

    def _create_modern_quick_actions(self, layout):
        """Créer la section moderne des actions rapides"""
        # Titre de section
        section_title = QLabel("Actions Rapides")
        section_title.setProperty("class", "subtitle")
        section_title.setStyleSheet("font-size: 20px; font-weight: 600; color: #1e293b; margin-bottom: 16px;")
        layout.addWidget(section_title)

        # Container des actions
        actions_container = QHBoxLayout()
        actions_container.setSpacing(20)

        # Carte Nouvel Article
        new_article_actions = [
            {
                'text': 'Ajouter un article',
                'callback': lambda: self.navigate_to_articles.emit(),
                'style': 'success'
            }
        ]

        new_article_card = ModernActionCard(
            title="Nouvel Article",
            description="Ajouter rapidement un nouvel article à votre inventaire avec toutes ses informations.",
            actions=new_article_actions
        )
        actions_container.addWidget(new_article_card)

        # Carte Mouvement Rapide
        quick_movement_actions = [
            {
                'text': 'Enregistrer un mouvement',
                'callback': lambda: self.navigate_to_movements.emit(),
                'style': 'primary'
            }
        ]

        quick_movement_card = ModernActionCard(
            title="Mouvement Rapide",
            description="Enregistrer rapidement une entrée ou sortie de stock pour mettre à jour vos quantités.",
            actions=quick_movement_actions
        )
        actions_container.addWidget(quick_movement_card)

        # Carte Nouvelle Commande
        new_order_actions = [
            {
                'text': 'Créer une commande',
                'callback': lambda: self.navigate_to_orders.emit(),
                'style': 'warning'
            }
        ]

        new_order_card = ModernActionCard(
            title="Nouvelle Commande",
            description="Créer une nouvelle commande fournisseur pour réapprovisionner votre stock.",
            actions=new_order_actions
        )
        actions_container.addWidget(new_order_card)

        layout.addLayout(actions_container)

    def _generate_quick_report(self):
        """Générer un rapport rapide"""
        try:
            # Utiliser le contrôleur de rapports pour générer un rapport de stock
            self.report_controller.generate_stock_report()

            # Afficher un message de succès (à implémenter avec une notification)
            self.logger.info("Rapport rapide généré avec succès")

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport rapide: {e}")
    
    def _create_stats_cards(self, layout):
        """Créer les cartes de statistiques principales"""
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        
        # Initialiser les cartes vides
        self.total_articles_card = self._create_stat_card("Articles", "0", "#0078d4")
        self.low_stock_card = self._create_stat_card("Stock bas", "0", "#fd7e14")
        self.out_of_stock_card = self._create_stat_card("Ruptures", "0", "#dc3545")
        self.total_value_card = self._create_stat_card("Valeur totale", "0 €", "#28a745")
        self.suppliers_card = self._create_stat_card("Fournisseurs", "0", "#6f42c1")
        self.pending_orders_card = self._create_stat_card("Commandes en attente", "0", "#fd7e14")
        self.recent_movements_card = self._create_stat_card("Mouvements (7j)", "0", "#17a2b8")
        self.overdue_orders_card = self._create_stat_card("Commandes en retard", "0", "#dc3545")
        
        # Disposition en grille 4x2
        stats_layout.addWidget(self.total_articles_card, 0, 0)
        stats_layout.addWidget(self.low_stock_card, 0, 1)
        stats_layout.addWidget(self.out_of_stock_card, 0, 2)
        stats_layout.addWidget(self.total_value_card, 0, 3)
        stats_layout.addWidget(self.suppliers_card, 1, 0)
        stats_layout.addWidget(self.pending_orders_card, 1, 1)
        stats_layout.addWidget(self.recent_movements_card, 1, 2)
        stats_layout.addWidget(self.overdue_orders_card, 1, 3)
        
        layout.addLayout(stats_layout)
    
    def _create_stat_card(self, title: str, value: str, color: str):
        """Créer une carte de statistique"""
        if FLUENT_AVAILABLE:
            card = CardWidget()
        else:
            card = QFrame()
            card.setProperty("class", "stat-card")
            card.setFrameStyle(QFrame.Box)
        
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(15, 15, 15, 15)
        card_layout.setSpacing(5)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = CaptionLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setProperty("class", "stat-title")
        
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setProperty("class", "stat-value")
        
        # Style de la valeur
        font = QFont("Segoe UI", 18, QFont.Bold)
        value_label.setFont(font)
        value_label.setStyleSheet(f"color: {color};")
        
        card_layout.addWidget(value_label)
        
        card.setLayout(card_layout)
        card.setFixedHeight(100)
        
        # Stocker la référence au label de valeur pour mise à jour
        card.value_label = value_label
        
        return card
    
    def _create_alerts_section(self, layout):
        """Créer la section des alertes"""
        # Titre de section
        if FLUENT_AVAILABLE:
            alerts_title = TitleLabel("Alertes et Notifications")
        else:
            alerts_title = QLabel("Alertes et Notifications")
            alerts_title.setProperty("class", "section-title")
            font = QFont("Segoe UI", 16, QFont.Bold)
            alerts_title.setFont(font)
        
        layout.addWidget(alerts_title)
        
        # Container pour les alertes
        if FLUENT_AVAILABLE:
            self.alerts_container = CardWidget()
        else:
            self.alerts_container = QFrame()
            self.alerts_container.setProperty("class", "alerts-container")
            self.alerts_container.setFrameStyle(QFrame.Box)
        
        self.alerts_layout = QVBoxLayout()
        self.alerts_layout.setContentsMargins(15, 15, 15, 15)
        self.alerts_container.setLayout(self.alerts_layout)
        
        layout.addWidget(self.alerts_container)
    
    def _create_recent_activities(self, layout):
        """Créer la section des activités récentes"""
        # Titre de section
        if FLUENT_AVAILABLE:
            activities_title = TitleLabel("Activités Récentes")
        else:
            activities_title = QLabel("Activités Récentes")
            activities_title.setProperty("class", "section-title")
            font = QFont("Segoe UI", 16, QFont.Bold)
            activities_title.setFont(font)
        
        layout.addWidget(activities_title)
        
        # Table des activités récentes
        self.activities_table = QTableWidget()
        self.activities_table.setColumnCount(4)
        self.activities_table.setHorizontalHeaderLabels(["Date", "Type", "Article", "Quantité"])
        
        # Configuration de la table
        header = self.activities_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)
        
        self.activities_table.setAlternatingRowColors(True)
        self.activities_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.activities_table.setMaximumHeight(200)
        
        layout.addWidget(self.activities_table)
    
    def _create_quick_actions(self, layout):
        """Créer la section des actions rapides"""
        # Titre de section
        if FLUENT_AVAILABLE:
            actions_title = TitleLabel("Actions Rapides")
        else:
            actions_title = QLabel("Actions Rapides")
            actions_title.setProperty("class", "section-title")
            font = QFont("Segoe UI", 16, QFont.Bold)
            actions_title.setFont(font)
        
        layout.addWidget(actions_title)
        
        # Boutons d'actions rapides
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(15)
        
        if FLUENT_AVAILABLE:
            # Bouton Nouvel Article
            new_article_btn = PushButton("Nouvel Article")
            new_article_btn.setIcon(FluentIcon.ADD)
            
            # Bouton Nouveau Fournisseur
            new_supplier_btn = PushButton("Nouveau Fournisseur")
            new_supplier_btn.setIcon(FluentIcon.PEOPLE)
            
            # Bouton Nouvelle Commande
            new_order_btn = PushButton("Nouvelle Commande")
            new_order_btn.setIcon(FluentIcon.SHOPPING_CART)
            
            # Bouton Générer Rapport
            generate_report_btn = PushButton("Générer Rapport")
            generate_report_btn.setIcon(FluentIcon.DOCUMENT)
        else:
            new_article_btn = QPushButton("Nouvel Article")
            new_supplier_btn = QPushButton("Nouveau Fournisseur")
            new_order_btn = QPushButton("Nouvelle Commande")
            generate_report_btn = QPushButton("Générer Rapport")
        
        # Connecter les signaux
        new_article_btn.clicked.connect(self.navigate_to_articles.emit)
        new_supplier_btn.clicked.connect(self.navigate_to_suppliers.emit)
        new_order_btn.clicked.connect(self.navigate_to_orders.emit)
        generate_report_btn.clicked.connect(self._generate_quick_report)
        
        actions_layout.addWidget(new_article_btn)
        actions_layout.addWidget(new_supplier_btn)
        actions_layout.addWidget(new_order_btn)
        actions_layout.addWidget(generate_report_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
    
    def _load_data(self):
        """Charger toutes les données du tableau de bord"""
        try:
            # Statistiques des articles
            article_stats = self.article_controller.get_stock_statistics()
            self.total_articles_card.value_label.setText(str(article_stats.get('total_articles', 0)))
            self.low_stock_card.value_label.setText(str(article_stats.get('low_stock', 0)))
            self.out_of_stock_card.value_label.setText(str(article_stats.get('out_of_stock', 0)))
            self.total_value_card.value_label.setText(f"{article_stats.get('total_value', 0):.0f} €")
            
            # Statistiques des fournisseurs
            supplier_stats = self.supplier_controller.get_supplier_statistics()
            self.suppliers_card.value_label.setText(str(supplier_stats.get('total_suppliers', 0)))
            
            # Statistiques des commandes
            order_stats = self.order_controller.get_order_statistics()
            self.pending_orders_card.value_label.setText(str(order_stats.get('pending_orders', 0)))
            self.overdue_orders_card.value_label.setText(str(order_stats.get('overdue_orders', 0)))
            
            # Statistiques des mouvements
            movement_stats = self.movement_controller.get_movement_statistics()
            self.recent_movements_card.value_label.setText(str(movement_stats.get('week_movements', 0)))
            
            # Charger les alertes
            self._load_alerts()
            
            # Charger les activités récentes
            self._load_recent_activities()
            
            self.logger.info("Données du tableau de bord chargées")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des données: {e}")
    
    def _load_alerts(self):
        """Charger et afficher les alertes"""
        try:
            # Nettoyer les alertes existantes
            for i in reversed(range(self.alerts_layout.count())):
                child = self.alerts_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
            
            alerts = []
            
            # Alertes de stock bas
            article_stats = self.article_controller.get_stock_statistics()
            if article_stats.get('low_stock', 0) > 0:
                alerts.append({
                    'type': 'warning',
                    'message': f"{article_stats['low_stock']} article(s) en stock bas",
                    'action': 'Voir les articles'
                })
            
            # Alertes de rupture
            if article_stats.get('out_of_stock', 0) > 0:
                alerts.append({
                    'type': 'error',
                    'message': f"{article_stats['out_of_stock']} article(s) en rupture de stock",
                    'action': 'Voir les articles'
                })
            
            # Alertes de commandes en retard
            order_stats = self.order_controller.get_order_statistics()
            if order_stats.get('overdue_orders', 0) > 0:
                alerts.append({
                    'type': 'error',
                    'message': f"{order_stats['overdue_orders']} commande(s) en retard",
                    'action': 'Voir les commandes'
                })
            
            # Afficher les alertes
            if alerts:
                for alert in alerts:
                    alert_widget = self._create_alert_widget(alert)
                    self.alerts_layout.addWidget(alert_widget)
            else:
                no_alerts_label = QLabel("Aucune alerte")
                no_alerts_label.setAlignment(Qt.AlignCenter)
                no_alerts_label.setProperty("class", "no-alerts")
                self.alerts_layout.addWidget(no_alerts_label)
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des alertes: {e}")
    
    def _create_alert_widget(self, alert: dict):
        """Créer un widget d'alerte"""
        alert_frame = QFrame()
        alert_frame.setProperty("class", f"alert-{alert['type']}")
        
        alert_layout = QHBoxLayout()
        alert_layout.setContentsMargins(10, 5, 10, 5)
        
        # Message
        message_label = QLabel(alert['message'])
        alert_layout.addWidget(message_label)
        
        alert_layout.addStretch()
        
        # Bouton d'action
        if FLUENT_AVAILABLE:
            action_btn = PushButton(alert['action'])
        else:
            action_btn = QPushButton(alert['action'])
        
        action_btn.clicked.connect(lambda: self._handle_alert_action(alert))
        alert_layout.addWidget(action_btn)
        
        alert_frame.setLayout(alert_layout)
        return alert_frame
    
    def _handle_alert_action(self, alert: dict):
        """Gérer l'action d'une alerte"""
        if "article" in alert['message'].lower():
            self.navigate_to_articles.emit()
        elif "commande" in alert['message'].lower():
            self.navigate_to_orders.emit()
    
    def _load_recent_activities(self):
        """Charger les activités récentes"""
        try:
            # Récupérer les mouvements récents
            recent_movements = self.movement_controller.get_recent_movements(10)
            
            self.activities_table.setRowCount(len(recent_movements))
            
            for row, movement in enumerate(recent_movements):
                # Date
                movement_date = movement.get('movement_date', '')
                if movement_date:
                    try:
                        from datetime import datetime
                        date_obj = datetime.fromisoformat(movement_date.replace('Z', '+00:00'))
                        formatted_date = date_obj.strftime('%d/%m %H:%M')
                    except:
                        formatted_date = movement_date[:16]
                else:
                    formatted_date = ''
                
                self.activities_table.setItem(row, 0, QTableWidgetItem(formatted_date))
                self.activities_table.setItem(row, 1, QTableWidgetItem(movement.get('movement_type', '')))
                self.activities_table.setItem(row, 2, QTableWidgetItem(movement.get('article_name', '')))
                self.activities_table.setItem(row, 3, QTableWidgetItem(str(movement.get('quantity', 0))))
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des activités: {e}")
    
    def _refresh_data(self):
        """Actualiser toutes les données"""
        self._load_data()
        if FLUENT_AVAILABLE:
            InfoBar.success("Actualisation", "Données mises à jour", parent=self)
    
    def _generate_quick_report(self):
        """Générer un rapport rapide"""
        try:
            result = self.report_controller.generate_stock_report("pdf", True)
            if result.success:
                if FLUENT_AVAILABLE:
                    InfoBar.success("Rapport", f"Rapport généré: {result.data}", parent=self)
                else:
                    self.logger.info(f"Rapport généré: {result.data}")
            else:
                if FLUENT_AVAILABLE:
                    InfoBar.error("Erreur", result.message, parent=self)
                else:
                    self.logger.error(f"Erreur rapport: {result.message}")
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport: {e}")
