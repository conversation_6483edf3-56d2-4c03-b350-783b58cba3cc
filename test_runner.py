#!/usr/bin/env python3
"""
Système de Tests pour GSlim
Tests automatisés pour valider toutes les fonctionnalités
"""

import sys
import os
import unittest
from PyQt5.QtWidgets import QApplication

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class TestImports(unittest.TestCase):
    """Tests des imports"""
    
    def test_theme_manager_import(self):
        """Tester l'import du gestionnaire de thèmes"""
        try:
            from styles.theme_manager import get_theme_manager
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Import du gestionnaire de thèmes échoué: {e}")
    
    def test_widgets_import(self):
        """Tester l'import des widgets"""
        try:
            from widgets.enhanced_widgets import EnhancedCard
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Import des widgets échoué: {e}")
    
    def test_views_import(self):
        """Tester l'import des vues"""
        try:
            from views.integrated_dashboard import IntegratedDashboard
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Import des vues échoué: {e}")


class TestThemeManager(unittest.TestCase):
    """Tests du gestionnaire de thèmes"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_theme_manager_creation(self):
        """Tester la création du gestionnaire de thèmes"""
        from styles.theme_manager import get_theme_manager
        theme_manager = get_theme_manager()
        self.assertIsNotNone(theme_manager)
    
    def test_available_themes(self):
        """Tester les thèmes disponibles"""
        from styles.theme_manager import get_theme_manager
        theme_manager = get_theme_manager()
        themes = theme_manager.get_available_themes()
        self.assertGreater(len(themes), 0)


class TestWidgets(unittest.TestCase):
    """Tests des widgets"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_enhanced_card_creation(self):
        """Tester la création d'une carte améliorée"""
        from widgets.enhanced_widgets import EnhancedCard
        card = EnhancedCard("Test", None)
        self.assertIsNotNone(card)
        self.assertEqual(card.title, "Test")


def run_tests():
    """Exécuter tous les tests"""
    print("🧪 SYSTÈME DE TESTS GSLIM")
    print("="*40)
    
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les tests
    suite.addTests(loader.loadTestsFromTestCase(TestImports))
    suite.addTests(loader.loadTestsFromTestCase(TestThemeManager))
    suite.addTests(loader.loadTestsFromTestCase(TestWidgets))
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Résumé
    print("\n" + "="*40)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*40)
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        return True
    else:
        print("\n❌ Certains tests ont échoué")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
