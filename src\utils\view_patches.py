"""
Patch pour les méthodes manquantes dans les vues
"""

def patch_orders_window(orders_window_class):
    """Ajouter les méthodes manquantes à OrdersWindow"""
    
    def _on_order_selected(self, order_id=None):
        """Méthode de sélection de commande"""
        try:
            if order_id:
                self.logger.info(f"Commande sélectionnée: {order_id}")
                # Logique de sélection de commande
            else:
                self.logger.info("Aucune commande sélectionnée")
        except Exception as e:
            self.logger.error(f"Erreur sélection commande: {e}")
    
    def _on_order_double_clicked(self, order_id=None):
        """Méthode de double-clic sur commande"""
        try:
            if order_id:
                self.logger.info(f"Double-clic sur commande: {order_id}")
                # Logique de double-clic
        except Exception as e:
            self.logger.error(f"Erreur double-clic commande: {e}")
    
    # Ajouter les méthodes à la classe
    if not hasattr(orders_window_class, '_on_order_selected'):
        orders_window_class._on_order_selected = _on_order_selected
    
    if not hasattr(orders_window_class, '_on_order_double_clicked'):
        orders_window_class._on_order_double_clicked = _on_order_double_clicked
    
    return orders_window_class


def patch_reports_window(reports_window_class):
    """Ajouter les méthodes manquantes à ReportsWindow"""
    
    def get_chart_icon(self):
        """Obtenir l'icône de graphique"""
        try:
            # Utiliser une icône alternative si FluentIcon.CHART n'existe pas
            from qfluentwidgets import FluentIcon
            
            # Essayer différentes icônes
            chart_icons = ['CHART', 'PIE_CHART', 'BAR_CHART', 'GRAPH', 'ANALYTICS']
            
            for icon_name in chart_icons:
                if hasattr(FluentIcon, icon_name):
                    return getattr(FluentIcon, icon_name)
            
            # Icône par défaut
            return FluentIcon.DOCUMENT if hasattr(FluentIcon, 'DOCUMENT') else None
            
        except Exception as e:
            print(f"Erreur icône graphique: {e}")
            return None
    
    # Ajouter la méthode à la classe
    if not hasattr(reports_window_class, 'get_chart_icon'):
        reports_window_class.get_chart_icon = get_chart_icon
    
    return reports_window_class


def apply_view_patches():
    """Appliquer les patches aux vues"""
    try:
        # Importer et patcher les vues si elles existent
        patch_applied = False
        
        try:
            from views.orders_window import OrdersWindow
            OrdersWindow = patch_orders_window(OrdersWindow)
            patch_applied = True
            print("✅ Patch OrdersWindow appliqué")
        except ImportError:
            print("⚠️  OrdersWindow non trouvée")
        
        try:
            from views.reports_window import ReportsWindow
            ReportsWindow = patch_reports_window(ReportsWindow)
            patch_applied = True
            print("✅ Patch ReportsWindow appliqué")
        except ImportError:
            print("⚠️  ReportsWindow non trouvée")
        
        return patch_applied
        
    except Exception as e:
        print(f"❌ Erreur application patches: {e}")
        return False
