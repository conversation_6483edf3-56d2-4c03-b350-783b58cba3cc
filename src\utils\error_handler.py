"""
Gestionnaire d'erreurs global pour GSlim
"""

import sys
import traceback
from PyQt5.QtWidgets import QMessageBox, QApplication
from utils.logger import setup_logger


class ErrorHandler:
    """Gestionnaire d'erreurs global"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """Gérer les exceptions non capturées"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Logger l'erreur
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        self.logger.error(f"Exception non capturée: {error_msg}")
        
        # Afficher un message à l'utilisateur
        app = QApplication.instance()
        if app:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Une erreur inattendue s'est produite.")
            msg_box.setDetailedText(error_msg)
            msg_box.exec_()
    
    def install(self):
        """Installer le gestionnaire d'erreurs"""
        sys.excepthook = self.handle_exception


# Instance globale
error_handler = ErrorHandler()


def install_error_handler():
    """Installer le gestionnaire d'erreurs global"""
    error_handler.install()
