#!/usr/bin/env python3
"""
Script d'intégration des améliorations UI dans l'application principale
Applique progressivement les nouveaux composants et styles
"""

import sys
import os
import shutil
from pathlib import Path

def backup_original_files():
    """Créer une sauvegarde des fichiers originaux"""
    print("📦 Création de sauvegardes...")
    
    backup_dir = Path("backup_ui_original")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "src/views/main_window.py",
        "src/views/dashboard_window.py",
        "src/styles/themes.py",
        "src/app.py"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = backup_dir / Path(file_path).name
            shutil.copy2(file_path, backup_path)
            print(f"✅ Sauvegarde: {file_path} -> {backup_path}")
        else:
            print(f"⚠️  Fichier non trouvé: {file_path}")
    
    print(f"📁 Sauvegardes créées dans: {backup_dir}")

def update_main_app():
    """Mettre à jour l'application principale"""
    print("\n🔄 Mise à jour de l'application principale...")
    
    app_file = "src/app.py"
    if not os.path.exists(app_file):
        print(f"❌ Fichier {app_file} non trouvé")
        return False
    
    # Lire le contenu actuel
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter l'import du nouveau thème si pas déjà présent
    if "from styles.modern_theme import ModernTheme" not in content:
        # Trouver la ligne d'import des styles
        lines = content.split('\n')
        insert_index = -1
        
        for i, line in enumerate(lines):
            if "from styles.themes import" in line:
                insert_index = i + 1
                break
        
        if insert_index > 0:
            lines.insert(insert_index, "from styles.modern_theme import ModernTheme")
            content = '\n'.join(lines)
            print("✅ Import ModernTheme ajouté")
    
    # Ajouter l'application du thème moderne
    if "ModernTheme.get_modern_stylesheet()" not in content:
        # Chercher où appliquer le thème
        if "setStyleSheet" in content:
            content = content.replace(
                "theme_manager.get_current_theme()",
                "ModernTheme.get_modern_stylesheet()"
            )
            print("✅ Thème moderne appliqué")
    
    # Écrire le fichier mis à jour
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def create_integration_example():
    """Créer un exemple d'intégration"""
    print("\n📝 Création d'un exemple d'intégration...")
    
    example_content = '''"""
Exemple d'intégration des améliorations UI dans une fenêtre existante
"""

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout
from PyQt5.QtCore import pyqtSignal

# Imports des nouveaux composants
from widgets.modern_widgets import ModernStatCard
from widgets.advanced_ui import NotificationToast
from widgets.modern_navigation import ModernNavigationPanel
from styles.modern_theme import ModernTheme

class ModernizedWindow(QWidget):
    """Exemple de fenêtre modernisée"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Configurer l'interface avec les nouveaux composants"""
        # Appliquer le thème moderne
        self.setStyleSheet(ModernTheme.get_modern_stylesheet())
        
        layout = QHBoxLayout()
        
        # Navigation moderne
        self.navigation = ModernNavigationPanel()
        self.navigation.add_navigation_item("home", "Accueil")
        self.navigation.add_navigation_item("data", "Données")
        self.navigation.page_changed.connect(self.on_page_changed)
        layout.addWidget(self.navigation)
        
        # Zone de contenu
        content_layout = QVBoxLayout()
        
        # Cartes de statistiques modernes
        stats_layout = QHBoxLayout()
        
        card1 = ModernStatCard("Total", "1,234", "↗ +5%", "primary")
        card1.clicked.connect(lambda: self.show_notification("info", "Carte", "Carte cliquée"))
        stats_layout.addWidget(card1)
        
        card2 = ModernStatCard("Actifs", "89", "✓ Tous OK", "success")
        stats_layout.addWidget(card2)
        
        content_layout.addLayout(stats_layout)
        layout.addLayout(content_layout)
        
        self.setLayout(layout)
    
    def on_page_changed(self, route_key: str):
        """Gérer le changement de page"""
        self.show_notification("info", "Navigation", f"Page: {route_key}")
    
    def show_notification(self, toast_type: str, title: str, message: str):
        """Afficher une notification"""
        toast = NotificationToast(title, message, toast_type, 3000, self)
        toast.show()

# Pour intégrer dans une fenêtre existante:
# 1. Remplacer les imports de widgets par les nouveaux
# 2. Appliquer le thème: self.setStyleSheet(ModernTheme.get_modern_stylesheet())
# 3. Remplacer les widgets existants par les versions modernes
# 4. Ajouter les animations et interactions
'''
    
    with open("integration_example.py", 'w', encoding='utf-8') as f:
        f.write(example_content)
    
    print("✅ Exemple d'intégration créé: integration_example.py")

def update_requirements():
    """Mettre à jour le fichier requirements.txt si nécessaire"""
    print("\n📋 Vérification des dépendances...")
    
    requirements_file = "requirements.txt"
    if os.path.exists(requirements_file):
        with open(requirements_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier que PyQt5 est présent
        if "PyQt5" not in content:
            print("⚠️  PyQt5 non trouvé dans requirements.txt")
            print("   Ajoutez: PyQt5>=5.15.0")
        else:
            print("✅ PyQt5 trouvé dans requirements.txt")
    else:
        print("⚠️  Fichier requirements.txt non trouvé")

def create_migration_guide():
    """Créer un guide de migration"""
    print("\n📖 Création du guide de migration...")
    
    guide_content = '''# Guide de Migration UI - GSlim

## 🔄 Étapes de Migration

### 1. Sauvegarde
- ✅ Sauvegardes créées dans `backup_ui_original/`
- ✅ Possibilité de restauration en cas de problème

### 2. Intégration Progressive

#### Étape 1: Thème de Base
```python
# Dans votre fenêtre principale
from styles.modern_theme import ModernTheme

# Appliquer le thème
self.setStyleSheet(ModernTheme.get_modern_stylesheet())
```

#### Étape 2: Remplacer les Widgets
```python
# Ancien
card = QFrame()
# Nouveau
card = ModernStatCard("Titre", "Valeur", "Sous-titre", "primary")
```

#### Étape 3: Ajouter les Animations
```python
# Mise à jour animée des valeurs
card.update_value("nouvelle_valeur", animate=True)

# Notifications
toast = NotificationToast("Titre", "Message", "success")
toast.show()
```

### 3. Test et Validation
```bash
# Tester les composants
python test_ui_improvements.py

# Démonstration complète
python demo_modern_ui.py
```

## 🛠️ Composants Disponibles

### Cartes de Statistiques
- `ModernStatCard` - Cartes animées avec clics
- Types: primary, success, warning, error, info, accent

### Navigation
- `ModernNavigationPanel` - Navigation avec indicateurs
- `ModernNavigationItem` - Éléments individuels

### Interactions
- `FloatingActionButton` - Bouton d'action flottant
- `NotificationToast` - Notifications animées
- `LoadingSpinner` - Indicateur de chargement

### Barres de Progression
- `ModernProgressBar` - Progression animée

## 🎨 Personnalisation

### Couleurs
```python
# Obtenir les couleurs du thème
colors = ModernTheme.get_theme_colors(is_dark=False)

# Couleurs de statut
status_colors = ModernTheme.get_status_colors()
```

### Effets Visuels
```python
# Dégradés
gradient = ModernTheme.create_gradient_background("#color1", "#color2")

# Ombres
shadow = ModernTheme.create_shadow_effect("lg")
```

## 🔧 Dépannage

### Problèmes Courants
1. **Styles non appliqués**: Vérifier `setStyleSheet()`
2. **Animations saccadées**: Réduire le nombre d'animations simultanées
3. **Couleurs incorrectes**: Utiliser les tokens de ModernTheme

### Restauration
```bash
# En cas de problème, restaurer depuis la sauvegarde
cp backup_ui_original/* src/views/
```

## 📞 Support
- Consulter `DESIGN_GUIDE.md` pour les standards
- Voir `UI_IMPROVEMENTS.md` pour les détails techniques
- Tester avec `demo_modern_ui.py`
'''
    
    with open("MIGRATION_GUIDE.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ Guide de migration créé: MIGRATION_GUIDE.md")

def run_integration():
    """Exécuter le processus d'intégration"""
    print("🚀 Intégration des améliorations UI - GSlim")
    print("="*50)
    
    steps = [
        ("Sauvegarde des fichiers originaux", backup_original_files),
        ("Mise à jour de l'application principale", update_main_app),
        ("Création d'un exemple d'intégration", create_integration_example),
        ("Vérification des dépendances", update_requirements),
        ("Création du guide de migration", create_migration_guide)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            result = step_func()
            if result is not False:  # None ou True sont considérés comme succès
                success_count += 1
                print(f"✅ {step_name} terminé")
            else:
                print(f"❌ {step_name} échoué")
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    print("\n" + "="*50)
    print("📊 RÉSUMÉ DE L'INTÉGRATION")
    print("="*50)
    print(f"Étapes réussies: {success_count}/{len(steps)}")
    
    if success_count == len(steps):
        print("\n🎉 Intégration réussie!")
        print("\n📋 Prochaines étapes:")
        print("1. Tester: python test_ui_improvements.py")
        print("2. Démonstration: python demo_modern_ui.py")
        print("3. Consulter: MIGRATION_GUIDE.md")
        print("4. Intégrer progressivement dans vos fenêtres")
    else:
        print("\n⚠️  Intégration partielle. Vérifiez les erreurs ci-dessus.")
        print("💡 En cas de problème, restaurez depuis backup_ui_original/")

def main():
    """Fonction principale"""
    try:
        run_integration()
        return 0
    except KeyboardInterrupt:
        print("\n⏹️  Intégration interrompue par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
