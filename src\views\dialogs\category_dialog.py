"""
Dialogue de gestion des catégories conforme au cahier des charges
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QTextEdit, QPushButton, QLabel, QMessageBox, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        Dialog, LineEdit, TextEdit, PushButton, TitleLabel, BodyLabel, 
        CardWidget, FluentIcon
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class CategoryDialog(QDialog):
    """
    Dialogue de catégorie selon les spécifications
    Champs: Nom (requis), Description
    """

    def __init__(self, parent=None, category_data=None):
        super().__init__(parent)
        self.category_data = category_data or {}
        self.logger = setup_logger(__name__)
        
        # Mode édition ou création
        self.is_edit_mode = bool(category_data)
        
        # Initialiser l'interface
        self._init_ui()
        
        # Pré-remplir si mode édition
        if self.is_edit_mode:
            self._populate_fields()
        
        self.logger.info(f"Dialogue catégorie ouvert ({'édition' if self.is_edit_mode else 'création'})")

    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Configuration de la fenêtre
        title = "Modifier la catégorie" if self.is_edit_mode else "Ajouter une catégorie"
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(400, 300)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # En-tête
        self._create_header(main_layout)
        
        # Formulaire principal
        self._create_form(main_layout)
        
        # Boutons d'action
        self._create_buttons(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du dialogue"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Modifier la catégorie" if self.is_edit_mode else "Nouvelle catégorie")
            subtitle = BodyLabel("Modifiez les informations de la catégorie" if self.is_edit_mode else "Saisissez les informations de la nouvelle catégorie")
        else:
            title = QLabel("Modifier la catégorie" if self.is_edit_mode else "Nouvelle catégorie")
            title.setFont(QFont("Segoe UI", 16, QFont.Bold))
            subtitle = QLabel("Modifiez les informations de la catégorie" if self.is_edit_mode else "Saisissez les informations de la nouvelle catégorie")
            subtitle.setFont(QFont("Segoe UI", 10))
        
        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_form(self, layout):
        """Créer le formulaire"""
        if FLUENT_AVAILABLE:
            form_card = CardWidget()
        else:
            form_card = QFrame()
            form_card.setFrameStyle(QFrame.Box)
        
        form_layout = QFormLayout(form_card)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)
        
        # Nom (requis)
        if FLUENT_AVAILABLE:
            self.name_input = LineEdit()
        else:
            self.name_input = QLineEdit()
        
        self.name_input.setPlaceholderText("Nom de la catégorie (requis)")
        form_layout.addRow("Nom *:", self.name_input)
        
        # Description
        if FLUENT_AVAILABLE:
            self.description_input = TextEdit()
        else:
            self.description_input = QTextEdit()
        
        self.description_input.setPlaceholderText("Description de la catégorie")
        self.description_input.setMaximumHeight(80)
        form_layout.addRow("Description:", self.description_input)
        
        layout.addWidget(form_card)

    def _create_buttons(self, layout):
        """Créer les boutons d'action"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.save_btn = PushButton("Enregistrer")
            self.save_btn.setIcon(FluentIcon.SAVE)
            
            self.cancel_btn = PushButton("Annuler")
            self.cancel_btn.setIcon(FluentIcon.CANCEL)
        else:
            self.save_btn = QPushButton("Enregistrer")
            self.cancel_btn = QPushButton("Annuler")
        
        # Connecter les signaux
        self.save_btn.clicked.connect(self._on_save)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)

    def _populate_fields(self):
        """Pré-remplir les champs en mode édition"""
        if not self.category_data:
            return
        
        self.name_input.setText(self.category_data.get('name', ''))
        
        if hasattr(self.description_input, 'setPlainText'):
            self.description_input.setPlainText(self.category_data.get('description', ''))
        else:
            self.description_input.setText(self.category_data.get('description', ''))

    def _on_save(self):
        """Gérer la sauvegarde avec validation"""
        try:
            # Validation
            if not self._validate_form():
                return
            
            # Accepter le dialogue
            self.accept()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _validate_form(self):
        """Valider le formulaire"""
        # Nom requis
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "Validation", "Le nom de la catégorie est requis.")
            self.name_input.setFocus()
            return False
        
        return True

    def get_category_data(self):
        """Récupérer les données de la catégorie depuis le formulaire"""
        description = ""
        if hasattr(self.description_input, 'toPlainText'):
            description = self.description_input.toPlainText()
        else:
            description = self.description_input.text()
        
        return {
            'id': self.category_data.get('id') if self.is_edit_mode else None,
            'name': self.name_input.text().strip(),
            'description': description.strip()
        }
