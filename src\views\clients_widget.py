"""
Widget de gestion des clients conforme au cahier des charges
Gestion avec points de fidélité selon les spécifications
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLineEdit, QPushButton, QHeaderView, QAbstractItemView,
    QMessageBox, QLabel, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        TableWidget, LineEdit, PushButton, SearchLineEdit,
        TitleLabel, CardWidget, FluentIcon
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from config.settings import config
from utils.logger import setup_logger


class ClientsWidget(QWidget):
    """
    Widget de gestion des clients selon les spécifications
    Fonctionnalités: CRUD, recherche, points de fidélité
    """
    
    # Signaux
    error_occurred = pyqtSignal(str)
    success_message = pyqtSignal(str)
    info_message = pyqtSignal(str)

    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.current_user = None
        
        # Variables d'instance
        self.clients_data = []
        self.filtered_clients = []
        
        # Initialiser l'interface
        self._init_ui()
        
        # Charger les données initiales
        self.refresh_data()
        
        self.logger.info("Widget clients initialisé")

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # En-tête
        self._create_header(main_layout)
        
        # Barre de recherche
        self._create_search_bar(main_layout)
        
        # Tableau des clients
        self._create_clients_table(main_layout)
        
        # Barre d'actions
        self._create_action_bar(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du module"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Clients")
        else:
            title = QLabel("Gestion des Clients")
            title.setFont(QFont("Segoe UI", 20, QFont.Bold))
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Bouton d'actualisation
        if FLUENT_AVAILABLE:
            refresh_btn = PushButton("Actualiser")
            refresh_btn.setIcon(FluentIcon.SYNC)
        else:
            refresh_btn = QPushButton("Actualiser")
        
        refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)

    def _create_search_bar(self, layout):
        """Créer la barre de recherche"""
        if FLUENT_AVAILABLE:
            search_card = CardWidget()
        else:
            search_card = QFrame()
            search_card.setFrameStyle(QFrame.Box)
        
        search_layout = QHBoxLayout(search_card)
        search_layout.setContentsMargins(15, 10, 15, 10)
        
        # Recherche selon spécifications
        search_label = QLabel("Recherche:")
        if FLUENT_AVAILABLE:
            self.search_input = SearchLineEdit()
            self.search_input.setPlaceholderText("Rechercher par nom ou contact...")
        else:
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("Rechercher par nom ou contact...")
        
        self.search_input.textChanged.connect(self._on_search_changed)
        
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, 1)
        
        layout.addWidget(search_card)

    def _create_clients_table(self, layout):
        """Créer le tableau des clients selon les spécifications"""
        if FLUENT_AVAILABLE:
            self.clients_table = TableWidget()
        else:
            self.clients_table = QTableWidget()
        
        # Configuration du tableau selon spécifications
        columns = [
            "ID", "Nom", "Email", "Téléphone", "Adresse", "Points de Fidélité"
        ]
        
        self.clients_table.setColumnCount(len(columns))
        self.clients_table.setHorizontalHeaderLabels(columns)
        
        # Configuration de l'affichage
        self.clients_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.clients_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.clients_table.setAlternatingRowColors(True)
        
        # Ajustement des colonnes
        header = self.clients_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        
        # Double-clic pour modifier
        self.clients_table.doubleClicked.connect(self._on_client_double_click)
        
        layout.addWidget(self.clients_table, 1)

    def _create_action_bar(self, layout):
        """Créer la barre d'actions selon les spécifications"""
        actions_layout = QHBoxLayout()
        
        # Boutons d'action selon spécifications
        if FLUENT_AVAILABLE:
            self.add_btn = PushButton("Ajouter")
            self.add_btn.setIcon(FluentIcon.ADD)
            
            self.edit_btn = PushButton("Modifier")
            self.edit_btn.setIcon(FluentIcon.EDIT)
            
            self.delete_btn = PushButton("Supprimer")
            self.delete_btn.setIcon(FluentIcon.DELETE)
            
            self.loyalty_btn = PushButton("Gérer Points")
            self.loyalty_btn.setIcon(FluentIcon.SETTING)
        else:
            self.add_btn = QPushButton("Ajouter")
            self.edit_btn = QPushButton("Modifier")
            self.delete_btn = QPushButton("Supprimer")
            self.loyalty_btn = QPushButton("Gérer Points")
        
        # État initial des boutons
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        self.loyalty_btn.setEnabled(False)
        
        # Connecter les signaux
        self.add_btn.clicked.connect(self._on_add_client)
        self.edit_btn.clicked.connect(self._on_edit_client)
        self.delete_btn.clicked.connect(self._on_delete_client)
        self.loyalty_btn.clicked.connect(self._on_manage_loyalty)
        
        # Gestion de la sélection
        self.clients_table.itemSelectionChanged.connect(self._on_selection_changed)
        
        # Assemblage
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addWidget(self.loyalty_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)

    def _on_search_changed(self, text):
        """Gérer le changement de recherche"""
        self._apply_search_filter()

    def _apply_search_filter(self):
        """Appliquer le filtre de recherche"""
        search_text = self.search_input.text().lower()
        
        self.filtered_clients = []
        
        for client in self.clients_data:
            if search_text:
                searchable_text = f"{client.get('name', '')} {client.get('email', '')} {client.get('phone', '')}".lower()
                if search_text not in searchable_text:
                    continue
            
            self.filtered_clients.append(client)
        
        self._update_table_display()

    def _update_table_display(self):
        """Mettre à jour l'affichage du tableau"""
        self.clients_table.setRowCount(len(self.filtered_clients))
        
        for row, client in enumerate(self.filtered_clients):
            # Colonnes selon spécifications
            self.clients_table.setItem(row, 0, QTableWidgetItem(str(client.get('id', ''))))
            self.clients_table.setItem(row, 1, QTableWidgetItem(client.get('name', '')))
            self.clients_table.setItem(row, 2, QTableWidgetItem(client.get('email', '')))
            self.clients_table.setItem(row, 3, QTableWidgetItem(client.get('phone', '')))
            self.clients_table.setItem(row, 4, QTableWidgetItem(client.get('address', '')))
            self.clients_table.setItem(row, 5, QTableWidgetItem(str(client.get('loyalty_points', 0))))

    def _on_selection_changed(self):
        """Gérer le changement de sélection"""
        has_selection = len(self.clients_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.loyalty_btn.setEnabled(has_selection)

    def _on_client_double_click(self):
        """Gérer le double-clic sur un client"""
        self._on_edit_client()

    def _on_add_client(self):
        """Ajouter un nouveau client"""
        try:
            from views.dialogs.client_dialog import ClientDialog

            dialog = ClientDialog(self)
            if dialog.exec_() == dialog.Accepted:
                client_data = dialog.get_client_data()

                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                client_id = db_manager.create_client(client_data)

                if client_id:
                    self.refresh_data()
                    self.success_message.emit("Client ajouté avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la sauvegarde du client")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout: {e}")
            self.error_occurred.emit(f"Erreur lors de l'ajout: {e}")

    def _on_edit_client(self):
        """Modifier le client sélectionné"""
        try:
            selected_row = self.clients_table.currentRow()
            if selected_row < 0:
                return

            client = self.filtered_clients[selected_row]

            from views.dialogs.client_dialog import ClientDialog

            dialog = ClientDialog(self, client)
            if dialog.exec_() == dialog.Accepted:
                client_data = dialog.get_client_data()

                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.update_client(client['id'], client_data)

                if success:
                    self.refresh_data()
                    self.success_message.emit("Client modifié avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la mise à jour du client")

        except Exception as e:
            self.logger.error(f"Erreur lors de la modification: {e}")
            self.error_occurred.emit(f"Erreur lors de la modification: {e}")

    def _on_delete_client(self):
        """Supprimer le client sélectionné"""
        try:
            selected_row = self.clients_table.currentRow()
            if selected_row < 0:
                return

            client = self.filtered_clients[selected_row]

            # Dialogue de confirmation selon spécifications
            reply = QMessageBox.question(
                self,
                "Confirmer la suppression",
                f"Êtes-vous sûr de vouloir supprimer le client '{client.get('name', '')}'?\n\n"
                f"Points de fidélité: {client.get('loyalty_points', 0)}\n"
                "Les ventes passées seront anonymisées.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Supprimer de la base de données avec anonymisation
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.delete_client(client['id'])

                if success:
                    self.refresh_data()
                    self.success_message.emit("Client supprimé avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la suppression du client")

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression: {e}")
            self.error_occurred.emit(f"Erreur lors de la suppression: {e}")

    def _on_manage_loyalty(self):
        """Gérer les points de fidélité"""
        try:
            selected_row = self.clients_table.currentRow()
            if selected_row < 0:
                return

            client = self.filtered_clients[selected_row]

            from views.dialogs.loyalty_points_dialog import LoyaltyPointsDialog

            dialog = LoyaltyPointsDialog(self, client)
            if dialog.exec_() == dialog.Accepted:
                points_data = dialog.get_points_change()

                # Mettre à jour les points en base de données
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.update_loyalty_points(
                    client['id'],
                    points_data['points_change'],
                    points_data['reason']
                )

                if success:
                    self.refresh_data()
                    if points_data['points_change'] > 0:
                        self.success_message.emit(f"{points_data['points_change']} points ajoutés")
                    else:
                        self.success_message.emit(f"{abs(points_data['points_change'])} points retirés")
                else:
                    self.error_occurred.emit("Erreur lors de la mise à jour des points")

        except Exception as e:
            self.logger.error(f"Erreur lors de la gestion des points: {e}")
            self.error_occurred.emit(f"Erreur lors de la gestion des points: {e}")

    def refresh_data(self):
        """Actualiser les données depuis la base de données"""
        try:
            db_manager = self.app_instance.get_database_manager()
            
            # Charger les clients
            self.clients_data = self._load_clients(db_manager)
            
            # Appliquer les filtres
            self._apply_search_filter()
            
            self.logger.info(f"{len(self.clients_data)} clients chargés")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement: {e}")
            self.error_occurred.emit(f"Erreur de chargement: {e}")

    def _load_clients(self, db_manager):
        """Charger les clients depuis la base de données"""
        try:
            # Récupérer le terme de recherche actuel
            search_term = self.search_input.text() if hasattr(self, 'search_input') else ""

            # Charger les clients avec filtre de recherche
            return db_manager.get_clients(search_term)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des clients: {e}")
            return []

    def set_current_user(self, user_data):
        """Définir l'utilisateur actuel"""
        self.current_user = user_data
