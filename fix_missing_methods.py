#!/usr/bin/env python3
"""
Correctif Méthodes Manquantes - GSlim
Ajoute les méthodes manquantes dans les contrôleurs
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def fix_article_controller():
    """Corriger le contrôleur d'articles"""
    print("🔧 Correction du contrôleur d'articles...")
    
    try:
        # Lire le fichier actuel
        with open("src/controllers/article_controller.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Ajouter les méthodes manquantes
        additional_methods = '''
    def get_stock_statistics(self):
        """Obtenir les statistiques de stock"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {
                    'total_articles': 0,
                    'low_stock_count': 0,
                    'total_value': 0.0,
                    'out_of_stock': 0
                }
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {
                    'total_articles': 0,
                    'low_stock_count': 0,
                    'total_value': 0.0,
                    'out_of_stock': 0
                }
            
            # Total des articles
            cursor.execute("SELECT COUNT(*) FROM articles")
            total_articles = cursor.fetchone()[0]
            
            # Articles en stock faible
            cursor.execute("SELECT COUNT(*) FROM articles WHERE quantite_stock <= seuil_alerte")
            low_stock_count = cursor.fetchone()[0]
            
            # Valeur totale du stock
            cursor.execute("SELECT SUM(prix_unitaire * quantite_stock) FROM articles")
            result = cursor.fetchone()[0]
            total_value = result if result else 0.0
            
            # Articles en rupture
            cursor.execute("SELECT COUNT(*) FROM articles WHERE quantite_stock = 0")
            out_of_stock = cursor.fetchone()[0]
            
            return {
                'total_articles': total_articles,
                'low_stock_count': low_stock_count,
                'total_value': total_value,
                'out_of_stock': out_of_stock
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques de stock: {e}")
            return {
                'total_articles': 0,
                'low_stock_count': 0,
                'total_value': 0.0,
                'out_of_stock': 0
            }
    
    def get_all(self):
        """Alias pour get_all_articles (compatibilité)"""
        return self.get_all_articles()
'''
        
        # Ajouter les méthodes avant la fin de la classe
        if content.strip().endswith('return False'):
            # Trouver la dernière méthode et ajouter avant
            lines = content.split('\n')
            insert_index = len(lines) - 1
            
            # Trouver un bon endroit pour insérer
            for i in range(len(lines) - 1, -1, -1):
                if lines[i].strip().startswith('def ') and not lines[i].strip().startswith('def __'):
                    # Trouver la fin de cette méthode
                    for j in range(i + 1, len(lines)):
                        if lines[j].strip() and not lines[j].startswith('    ') and not lines[j].startswith('\t'):
                            insert_index = j
                            break
                    break
            
            lines.insert(insert_index, additional_methods)
            content = '\n'.join(lines)
        else:
            content += additional_methods
        
        # Sauvegarder le fichier modifié
        with open("src/controllers/article_controller.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Contrôleur d'articles corrigé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        return False


def fix_supplier_controller():
    """Corriger le contrôleur de fournisseurs"""
    print("🔧 Correction du contrôleur de fournisseurs...")
    
    try:
        # Lire le fichier actuel
        with open("src/controllers/supplier_controller.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Ajouter les méthodes manquantes
        additional_methods = '''
    def count(self):
        """Compter le nombre total de fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return 0
            
            cursor = self.db_manager.cursor
            if not cursor:
                return 0
            
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            result = cursor.fetchone()
            return result[0] if result else 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors du comptage des fournisseurs: {e}")
            return 0
    
    def get_supplier_by_id(self, supplier_id):
        """Récupérer un fournisseur par son ID"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return None
            
            cursor = self.db_manager.cursor
            if not cursor:
                return None
            
            cursor.execute("SELECT * FROM suppliers WHERE id = ?", (supplier_id,))
            result = cursor.fetchone()
            
            if result:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du fournisseur {supplier_id}: {e}")
            return None
    
    def update_supplier(self, supplier_id, supplier_data):
        """Mettre à jour un fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            query = """
                UPDATE suppliers 
                SET nom = ?, contact = ?, telephone = ?, email = ?, adresse = ?
                WHERE id = ?
            """
            
            cursor.execute(query, (
                supplier_data.get('nom'),
                supplier_data.get('contact'),
                supplier_data.get('telephone'),
                supplier_data.get('email'),
                supplier_data.get('adresse'),
                supplier_id
            ))
            
            self.db_manager.connection.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du fournisseur: {e}")
            return False
    
    def delete_supplier(self, supplier_id):
        """Supprimer un fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Vérifier s'il y a des articles liés
            cursor.execute("SELECT COUNT(*) FROM articles WHERE fournisseur_id = ?", (supplier_id,))
            article_count = cursor.fetchone()[0]
            
            if article_count > 0:
                raise Exception(f"Impossible de supprimer: {article_count} articles liés à ce fournisseur")
            
            cursor.execute("DELETE FROM suppliers WHERE id = ?", (supplier_id,))
            self.db_manager.connection.commit()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression du fournisseur: {e}")
            raise
'''
        
        # Ajouter les méthodes avant la fin de la classe
        if 'def get_statistics(self):' in content:
            # Insérer après la méthode get_statistics
            lines = content.split('\n')
            insert_index = len(lines)
            
            for i, line in enumerate(lines):
                if 'def get_statistics(self):' in line:
                    # Trouver la fin de cette méthode
                    for j in range(i + 1, len(lines)):
                        if lines[j].strip() and not lines[j].startswith('    ') and not lines[j].startswith('\t'):
                            insert_index = j
                            break
                    break
            
            lines.insert(insert_index, additional_methods)
            content = '\n'.join(lines)
        else:
            content += additional_methods
        
        # Sauvegarder le fichier modifié
        with open("src/controllers/supplier_controller.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Contrôleur de fournisseurs corrigé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        return False


def test_corrections():
    """Tester les corrections appliquées"""
    print("🧪 Test des corrections...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.article_controller import ArticleController
        from controllers.supplier_controller import SupplierController
        
        # Test du gestionnaire de base de données
        db_manager = DatabaseManager()
        db_manager.connect()
        
        # Test du contrôleur d'articles
        article_controller = ArticleController(db_manager)
        
        if hasattr(article_controller, 'get_stock_statistics'):
            stats = article_controller.get_stock_statistics()
            print(f"✅ get_stock_statistics: {stats}")
        else:
            print("❌ get_stock_statistics manquante")
            return False
        
        if hasattr(article_controller, 'get_all'):
            articles = article_controller.get_all()
            print(f"✅ get_all: {len(articles)} articles")
        else:
            print("❌ get_all manquante")
            return False
        
        # Test du contrôleur de fournisseurs
        supplier_controller = SupplierController(db_manager)
        
        if hasattr(supplier_controller, 'count'):
            count = supplier_controller.count()
            print(f"✅ count: {count} fournisseurs")
        else:
            print("❌ count manquante")
            return False
        
        db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Erreur de test: {e}")
        return False


def main():
    """Fonction principale"""
    print("🔧 CORRECTIF MÉTHODES MANQUANTES - GSLIM")
    print("="*50)
    
    fixes = [
        ("Contrôleur d'articles", fix_article_controller),
        ("Contrôleur de fournisseurs", fix_supplier_controller),
        ("Test des corrections", test_corrections)
    ]
    
    success_count = 0
    for fix_name, fix_func in fixes:
        print(f"\n📋 {fix_name}...")
        try:
            result = fix_func()
            if result:
                success_count += 1
                print(f"✅ {fix_name} - Succès")
            else:
                print(f"❌ {fix_name} - Échec")
        except Exception as e:
            print(f"❌ {fix_name} - Erreur: {e}")
    
    print(f"\n📊 Correctifs appliqués: {success_count}/{len(fixes)}")
    
    if success_count == len(fixes):
        print("\n🎉 TOUTES LES MÉTHODES MANQUANTES CORRIGÉES !")
        print("✅ get_stock_statistics ajoutée au contrôleur d'articles")
        print("✅ get_all ajoutée au contrôleur d'articles")
        print("✅ count ajoutée au contrôleur de fournisseurs")
        print("✅ Méthodes CRUD complètes pour les fournisseurs")
        
        print("\n🚀 Votre application devrait maintenant fonctionner sans erreurs !")
        print("   python main.py")
        
        return True
    else:
        print("\n⚠️  Correctifs partiels appliqués")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
