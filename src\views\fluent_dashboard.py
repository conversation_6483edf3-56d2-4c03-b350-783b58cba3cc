"""
GSlim v1.0.0 - Interface moderne avec Fluent Widgets
Tableau de bord principal avec design moderne et animations fluides
"""

import sys
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QFrame, QSizePolicy, QSpacerItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

try:
    from qfluentwidgets import (
        FluentWindow, NavigationInterface, NavigationItemPosition,
        PushButton, CardWidget, InfoBadge, FluentIcon, setTheme, Theme,
        TitleLabel, BodyLabel, CaptionLabel, StrongBodyLabel,
        ToggleButton, ToolButton, InfoBar, InfoBarPosition
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False
    print("⚠️ Fluent Widgets non disponible. Installation: pip install PyQt-Fluent-Widgets")

from styles.fluent_theme import FluentTheme


class ModernStatCard(CardWidget if FLUENT_AVAILABLE else QFrame):
    """Carte de statistique moderne avec animations Fluent"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title: str, value: str, icon: str, color: str = "primary", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.icon_name = icon
        self.color = color
        self.is_hovered = False
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface de la carte"""
        self.setFixedSize(280, 160)
        self.setCursor(Qt.PointingHandCursor)
        
        # Layout principal
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        # Header avec icône
        header_layout = QHBoxLayout()
        
        # Icône
        if FLUENT_AVAILABLE:
            icon_widget = ToolButton()
            icon_widget.setIcon(getattr(FluentIcon, self.icon_name, FluentIcon.INFO))
            icon_widget.setFixedSize(48, 48)
            icon_widget.setEnabled(False)
            header_layout.addWidget(icon_widget)
        
        header_layout.addStretch()
        
        # Badge (optionnel)
        if FLUENT_AVAILABLE:
            badge = InfoBadge.success("●", self)
            badge.setFixedSize(12, 12)
            header_layout.addWidget(badge)
        
        layout.addLayout(header_layout)
        
        # Valeur principale
        if FLUENT_AVAILABLE:
            self.value_label = TitleLabel(self.value)
        else:
            self.value_label = QLabel(self.value)
            self.value_label.setStyleSheet("font-size: 32px; font-weight: bold; color: #F0F0F0;")
        
        layout.addWidget(self.value_label)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = BodyLabel(self.title)
        else:
            title_label = QLabel(self.title)
            title_label.setStyleSheet("font-size: 14px; color: #B0B0B0;")
        
        layout.addWidget(title_label)
        
        self.setLayout(layout)
        
        # Appliquer le style selon la couleur
        self.apply_color_style()
    
    def apply_color_style(self):
        """Appliquer le style de couleur"""
        if not FLUENT_AVAILABLE:
            colors = {
                "primary": "#2196F3",
                "success": "#4CAF50", 
                "warning": "#FF9800",
                "error": "#F44336",
                "info": "#00BCD4"
            }
            
            color = colors.get(self.color, "#2196F3")
            self.setStyleSheet(f"""
                QFrame {{
                    background-color: #1F1F1F;
                    border: 1px solid #2C2C2C;
                    border-left: 4px solid {color};
                    border-radius: 12px;
                    padding: 16px;
                }}
                QFrame:hover {{
                    background-color: #252525;
                    border-color: {color};
                    transform: translateY(-2px);
                }}
            """)
    
    def setup_animations(self):
        """Configurer les animations"""
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def enterEvent(self, event):
        """Animation d'entrée de survol"""
        self.is_hovered = True
        if hasattr(self, 'hover_animation'):
            current_rect = self.geometry()
            new_rect = current_rect.adjusted(0, -2, 0, -2)
            
            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(new_rect)
            self.hover_animation.start()
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation de sortie de survol"""
        self.is_hovered = False
        if hasattr(self, 'hover_animation'):
            current_rect = self.geometry()
            new_rect = current_rect.adjusted(0, 2, 0, 2)
            
            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(new_rect)
            self.hover_animation.start()
        
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Gérer le clic"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)
    
    def update_value(self, new_value: str):
        """Mettre à jour la valeur avec animation"""
        self.value = new_value
        self.value_label.setText(new_value)


class FluentDashboard(FluentWindow if FLUENT_AVAILABLE else QWidget):
    """Tableau de bord principal avec Fluent Design"""
    
    def __init__(self):
        super().__init__()
        self.current_theme = "dark"
        self.setup_window()
        self.setup_navigation()
        self.setup_ui()
        self.setup_timer()
        
        # Appliquer le thème sombre par défaut
        if FLUENT_AVAILABLE:
            setTheme(Theme.DARK)
        
        self.apply_custom_styles()
    
    def setup_window(self):
        """Configurer la fenêtre principale"""
        self.setWindowTitle("GSlim v1.0.0 - Tableau de Bord")
        self.setGeometry(100, 100, 1400, 900)
        
        if FLUENT_AVAILABLE:
            self.setMicaEffectEnabled(True)
    
    def setup_navigation(self):
        """Configurer la navigation Fluent"""
        if not FLUENT_AVAILABLE:
            return
        
        # Navigation items
        self.addSubInterface(
            interface=QWidget(),
            icon=FluentIcon.HOME,
            text="Tableau de bord",
            position=NavigationItemPosition.TOP
        )
        
        self.addSubInterface(
            interface=QWidget(),
            icon=FluentIcon.PACKAGE,
            text="Produits",
            position=NavigationItemPosition.TOP
        )
        
        self.addSubInterface(
            interface=QWidget(),
            icon=FluentIcon.SHOPPING_CART,
            text="Ventes",
            position=NavigationItemPosition.TOP
        )
        
        self.addSubInterface(
            interface=QWidget(),
            icon=FluentIcon.CHART,
            text="Statistiques",
            position=NavigationItemPosition.TOP
        )
        
        self.addSubInterface(
            interface=QWidget(),
            icon=FluentIcon.SETTING,
            text="Paramètres",
            position=NavigationItemPosition.BOTTOM
        )
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        if FLUENT_AVAILABLE:
            self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(32, 24, 32, 24)
        main_layout.setSpacing(24)
        
        # Header
        self.create_header(main_layout)
        
        # Cartes de statistiques
        self.create_stats_section(main_layout)
        
        # Section des actions
        self.create_actions_section(main_layout)
        
        # Zone graphique (placeholder)
        self.create_chart_section(main_layout)
        
        central_widget.setLayout(main_layout)
        
        if not FLUENT_AVAILABLE:
            self.setLayout(main_layout)
    
    def create_header(self, layout):
        """Créer l'en-tête"""
        header_layout = QHBoxLayout()
        
        # Titre principal
        if FLUENT_AVAILABLE:
            title = TitleLabel("Tableau de Bord")
        else:
            title = QLabel("Tableau de Bord")
            title.setStyleSheet("font-size: 28px; font-weight: bold; color: #F0F0F0;")
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(12)
        
        # Bouton changement de thème
        if FLUENT_AVAILABLE:
            self.theme_btn = ToggleButton()
            self.theme_btn.setText("🌙" if self.current_theme == "dark" else "☀️")
            self.theme_btn.clicked.connect(self.toggle_theme)
        else:
            self.theme_btn = QPushButton("🌙")
            self.theme_btn.clicked.connect(self.toggle_theme)
            self.theme_btn.setStyleSheet("""
                QPushButton {
                    background-color: #00BCD4;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #00ACC1;
                }
            """)
        
        buttons_layout.addWidget(self.theme_btn)
        
        # Bouton déconnexion
        if FLUENT_AVAILABLE:
            logout_btn = PushButton("Déconnexion")
            logout_btn.setIcon(FluentIcon.POWER_BUTTON)
        else:
            logout_btn = QPushButton("Déconnexion")
            logout_btn.setStyleSheet("""
                QPushButton {
                    background-color: #F44336;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #E53935;
                }
            """)
        
        logout_btn.clicked.connect(self.logout)
        buttons_layout.addWidget(logout_btn)
        
        header_layout.addLayout(buttons_layout)
        
        # Info mise à jour
        if FLUENT_AVAILABLE:
            update_info = CaptionLabel("Dernière mise à jour: 02/08/2025 à 15:58")
        else:
            update_info = QLabel("Dernière mise à jour: 02/08/2025 à 15:58")
            update_info.setStyleSheet("color: #B0B0B0; font-size: 12px;")
        
        header_layout.addWidget(update_info)
        
        layout.addLayout(header_layout)
    
    def create_stats_section(self, layout):
        """Créer la section des statistiques"""
        stats_layout = QGridLayout()
        stats_layout.setSpacing(20)
        
        # Données de démonstration
        stats_data = [
            ("Produits Disponibles", "1,234", "PACKAGE", "primary"),
            ("Ventes du Mois", "89", "SHOPPING_CART", "success"),
            ("Revenu Total", "€45,678", "MONEY", "info"),
            ("Commandes Pending", "23", "CLOCK", "warning")
        ]
        
        self.stat_cards = []
        for i, (title, value, icon, color) in enumerate(stats_data):
            card = ModernStatCard(title, value, icon, color)
            card.clicked.connect(lambda t=title: self.on_stat_card_clicked(t))
            self.stat_cards.append(card)
            
            row = i // 4
            col = i % 4
            stats_layout.addWidget(card, row, col)
        
        layout.addLayout(stats_layout)
    
    def create_actions_section(self, layout):
        """Créer la section des actions"""
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(16)
        
        # Bouton Actualiser
        if FLUENT_AVAILABLE:
            refresh_btn = PushButton("Actualiser")
            refresh_btn.setIcon(FluentIcon.SYNC)
        else:
            refresh_btn = QPushButton("🔄 Actualiser")
            refresh_btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
        
        refresh_btn.clicked.connect(self.refresh_data)
        actions_layout.addWidget(refresh_btn)
        
        # Bouton Rapport Rapide
        if FLUENT_AVAILABLE:
            report_btn = PushButton("Rapport Rapide")
            report_btn.setIcon(FluentIcon.DOCUMENT)
        else:
            report_btn = QPushButton("📄 Rapport Rapide")
            report_btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 24px;
                    font-size: 14px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #388E3C;
                }
            """)
        
        report_btn.clicked.connect(self.generate_report)
        actions_layout.addWidget(report_btn)
        
        actions_layout.addStretch()
        layout.addLayout(actions_layout)
    
    def create_chart_section(self, layout):
        """Créer la section graphique"""
        if FLUENT_AVAILABLE:
            chart_card = CardWidget()
        else:
            chart_card = QFrame()
            chart_card.setStyleSheet("""
                QFrame {
                    background-color: #1F1F1F;
                    border: 1px solid #2C2C2C;
                    border-radius: 12px;
                    padding: 20px;
                }
            """)
        
        chart_card.setMinimumHeight(300)
        
        chart_layout = QVBoxLayout()
        
        if FLUENT_AVAILABLE:
            chart_title = StrongBodyLabel("Performance des Ventes")
        else:
            chart_title = QLabel("Performance des Ventes")
            chart_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #F0F0F0; margin-bottom: 16px;")
        
        chart_layout.addWidget(chart_title)
        
        # Placeholder pour le graphique
        chart_placeholder = QLabel("📈 Graphique de performance (à implémenter)")
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_placeholder.setStyleSheet("color: #B0B0B0; font-size: 16px; padding: 60px;")
        chart_layout.addWidget(chart_placeholder)
        
        chart_card.setLayout(chart_layout)
        layout.addWidget(chart_card)
    
    def setup_timer(self):
        """Configurer le timer pour les mises à jour"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_stats)
        self.update_timer.start(5000)  # Mise à jour toutes les 5 secondes
    
    def apply_custom_styles(self):
        """Appliquer les styles personnalisés"""
        if FLUENT_AVAILABLE:
            return
        
        self.setStyleSheet("""
            QWidget {
                background-color: #121212;
                color: #F0F0F0;
                font-family: 'Segoe UI', 'Roboto', 'Poppins', sans-serif;
            }
        """)
    
    def toggle_theme(self):
        """Basculer entre thème clair et sombre"""
        if FLUENT_AVAILABLE:
            if self.current_theme == "dark":
                setTheme(Theme.LIGHT)
                self.current_theme = "light"
                self.theme_btn.setText("☀️")
            else:
                setTheme(Theme.DARK)
                self.current_theme = "dark"
                self.theme_btn.setText("🌙")
        
        self.show_info_bar("Thème changé", f"Thème {self.current_theme} appliqué")
    
    def refresh_data(self):
        """Actualiser les données"""
        import random
        
        # Simuler la mise à jour des données
        for card in self.stat_cards:
            if "€" in card.value:
                new_value = f"€{random.randint(40000, 50000):,}"
            else:
                new_value = str(random.randint(50, 2000))
            card.update_value(new_value)
        
        self.show_info_bar("Actualisation", "Données mises à jour avec succès")
    
    def generate_report(self):
        """Générer un rapport"""
        self.show_info_bar("Rapport", "Génération du rapport en cours...")
    
    def logout(self):
        """Déconnexion"""
        self.show_info_bar("Déconnexion", "Déconnexion en cours...")
        QTimer.singleShot(2000, self.close)
    
    def on_stat_card_clicked(self, title):
        """Gérer le clic sur une carte de statistique"""
        self.show_info_bar("Navigation", f"Ouverture de {title}")
    
    def update_stats(self):
        """Mettre à jour les statistiques automatiquement"""
        # Mise à jour automatique légère
        pass
    
    def show_info_bar(self, title, message):
        """Afficher une barre d'information"""
        if FLUENT_AVAILABLE:
            InfoBar.success(
                title=title,
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
        else:
            print(f"💬 {title}: {message}")


def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("GSlim")
    app.setApplicationVersion("1.0.0")
    
    # Créer et afficher la fenêtre
    window = FluentDashboard()
    window.show()
    
    # Démarrer la boucle d'événements
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
