#!/usr/bin/env python3
"""
Test final de la navigation - Vérification que tous les modules s'affichent
"""

import sys
import os

# Ajouter src au path
sys.path.insert(0, 'src')

def test_application_complete():
    """Test complet de l'application"""
    print("🚀 TEST FINAL DE L'APPLICATION GSLIM")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from app import GSlimApp
        
        print("✅ Imports réussis")
        
        # Créer l'application
        app = GSlimApp()
        print("✅ GSlimApp créé")
        
        # Initialiser la base de données
        db_manager = app.get_database_manager()
        print("✅ DatabaseManager initialisé")
        
        # Tester la connexion à la base
        stats = db_manager.get_dashboard_stats()
        print(f"✅ Statistiques récupérées: {len(stats)} KPIs")
        
        # Afficher les statistiques
        print("\n📊 STATISTIQUES ACTUELLES:")
        for key, value in stats.items():
            print(f"   • {key}: {value}")
        
        print("\n🎯 MODULES À TESTER:")
        print("   1. Dashboard - Tableau de bord avec KPIs")
        print("   2. Produits - Gestion complète des produits")
        print("   3. Clients - Gestion avec points de fidélité")
        print("   4. Fournisseurs - Gestion avec évaluation")
        print("   5. Ventes - Système de ventes")
        print("   6. Paramètres - Configuration et thèmes")
        
        print("\n✅ APPLICATION PRÊTE POUR LE TEST MANUEL")
        print("\n📋 INSTRUCTIONS DE TEST:")
        print("   1. Lancez: python main.py")
        print("   2. Connectez-vous: admin / admin123")
        print("   3. Vérifiez que tous les modules apparaissent dans la navigation")
        print("   4. Testez chaque module individuellement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    success = test_application_complete()
    
    if success:
        print("\n🎉 TEST RÉUSSI - APPLICATION PRÊTE !")
    else:
        print("\n❌ TEST ÉCHOUÉ - VÉRIFIEZ LES ERREURS")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
