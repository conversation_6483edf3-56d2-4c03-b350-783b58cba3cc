"""
Tableau de bord amélioré avec les nouveaux composants UI modernes
Exemple d'utilisation des widgets avec animations et design moderne
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGridLayout, QScrollArea, QSizePolicy, QSpacerItem
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor

try:
    from qfluentwidgets import FluentIcon
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from widgets.modern_widgets import ModernStatCard, ModernProgressBar
from widgets.advanced_ui import FloatingActionButton, NotificationToast, LoadingSpinner
from widgets.modern_navigation import ModernNavigationPanel
from styles.modern_theme import ModernTheme
from utils.logger import setup_logger


class EnhancedDashboardWindow(QWidget):
    """Tableau de bord amélioré avec composants modernes"""
    
    # Signaux
    navigate_to_page = pyqtSignal(str)
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.logger = setup_logger(__name__)
        
        # Données de démonstration
        self.demo_data = {
            'total_articles': 1250,
            'low_stock_items': 23,
            'total_suppliers': 45,
            'pending_orders': 8,
            'monthly_revenue': 125000,
            'stock_value': 89500
        }
        
        self.setup_ui()
        self.setup_demo_timer()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        # Layout principal
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Navigation moderne
        self.create_navigation(main_layout)
        
        # Zone de contenu principal
        self.create_content_area(main_layout)
        
        # Bouton d'action flottant
        self.create_floating_action_button()
        
        self.setLayout(main_layout)
        
        # Appliquer le thème moderne
        self.setStyleSheet(ModernTheme.get_modern_stylesheet())
    
    def create_navigation(self, layout):
        """Créer la navigation moderne"""
        self.navigation = ModernNavigationPanel()
        self.navigation.page_changed.connect(self.on_page_changed)
        
        # Ajouter les éléments de navigation
        self.navigation.add_navigation_item("dashboard", "Tableau de bord")
        self.navigation.add_navigation_item("articles", "Articles")
        self.navigation.add_navigation_item("suppliers", "Fournisseurs")
        self.navigation.add_navigation_item("movements", "Mouvements")
        self.navigation.add_navigation_item("orders", "Commandes")
        self.navigation.add_navigation_item("reports", "Rapports")
        self.navigation.add_navigation_item("settings", "Paramètres")
        
        # Définir la page active
        self.navigation.set_active_page("dashboard")
        
        layout.addWidget(self.navigation)
    
    def create_content_area(self, layout):
        """Créer la zone de contenu principal"""
        content_widget = QWidget()
        content_widget.setProperty("class", "content-area")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(32, 32, 32, 32)
        content_layout.setSpacing(24)
        
        # En-tête du dashboard
        self.create_dashboard_header(content_layout)
        
        # Zone de scroll pour le contenu
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(24)
        
        # Cartes de statistiques
        self.create_stats_section(scroll_layout)
        
        # Section des graphiques et indicateurs
        self.create_charts_section(scroll_layout)
        
        # Section des alertes et notifications
        self.create_alerts_section(scroll_layout)
        
        # Spacer pour pousser le contenu vers le haut
        scroll_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        scroll_area.setWidget(scroll_widget)
        content_layout.addWidget(scroll_area)
        
        content_widget.setLayout(content_layout)
        layout.addWidget(content_widget)
    
    def create_dashboard_header(self, layout):
        """Créer l'en-tête du dashboard"""
        header_layout = QHBoxLayout()
        
        # Titre et description
        title_layout = QVBoxLayout()
        title_layout.setSpacing(8)
        
        title_label = QLabel("Tableau de Bord")
        title_label.setProperty("class", "title")
        title_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Vue d'ensemble de votre activité")
        subtitle_label.setProperty("class", "body")
        subtitle_label.setStyleSheet(f"color: {ModernTheme.LIGHT_TEXT_SECONDARY};")
        title_layout.addWidget(subtitle_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Boutons d'action
        refresh_btn = QPushButton("Actualiser")
        refresh_btn.setProperty("class", "secondary")
        refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_btn)
        
        export_btn = QPushButton("Exporter")
        export_btn.clicked.connect(self.export_data)
        header_layout.addWidget(export_btn)
        
        layout.addLayout(header_layout)
    
    def create_stats_section(self, layout):
        """Créer la section des statistiques"""
        stats_layout = QGridLayout()
        stats_layout.setSpacing(16)
        
        # Cartes de statistiques avec animations
        self.total_articles_card = ModernStatCard(
            "Total Articles", 
            str(self.demo_data['total_articles']),
            "En stock",
            "primary"
        )
        self.total_articles_card.clicked.connect(lambda: self.navigate_to_page.emit("articles"))
        stats_layout.addWidget(self.total_articles_card, 0, 0)
        
        self.low_stock_card = ModernStatCard(
            "Stock Faible",
            str(self.demo_data['low_stock_items']),
            "Articles à réapprovisionner",
            "warning"
        )
        self.low_stock_card.clicked.connect(lambda: self.show_notification("warning", "Stock Faible", "23 articles nécessitent un réapprovisionnement"))
        stats_layout.addWidget(self.low_stock_card, 0, 1)
        
        self.suppliers_card = ModernStatCard(
            "Fournisseurs",
            str(self.demo_data['total_suppliers']),
            "Partenaires actifs",
            "success"
        )
        self.suppliers_card.clicked.connect(lambda: self.navigate_to_page.emit("suppliers"))
        stats_layout.addWidget(self.suppliers_card, 0, 2)
        
        self.orders_card = ModernStatCard(
            "Commandes",
            str(self.demo_data['pending_orders']),
            "En attente",
            "error"
        )
        self.orders_card.clicked.connect(lambda: self.navigate_to_page.emit("orders"))
        stats_layout.addWidget(self.orders_card, 0, 3)
        
        layout.addLayout(stats_layout)
    
    def create_charts_section(self, layout):
        """Créer la section des graphiques"""
        charts_frame = QFrame()
        charts_frame.setProperty("class", "card")
        charts_frame.setMinimumHeight(300)
        
        charts_layout = QVBoxLayout(charts_frame)
        charts_layout.setContentsMargins(24, 20, 24, 20)
        charts_layout.setSpacing(16)
        
        # Titre de la section
        charts_title = QLabel("Indicateurs de Performance")
        charts_title.setProperty("class", "heading")
        charts_layout.addWidget(charts_title)
        
        # Barres de progression modernes
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(16)
        
        # Progression du stock
        stock_label = QLabel("Niveau de Stock Global")
        stock_label.setProperty("class", "body")
        progress_layout.addWidget(stock_label)
        
        self.stock_progress = ModernProgressBar(0, 100)
        self.stock_progress.setValue(75, animate=True)
        progress_layout.addWidget(self.stock_progress)
        
        # Progression des ventes
        sales_label = QLabel("Objectif Ventes Mensuel")
        sales_label.setProperty("class", "body")
        progress_layout.addWidget(sales_label)
        
        self.sales_progress = ModernProgressBar(0, 100)
        self.sales_progress.setValue(62, animate=True)
        progress_layout.addWidget(self.sales_progress)
        
        charts_layout.addLayout(progress_layout)
        charts_layout.addStretch()
        
        layout.addWidget(charts_frame)
    
    def create_alerts_section(self, layout):
        """Créer la section des alertes"""
        alerts_frame = QFrame()
        alerts_frame.setProperty("class", "card")
        
        alerts_layout = QVBoxLayout(alerts_frame)
        alerts_layout.setContentsMargins(24, 20, 24, 20)
        alerts_layout.setSpacing(16)
        
        # Titre de la section
        alerts_title = QLabel("Alertes et Notifications")
        alerts_title.setProperty("class", "heading")
        alerts_layout.addWidget(alerts_title)
        
        # Boutons de démonstration des notifications
        demo_layout = QHBoxLayout()
        
        info_btn = QPushButton("Info")
        info_btn.setProperty("class", "outline")
        info_btn.clicked.connect(lambda: self.show_notification("info", "Information", "Ceci est une notification d'information"))
        demo_layout.addWidget(info_btn)
        
        success_btn = QPushButton("Succès")
        success_btn.setProperty("class", "success")
        success_btn.clicked.connect(lambda: self.show_notification("success", "Succès", "Opération réalisée avec succès"))
        demo_layout.addWidget(success_btn)
        
        warning_btn = QPushButton("Avertissement")
        warning_btn.setProperty("class", "warning")
        warning_btn.clicked.connect(lambda: self.show_notification("warning", "Attention", "Ceci est un avertissement"))
        demo_layout.addWidget(warning_btn)
        
        error_btn = QPushButton("Erreur")
        error_btn.setProperty("class", "danger")
        error_btn.clicked.connect(lambda: self.show_notification("error", "Erreur", "Une erreur s'est produite"))
        demo_layout.addWidget(error_btn)
        
        demo_layout.addStretch()
        alerts_layout.addLayout(demo_layout)
        
        layout.addWidget(alerts_frame)
    
    def create_floating_action_button(self):
        """Créer le bouton d'action flottant"""
        self.fab = FloatingActionButton("+")
        self.fab.setParent(self)
        self.fab.clicked.connect(self.on_fab_clicked)
        
        # Positionner le FAB en bas à droite
        self.fab.move(self.width() - 80, self.height() - 80)
    
    def setup_demo_timer(self):
        """Configurer le timer pour la démonstration"""
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.update_demo_data)
        self.demo_timer.start(5000)  # Mise à jour toutes les 5 secondes
    
    def update_demo_data(self):
        """Mettre à jour les données de démonstration"""
        import random
        
        # Simuler des changements de données
        self.demo_data['total_articles'] += random.randint(-5, 10)
        self.demo_data['low_stock_items'] = max(0, self.demo_data['low_stock_items'] + random.randint(-2, 3))
        
        # Mettre à jour les cartes avec animation
        self.total_articles_card.update_value(str(self.demo_data['total_articles']), animate=True)
        self.low_stock_card.update_value(str(self.demo_data['low_stock_items']), animate=True)
    
    def on_page_changed(self, route_key: str):
        """Gérer le changement de page"""
        self.navigate_to_page.emit(route_key)
    
    def on_fab_clicked(self):
        """Gérer le clic sur le FAB"""
        self.show_notification("info", "Action Rapide", "Bouton d'action flottant cliqué!")
    
    def show_notification(self, toast_type: str, title: str, message: str):
        """Afficher une notification toast"""
        toast = NotificationToast(title, message, toast_type, 3000, self)
        toast.show()
    
    def refresh_data(self):
        """Actualiser les données"""
        self.show_notification("info", "Actualisation", "Données mises à jour avec succès")
        self.update_demo_data()
    
    def export_data(self):
        """Exporter les données"""
        self.show_notification("success", "Export", "Données exportées avec succès")
    
    def resizeEvent(self, event):
        """Gérer le redimensionnement de la fenêtre"""
        super().resizeEvent(event)
        if hasattr(self, 'fab'):
            # Repositionner le FAB
            self.fab.move(self.width() - 80, self.height() - 80)
