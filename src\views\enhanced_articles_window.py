"""
Module Articles Amélioré - GSlim
Interface moderne pour la gestion des articles avec widgets avancés
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QSplitter, QMessageBox, QDialog, QScrollArea, QTabWidget,
    QGroupBox, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QColor, QIcon

try:
    from qfluentwidgets import (
        FluentIcon, InfoBar, InfoBarPosition, CardWidget, TitleLabel
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from controllers.article_controller import ArticleController
from utils.logger import setup_logger
from widgets.enhanced_widgets import (
    EnhancedCard, EnhancedTable, EnhancedForm, EnhancedProgressBar, StatusIndicator
)
from widgets.modern_widgets import ModernStatCard
from styles.theme_manager import get_theme_manager


class ArticleStatsWidget(QWidget):
    """Widget de statistiques des articles"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface des statistiques"""
        layout = QGridLayout()
        layout.setSpacing(15)
        
        # Cartes de statistiques
        self.total_card = ModernStatCard(
            "Total Articles", "0", "📦", "primary"
        )
        self.active_card = ModernStatCard(
            "Articles Actifs", "0", "✅", "success"
        )
        self.low_stock_card = ModernStatCard(
            "Stock Faible", "0", "⚠️", "warning"
        )
        self.value_card = ModernStatCard(
            "Valeur Totale", "€0", "💰", "info"
        )
        
        # Ajouter les cartes à la grille
        layout.addWidget(self.total_card, 0, 0)
        layout.addWidget(self.active_card, 0, 1)
        layout.addWidget(self.low_stock_card, 1, 0)
        layout.addWidget(self.value_card, 1, 1)
        
        self.setLayout(layout)
    
    def setup_animations(self):
        """Configurer les animations"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.animate_update)
        self.update_timer.start(5000)  # Mise à jour toutes les 5 secondes
    
    def update_stats(self, stats: dict):
        """Mettre à jour les statistiques"""
        self.total_card.update_value(str(stats.get('total', 0)), animate=True)
        self.active_card.update_value(str(stats.get('active', 0)), animate=True)
        self.low_stock_card.update_value(str(stats.get('low_stock', 0)), animate=True)
        self.value_card.update_value(f"€{stats.get('total_value', 0):,.2f}", animate=True)
    
    def animate_update(self):
        """Animation de mise à jour périodique"""
        # Simuler une légère variation pour l'animation
        import random
        for card in [self.total_card, self.active_card, self.low_stock_card, self.value_card]:
            card.pulse_animation()


class ArticleFormDialog(QDialog):
    """Dialog de formulaire pour articles"""
    
    article_saved = pyqtSignal(dict)
    
    def __init__(self, article_data=None, parent=None):
        super().__init__(parent)
        self.article_data = article_data
        self.is_edit_mode = article_data is not None
        
        self.setup_dialog()
        self.setup_form()
        
        if self.is_edit_mode:
            self.load_article_data()
    
    def setup_dialog(self):
        """Configurer le dialog"""
        title = "Modifier l'article" if self.is_edit_mode else "Nouvel article"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(500, 600)
        
        # Appliquer le thème
        theme_manager = get_theme_manager()
        current_theme, _ = theme_manager.get_current_theme()
        stylesheet = theme_manager._get_theme_stylesheet(current_theme, theme_manager.current_mode)
        self.setStyleSheet(stylesheet)
    
    def setup_form(self):
        """Configurer le formulaire"""
        layout = QVBoxLayout()
        
        # Titre du dialog
        title_label = QLabel("📦 Gestion des Articles")
        title_label.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Configuration des champs
        fields_config = [
            {
                "name": "nom",
                "label": "Nom de l'article",
                "type": "text",
                "placeholder": "Entrez le nom de l'article",
                "required": True
            },
            {
                "name": "description",
                "label": "Description",
                "type": "textarea",
                "placeholder": "Description détaillée de l'article"
            },
            {
                "name": "prix_unitaire",
                "label": "Prix unitaire (€)",
                "type": "decimal",
                "min": 0.01,
                "max": 999999.99,
                "required": True
            },
            {
                "name": "quantite_stock",
                "label": "Quantité en stock",
                "type": "number",
                "min": 0,
                "max": 999999,
                "required": True
            },
            {
                "name": "seuil_alerte",
                "label": "Seuil d'alerte",
                "type": "number",
                "min": 0,
                "max": 999999
            },
            {
                "name": "categorie",
                "label": "Catégorie",
                "type": "combo",
                "options": ["Électronique", "Vêtements", "Alimentation", "Mobilier", "Autre"]
            },
            {
                "name": "fournisseur",
                "label": "Fournisseur",
                "type": "combo",
                "options": ["Fournisseur A", "Fournisseur B", "Fournisseur C"]
            }
        ]
        
        # Créer le formulaire amélioré
        self.form = EnhancedForm(fields_config)
        self.form.form_submitted.connect(self.on_form_submitted)
        layout.addWidget(self.form)
        
        self.setLayout(layout)
    
    def load_article_data(self):
        """Charger les données de l'article"""
        if self.article_data:
            self.form.load_data(self.article_data)
    
    def on_form_submitted(self, data):
        """Gérer la soumission du formulaire"""
        # Ajouter l'ID si en mode édition
        if self.is_edit_mode and self.article_data:
            data['id'] = self.article_data.get('id')
        
        self.article_saved.emit(data)
        self.accept()


class ArticleSearchWidget(QWidget):
    """Widget de recherche et filtrage avancé"""
    
    search_changed = pyqtSignal(str)
    filter_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_search_timer()
    
    def setup_ui(self):
        """Configurer l'interface de recherche"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Barre de recherche principale
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 Rechercher des articles...")
        self.search_input.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_input)
        
        # Bouton de recherche avancée
        advanced_btn = QPushButton("🔧 Filtres")
        advanced_btn.clicked.connect(self.toggle_advanced_filters)
        search_layout.addWidget(advanced_btn)
        
        layout.addLayout(search_layout)
        
        # Filtres avancés (masqués par défaut)
        self.filters_widget = QFrame()
        self.filters_widget.setVisible(False)
        self.setup_advanced_filters()
        layout.addWidget(self.filters_widget)
        
        self.setLayout(layout)
    
    def setup_advanced_filters(self):
        """Configurer les filtres avancés"""
        layout = QGridLayout()
        
        # Filtre par catégorie
        layout.addWidget(QLabel("Catégorie:"), 0, 0)
        self.category_filter = QComboBox()
        self.category_filter.addItems(["Toutes", "Électronique", "Vêtements", "Alimentation", "Mobilier", "Autre"])
        self.category_filter.currentTextChanged.connect(self.on_filter_changed)
        layout.addWidget(self.category_filter, 0, 1)
        
        # Filtre par stock
        layout.addWidget(QLabel("Stock:"), 0, 2)
        self.stock_filter = QComboBox()
        self.stock_filter.addItems(["Tous", "En stock", "Stock faible", "Rupture"])
        self.stock_filter.currentTextChanged.connect(self.on_filter_changed)
        layout.addWidget(self.stock_filter, 0, 3)
        
        # Filtre par prix
        layout.addWidget(QLabel("Prix min:"), 1, 0)
        self.price_min = QDoubleSpinBox()
        self.price_min.setRange(0, 999999)
        self.price_min.valueChanged.connect(self.on_filter_changed)
        layout.addWidget(self.price_min, 1, 1)
        
        layout.addWidget(QLabel("Prix max:"), 1, 2)
        self.price_max = QDoubleSpinBox()
        self.price_max.setRange(0, 999999)
        self.price_max.setValue(999999)
        self.price_max.valueChanged.connect(self.on_filter_changed)
        layout.addWidget(self.price_max, 1, 3)
        
        self.filters_widget.setLayout(layout)
    
    def setup_search_timer(self):
        """Configurer le timer de recherche"""
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.emit_search)
        
        self.filter_timer = QTimer()
        self.filter_timer.setSingleShot(True)
        self.filter_timer.timeout.connect(self.emit_filters)
    
    def on_search_changed(self):
        """Gérer le changement de recherche"""
        self.search_timer.start(300)  # Délai de 300ms
    
    def on_filter_changed(self):
        """Gérer le changement de filtres"""
        self.filter_timer.start(300)
    
    def emit_search(self):
        """Émettre le signal de recherche"""
        self.search_changed.emit(self.search_input.text())
    
    def emit_filters(self):
        """Émettre le signal de filtres"""
        filters = {
            'category': self.category_filter.currentText(),
            'stock': self.stock_filter.currentText(),
            'price_min': self.price_min.value(),
            'price_max': self.price_max.value()
        }
        self.filter_changed.emit(filters)
    
    def toggle_advanced_filters(self):
        """Basculer l'affichage des filtres avancés"""
        self.filters_widget.setVisible(not self.filters_widget.isVisible())


class EnhancedArticlesWindow(QWidget):
    """Fenêtre de gestion des articles améliorée"""
    
    article_selected = pyqtSignal(dict)
    
    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.controller = ArticleController(app_instance.get_database_manager())
        
        # Variables d'état
        self.current_articles = []
        self.selected_article = None
        self.loading = False
        
        self.setup_ui()
        self.setup_animations()
        self.connect_signals()
        self.load_articles()
    
    def setup_ui(self):
        """Configurer l'interface principale"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête avec titre et actions
        self.create_header(layout)
        
        # Statistiques
        self.stats_widget = ArticleStatsWidget()
        stats_card = EnhancedCard("📊 Statistiques des Articles", self.stats_widget)
        layout.addWidget(stats_card)
        
        # Zone de recherche et filtres
        self.search_widget = ArticleSearchWidget()
        search_card = EnhancedCard("🔍 Recherche et Filtres", self.search_widget)
        layout.addWidget(search_card)
        
        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)
        
        # Table des articles
        self.create_articles_table(splitter)
        
        # Panel de détails
        self.create_details_panel(splitter)
        
        # Proportions du splitter
        splitter.setSizes([700, 300])
        layout.addWidget(splitter)
        
        # Barre de progression
        self.progress_bar = EnhancedProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.setLayout(layout)
    
    def create_header(self, layout):
        """Créer l'en-tête"""
        header_layout = QHBoxLayout()
        
        # Titre
        title = QLabel("📦 Gestion des Articles")
        title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Boutons d'action
        self.add_btn = QPushButton("➕ Nouvel Article")
        self.add_btn.setProperty("class", "primary")
        self.add_btn.clicked.connect(self.add_article)
        header_layout.addWidget(self.add_btn)
        
        self.edit_btn = QPushButton("✏️ Modifier")
        self.edit_btn.setProperty("class", "secondary")
        self.edit_btn.setEnabled(False)
        self.edit_btn.clicked.connect(self.edit_article)
        header_layout.addWidget(self.edit_btn)
        
        self.delete_btn = QPushButton("🗑️ Supprimer")
        self.delete_btn.setProperty("class", "danger")
        self.delete_btn.setEnabled(False)
        self.delete_btn.clicked.connect(self.delete_article)
        header_layout.addWidget(self.delete_btn)
        
        self.refresh_btn = QPushButton("🔄 Actualiser")
        self.refresh_btn.clicked.connect(self.load_articles)
        header_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(header_layout)
    
    def create_articles_table(self, parent):
        """Créer la table des articles"""
        table_widget = QWidget()
        layout = QVBoxLayout()
        
        # Table améliorée
        self.articles_table = EnhancedTable()
        self.articles_table.setColumnCount(7)
        self.articles_table.setHorizontalHeaderLabels([
            "ID", "Nom", "Catégorie", "Prix", "Stock", "Seuil", "Statut"
        ])
        
        # Masquer la colonne ID
        self.articles_table.hideColumn(0)
        
        layout.addWidget(self.articles_table)
        table_widget.setLayout(layout)
        
        # Encapsuler dans une carte
        table_card = EnhancedCard("📋 Liste des Articles", table_widget)
        parent.addWidget(table_card)
    
    def create_details_panel(self, parent):
        """Créer le panel de détails"""
        details_widget = QWidget()
        layout = QVBoxLayout()
        
        # Informations de l'article sélectionné
        self.details_label = QLabel("Sélectionnez un article pour voir les détails")
        self.details_label.setAlignment(Qt.AlignCenter)
        self.details_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.details_label)
        
        # Zone de détails (masquée par défaut)
        self.details_content = QFrame()
        self.details_content.setVisible(False)
        self.setup_details_content()
        layout.addWidget(self.details_content)
        
        layout.addStretch()
        details_widget.setLayout(layout)
        
        # Encapsuler dans une carte
        details_card = EnhancedCard("📄 Détails de l'Article", details_widget)
        parent.addWidget(details_card)
    
    def setup_details_content(self):
        """Configurer le contenu des détails"""
        layout = QVBoxLayout()
        
        # Champs de détails
        self.detail_fields = {}
        fields = [
            ("Nom", "nom"),
            ("Description", "description"),
            ("Prix unitaire", "prix_unitaire"),
            ("Quantité", "quantite_stock"),
            ("Seuil d'alerte", "seuil_alerte"),
            ("Catégorie", "categorie"),
            ("Fournisseur", "fournisseur")
        ]
        
        for label_text, field_name in fields:
            field_layout = QHBoxLayout()
            
            label = QLabel(f"{label_text}:")
            label.setFont(QFont("Segoe UI", 9, QFont.Bold))
            label.setMinimumWidth(100)
            field_layout.addWidget(label)
            
            value_label = QLabel("-")
            value_label.setWordWrap(True)
            field_layout.addWidget(value_label)
            
            self.detail_fields[field_name] = value_label
            layout.addLayout(field_layout)
        
        # Indicateur de statut
        status_layout = QHBoxLayout()
        status_layout.addWidget(QLabel("Statut:"))
        self.status_indicator = StatusIndicator()
        status_layout.addWidget(self.status_indicator)
        status_layout.addStretch()
        layout.addLayout(status_layout)
        
        self.details_content.setLayout(layout)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de chargement
        self.loading_animation = QPropertyAnimation(self.progress_bar, b"value")
        self.loading_animation.setDuration(2000)
        self.loading_animation.setLoopCount(-1)
    
    def connect_signals(self):
        """Connecter les signaux"""
        # Table
        self.articles_table.row_selected.connect(self.on_article_selected)
        
        # Recherche et filtres
        self.search_widget.search_changed.connect(self.on_search_changed)
        self.search_widget.filter_changed.connect(self.on_filter_changed)
    
    def load_articles(self):
        """Charger les articles"""
        self.set_loading(True)
        
        try:
            # Simuler un délai de chargement
            QTimer.singleShot(1000, self._load_articles_data)
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des articles: {e}")
            self.show_error("Erreur de chargement", str(e))
            self.set_loading(False)
    
    def _load_articles_data(self):
        """Charger les données des articles"""
        try:
            # Récupérer les articles via le contrôleur
            articles = self.controller.get_all_articles()
            self.current_articles = articles
            
            # Mettre à jour la table
            self.update_articles_table(articles)
            
            # Mettre à jour les statistiques
            self.update_statistics(articles)
            
            self.set_loading(False)
            self.show_success(f"{len(articles)} articles chargés")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des données: {e}")
            self.show_error("Erreur", str(e))
            self.set_loading(False)
    
    def update_articles_table(self, articles):
        """Mettre à jour la table des articles"""
        self.articles_table.setRowCount(0)
        
        for article in articles:
            row_data = [
                str(article.get('id', '')),
                article.get('nom', ''),
                article.get('categorie', ''),
                f"€{article.get('prix_unitaire', 0):.2f}",
                str(article.get('quantite_stock', 0)),
                str(article.get('seuil_alerte', 0)),
                self.get_stock_status(article)
            ]
            
            self.articles_table.add_data_row(row_data, animate=True)
    
    def get_stock_status(self, article):
        """Obtenir le statut du stock"""
        stock = article.get('quantite_stock', 0)
        seuil = article.get('seuil_alerte', 0)
        
        if stock == 0:
            return "❌ Rupture"
        elif stock <= seuil:
            return "⚠️ Faible"
        else:
            return "✅ Normal"
    
    def update_statistics(self, articles):
        """Mettre à jour les statistiques"""
        total = len(articles)
        active = sum(1 for a in articles if a.get('quantite_stock', 0) > 0)
        low_stock = sum(1 for a in articles if 0 < a.get('quantite_stock', 0) <= a.get('seuil_alerte', 0))
        total_value = sum(a.get('prix_unitaire', 0) * a.get('quantite_stock', 0) for a in articles)
        
        stats = {
            'total': total,
            'active': active,
            'low_stock': low_stock,
            'total_value': total_value
        }
        
        self.stats_widget.update_stats(stats)
    
    def on_article_selected(self, row):
        """Gérer la sélection d'un article"""
        if row < len(self.current_articles):
            self.selected_article = self.current_articles[row]
            self.update_article_details(self.selected_article)
            
            # Activer les boutons
            self.edit_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
            # Émettre le signal
            self.article_selected.emit(self.selected_article)
    
    def update_article_details(self, article):
        """Mettre à jour les détails de l'article"""
        self.details_label.setVisible(False)
        self.details_content.setVisible(True)
        
        # Mettre à jour les champs
        for field_name, label in self.detail_fields.items():
            value = article.get(field_name, '-')
            if field_name == 'prix_unitaire':
                value = f"€{value:.2f}" if isinstance(value, (int, float)) else value
            label.setText(str(value))
        
        # Mettre à jour le statut
        stock = article.get('quantite_stock', 0)
        seuil = article.get('seuil_alerte', 0)
        
        if stock == 0:
            self.status_indicator.set_status("error")
        elif stock <= seuil:
            self.status_indicator.set_status("warning")
        else:
            self.status_indicator.set_status("success")
    
    def add_article(self):
        """Ajouter un nouvel article"""
        dialog = ArticleFormDialog(parent=self)
        dialog.article_saved.connect(self.on_article_saved)
        dialog.exec_()
    
    def edit_article(self):
        """Modifier l'article sélectionné"""
        if not self.selected_article:
            return
        
        dialog = ArticleFormDialog(self.selected_article, parent=self)
        dialog.article_saved.connect(self.on_article_saved)
        dialog.exec_()
    
    def delete_article(self):
        """Supprimer l'article sélectionné"""
        if not self.selected_article:
            return
        
        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer l'article '{self.selected_article.get('nom', '')}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.controller.delete_article(self.selected_article['id'])
                self.show_success("Article supprimé avec succès")
                self.load_articles()
            except Exception as e:
                self.show_error("Erreur de suppression", str(e))
    
    def on_article_saved(self, article_data):
        """Gérer la sauvegarde d'un article"""
        try:
            if 'id' in article_data:
                # Modification
                self.controller.update_article(article_data['id'], article_data)
                self.show_success("Article modifié avec succès")
            else:
                # Création
                self.controller.create_article(article_data)
                self.show_success("Article créé avec succès")
            
            self.load_articles()
            
        except Exception as e:
            self.show_error("Erreur de sauvegarde", str(e))
    
    def on_search_changed(self, search_text):
        """Gérer le changement de recherche"""
        # Filtrer les articles affichés
        filtered_articles = []
        
        for article in self.current_articles:
            if (search_text.lower() in article.get('nom', '').lower() or
                search_text.lower() in article.get('description', '').lower() or
                search_text.lower() in article.get('categorie', '').lower()):
                filtered_articles.append(article)
        
        self.update_articles_table(filtered_articles)
    
    def on_filter_changed(self, filters):
        """Gérer le changement de filtres"""
        # Appliquer les filtres
        filtered_articles = []
        
        for article in self.current_articles:
            # Filtre par catégorie
            if filters['category'] != "Toutes" and article.get('categorie') != filters['category']:
                continue
            
            # Filtre par prix
            prix = article.get('prix_unitaire', 0)
            if prix < filters['price_min'] or prix > filters['price_max']:
                continue
            
            # Filtre par stock
            stock = article.get('quantite_stock', 0)
            seuil = article.get('seuil_alerte', 0)
            
            if filters['stock'] == "En stock" and stock <= 0:
                continue
            elif filters['stock'] == "Stock faible" and (stock <= 0 or stock > seuil):
                continue
            elif filters['stock'] == "Rupture" and stock > 0:
                continue
            
            filtered_articles.append(article)
        
        self.update_articles_table(filtered_articles)
    
    def set_loading(self, loading: bool):
        """Définir l'état de chargement"""
        self.loading = loading
        self.progress_bar.setVisible(loading)
        
        if loading:
            self.progress_bar.set_pulsing(True)
            self.articles_table.set_loading(True)
        else:
            self.progress_bar.set_pulsing(False)
            self.articles_table.set_loading(False)
    
    def show_success(self, message: str):
        """Afficher un message de succès"""
        if FLUENT_AVAILABLE:
            InfoBar.success(
                title="Succès",
                content=message,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
        else:
            QMessageBox.information(self, "Succès", message)
    
    def show_error(self, title: str, message: str):
        """Afficher un message d'erreur"""
        if FLUENT_AVAILABLE:
            InfoBar.error(
                title=title,
                content=message,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self
            )
        else:
            QMessageBox.critical(self, title, message)
