"""
Composants UI avancés avec animations et micro-interactions
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGraphicsDropShadowEffect, QGridLayout, QSizePolicy,
    QScrollArea, QApplication, QGraphicsOpacityEffect
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect,
    QTimer, QParallelAnimationGroup, QSequentialAnimationGroup,
    QAbstractAnimation, QPoint, QSize, QVariantAnimation
)
from PyQt5.QtGui import (
    QFont, QPixmap, QPainter, QColor, QPen, QBrush,
    QPainterPath, QLinearGradient, QRadialGradient, QPalette
)

from styles.modern_theme import ModernTheme


class FloatingActionButton(QPushButton):
    """Bouton d'action flottant avec animations"""
    
    def __init__(self, icon_text: str = "+", parent=None):
        super().__init__(icon_text, parent)
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setFixedSize(56, 56)
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {ModernTheme.PRIMARY_600};
                color: white;
                border: none;
                border-radius: 28px;
                font-size: 24px;
                font-weight: bold;
                box-shadow: {ModernTheme.SHADOW_LG};
            }}
            QPushButton:hover {{
                background-color: {ModernTheme.PRIMARY_700};
                transform: scale(1.1);
            }}
            QPushButton:pressed {{
                background-color: {ModernTheme.PRIMARY_800};
                transform: scale(0.95);
            }}
        """)
        
        # Effet d'ombre
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(20)
        self.shadow_effect.setColor(QColor(ModernTheme.PRIMARY_600))
        self.shadow_effect.setOffset(0, 4)
        self.setGraphicsEffect(self.shadow_effect)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de rotation
        self.rotation_animation = QVariantAnimation()
        self.rotation_animation.setDuration(300)
        self.rotation_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.rotation_animation.valueChanged.connect(self.update_rotation)
        
        # Animation de pulsation
        self.pulse_animation = QPropertyAnimation(self, b"geometry")
        self.pulse_animation.setDuration(1000)
        self.pulse_animation.setEasingCurve(QEasingCurve.InOutSine)
        self.pulse_animation.setLoopCount(-1)
        
        self.rotation_angle = 0
    
    def update_rotation(self, value):
        """Mettre à jour la rotation"""
        self.rotation_angle = value
        self.update()
    
    def animate_click(self):
        """Animer le clic avec rotation"""
        self.rotation_animation.setStartValue(0)
        self.rotation_animation.setEndValue(45)
        self.rotation_animation.start()
    
    def paintEvent(self, event):
        """Dessiner le bouton avec rotation"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Appliquer la rotation
        center = self.rect().center()
        painter.translate(center)
        painter.rotate(self.rotation_angle)
        painter.translate(-center)
        
        # Dessiner le bouton normalement
        super().paintEvent(event)
        
        painter.end()
    
    def mousePressEvent(self, event):
        """Gérer le clic avec animation"""
        if event.button() == Qt.LeftButton:
            self.animate_click()
        super().mousePressEvent(event)


class NotificationToast(QWidget):
    """Toast de notification avec animations d'entrée/sortie"""
    
    closed = pyqtSignal()
    
    def __init__(self, title: str, message: str, toast_type: str = "info", 
                 duration: int = 3000, parent=None):
        super().__init__(parent)
        self.title = title
        self.message = message
        self.toast_type = toast_type
        self.duration = duration
        
        self.setup_ui()
        self.setup_animations()
        self.show_toast()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setFixedSize(350, 80)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Couleurs selon le type
        colors = {
            "info": ModernTheme.INFO_COLOR,
            "success": ModernTheme.SUCCESS_COLOR,
            "warning": ModernTheme.WARNING_COLOR,
            "error": ModernTheme.ERROR_COLOR
        }
        
        accent_color = colors.get(self.toast_type, ModernTheme.INFO_COLOR)
        
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {ModernTheme.LIGHT_BACKGROUND_PRIMARY};
                border: 1px solid {ModernTheme.LIGHT_BORDER_COLOR};
                border-left: 4px solid {accent_color};
                border-radius: 8px;
                box-shadow: {ModernTheme.SHADOW_LG};
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(4)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet(f"""
            font-weight: 600;
            color: {ModernTheme.LIGHT_TEXT_PRIMARY};
            font-size: 14px;
        """)
        layout.addWidget(title_label)
        
        # Message
        message_label = QLabel(self.message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {ModernTheme.LIGHT_TEXT_SECONDARY};
            font-size: 12px;
        """)
        layout.addWidget(message_label)
        
        self.setLayout(layout)
        
        # Effet d'opacité
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation d'entrée (slide + fade)
        self.slide_in_animation = QPropertyAnimation(self, b"pos")
        self.slide_in_animation.setDuration(300)
        self.slide_in_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self.fade_in_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_in_animation.setDuration(300)
        self.fade_in_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Animation de sortie
        self.slide_out_animation = QPropertyAnimation(self, b"pos")
        self.slide_out_animation.setDuration(300)
        self.slide_out_animation.setEasingCurve(QEasingCurve.InCubic)
        
        self.fade_out_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_out_animation.setDuration(300)
        self.fade_out_animation.setEasingCurve(QEasingCurve.InCubic)
        
        # Groupe d'animations d'entrée
        self.enter_group = QParallelAnimationGroup()
        self.enter_group.addAnimation(self.slide_in_animation)
        self.enter_group.addAnimation(self.fade_in_animation)
        
        # Groupe d'animations de sortie
        self.exit_group = QParallelAnimationGroup()
        self.exit_group.addAnimation(self.slide_out_animation)
        self.exit_group.addAnimation(self.fade_out_animation)
        self.exit_group.finished.connect(self.close)
        
        # Timer pour fermeture automatique
        self.auto_close_timer = QTimer()
        self.auto_close_timer.timeout.connect(self.hide_toast)
        self.auto_close_timer.setSingleShot(True)
    
    def show_toast(self):
        """Afficher le toast avec animation"""
        # Position initiale (hors écran à droite)
        screen = QApplication.desktop().screenGeometry()
        start_pos = QPoint(screen.width(), 50)
        end_pos = QPoint(screen.width() - self.width() - 20, 50)
        
        self.move(start_pos)
        self.show()
        
        # Configurer les animations d'entrée
        self.slide_in_animation.setStartValue(start_pos)
        self.slide_in_animation.setEndValue(end_pos)
        
        self.fade_in_animation.setStartValue(0.0)
        self.fade_in_animation.setEndValue(1.0)
        
        # Démarrer l'animation d'entrée
        self.enter_group.start()
        
        # Démarrer le timer de fermeture automatique
        if self.duration > 0:
            self.auto_close_timer.start(self.duration)
    
    def hide_toast(self):
        """Masquer le toast avec animation"""
        # Position finale (hors écran à droite)
        screen = QApplication.desktop().screenGeometry()
        current_pos = self.pos()
        end_pos = QPoint(screen.width(), current_pos.y())
        
        # Configurer les animations de sortie
        self.slide_out_animation.setStartValue(current_pos)
        self.slide_out_animation.setEndValue(end_pos)
        
        self.fade_out_animation.setStartValue(1.0)
        self.fade_out_animation.setEndValue(0.0)
        
        # Démarrer l'animation de sortie
        self.exit_group.start()
    
    def mousePressEvent(self, event):
        """Fermer au clic"""
        if event.button() == Qt.LeftButton:
            self.auto_close_timer.stop()
            self.hide_toast()
        super().mousePressEvent(event)


class LoadingSpinner(QWidget):
    """Spinner de chargement animé"""
    
    def __init__(self, size: int = 32, parent=None):
        super().__init__(parent)
        self.size = size
        self.angle = 0
        
        self.setup_ui()
        self.setup_animation()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setFixedSize(self.size, self.size)
        self.setAttribute(Qt.WA_TranslucentBackground)
    
    def setup_animation(self):
        """Configurer l'animation de rotation"""
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self.rotate)
        self.rotation_timer.start(50)  # 20 FPS
    
    def rotate(self):
        """Faire tourner le spinner"""
        self.angle = (self.angle + 10) % 360
        self.update()
    
    def paintEvent(self, event):
        """Dessiner le spinner"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Centre du widget
        center = self.rect().center()
        radius = self.size // 2 - 4
        
        # Dessiner les segments du spinner
        painter.translate(center)
        painter.rotate(self.angle)
        
        for i in range(8):
            # Opacité décroissante pour l'effet de traînée
            opacity = 1.0 - (i * 0.125)
            color = QColor(ModernTheme.PRIMARY_600)
            color.setAlphaF(opacity)
            
            painter.setPen(QPen(color, 3, Qt.RoundCap))
            painter.drawLine(0, -radius + 2, 0, -radius + 8)
            painter.rotate(45)
        
        painter.end()
    
    def start(self):
        """Démarrer l'animation"""
        self.rotation_timer.start(50)
        self.show()
    
    def stop(self):
        """Arrêter l'animation"""
        self.rotation_timer.stop()
        self.hide()
