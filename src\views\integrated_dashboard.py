"""
Dashboard Intégré - GSlim
Interface moderne intégrant tous les modules avec widgets avancés
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QSplitter, QTabWidget, QGroupBox, QProgressBar,
    QSpacerItem, QSizePolicy, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QColor, QPalette

try:
    from qfluentwidgets import FluentIcon, InfoBar, InfoBarPosition
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger
from widgets.enhanced_widgets import <PERSON>hancedCard, EnhancedProgressBar, StatusIndicator
from widgets.modern_widgets import ModernStatCard
from styles.theme_manager import get_theme_manager


class IntegratedDashboard(QWidget):
    """Dashboard intégré avec tous les modules"""
    
    module_requested = pyqtSignal(str)
    action_requested = pyqtSignal(str)
    
    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        
        self.setup_ui()
        self.setup_timers()
        self.load_initial_data()
    
    def setup_ui(self):
        """Configurer l'interface principale"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        self.create_header(layout)
        
        # Statistiques principales
        self.create_main_stats(layout)
        
        # Contenu principal avec onglets
        self.create_main_content(layout)
        
        self.setLayout(layout)
    
    def create_header(self, layout):
        """Créer l'en-tête du dashboard"""
        header_layout = QHBoxLayout()
        
        # Titre et informations
        title_layout = QVBoxLayout()
        
        title = QLabel("🏠 Tableau de Bord Intégré")
        title.setFont(QFont("Segoe UI", 20, QFont.Bold))
        title_layout.addWidget(title)
        
        subtitle = QLabel(f"GSlim - Interface Moderne - {datetime.now().strftime('%d/%m/%Y %H:%M')}")
        subtitle.setStyleSheet("color: #666; font-size: 14px;")
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Boutons d'action
        self.refresh_btn = QPushButton("🔄 Actualiser")
        self.refresh_btn.setProperty("class", "primary")
        self.refresh_btn.clicked.connect(self.refresh_all_data)
        header_layout.addWidget(self.refresh_btn)
        
        self.theme_btn = QPushButton("🎨 Thème")
        self.theme_btn.setProperty("class", "secondary")
        self.theme_btn.clicked.connect(self.change_theme)
        header_layout.addWidget(self.theme_btn)
        
        layout.addLayout(header_layout)
    
    def create_main_stats(self, layout):
        """Créer les statistiques principales"""
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)
        
        # Cartes de statistiques
        self.articles_card = ModernStatCard("Articles", "0", "📦", "primary")
        self.sales_card = ModernStatCard("Ventes", "€0", "💰", "success")
        self.orders_card = ModernStatCard("Commandes", "0", "📋", "info")
        self.alerts_card = ModernStatCard("Alertes", "0", "⚠️", "warning")
        
        # Connecter les clics
        self.articles_card.clicked.connect(lambda: self.module_requested.emit("articles"))
        self.sales_card.clicked.connect(lambda: self.module_requested.emit("sales"))
        self.orders_card.clicked.connect(lambda: self.module_requested.emit("orders"))
        self.alerts_card.clicked.connect(lambda: self.module_requested.emit("alerts"))
        
        # Ajouter à la grille
        stats_layout.addWidget(self.articles_card, 0, 0)
        stats_layout.addWidget(self.sales_card, 0, 1)
        stats_layout.addWidget(self.orders_card, 0, 2)
        stats_layout.addWidget(self.alerts_card, 0, 3)
        
        # Encapsuler dans une carte
        stats_widget = QWidget()
        stats_widget.setLayout(stats_layout)
        stats_card = EnhancedCard("📊 Statistiques Principales", stats_widget)
        
        layout.addWidget(stats_card)
    
    def create_main_content(self, layout):
        """Créer le contenu principal avec onglets"""
        tabs = QTabWidget()
        
        # Onglet Modules
        modules_tab = self.create_modules_tab()
        tabs.addTab(modules_tab, "🧩 Modules")
        
        # Onglet Actions Rapides
        actions_tab = self.create_actions_tab()
        tabs.addTab(actions_tab, "⚡ Actions")
        
        # Onglet Système
        system_tab = self.create_system_tab()
        tabs.addTab(system_tab, "🖥️ Système")
        
        # Onglet Activité
        activity_tab = self.create_activity_tab()
        tabs.addTab(activity_tab, "📋 Activité")
        
        layout.addWidget(tabs)
    
    def create_modules_tab(self):
        """Créer l'onglet des modules"""
        widget = QWidget()
        layout = QGridLayout()
        layout.setSpacing(15)
        
        # Modules disponibles
        modules = [
            ("Articles", "📦", "Gestion des articles et stock", "articles"),
            ("Fournisseurs", "🏢", "Gestion des fournisseurs", "suppliers"),
            ("Ventes", "💰", "Gestion des ventes", "sales"),
            ("Commandes", "📋", "Gestion des commandes", "orders"),
            ("Mouvements", "📈", "Mouvements de stock", "movements"),
            ("Rapports", "📊", "Rapports et analyses", "reports")
        ]
        
        for i, (name, icon, desc, module_id) in enumerate(modules):
            module_card = self.create_module_card(name, icon, desc, module_id)
            row = i // 3
            col = i % 3
            layout.addWidget(module_card, row, col)
        
        widget.setLayout(layout)
        return widget
    
    def create_module_card(self, name, icon, description, module_id):
        """Créer une carte de module"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setProperty("class", "module-card")
        card.setCursor(Qt.PointingHandCursor)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI", 32))
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # Nom
        name_label = QLabel(name)
        name_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        name_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(name_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setFont(QFont("Segoe UI", 10))
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666;")
        layout.addWidget(desc_label)
        
        # Bouton d'accès
        access_btn = QPushButton("Ouvrir")
        access_btn.setProperty("class", "primary")
        access_btn.clicked.connect(lambda: self.module_requested.emit(module_id))
        layout.addWidget(access_btn)
        
        card.setLayout(layout)
        return card
    
    def create_actions_tab(self):
        """Créer l'onglet des actions rapides"""
        widget = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        # Actions rapides
        actions_layout = QGridLayout()
        actions_layout.setSpacing(10)
        
        actions = [
            ("Nouvel Article", "📦", "add_article", "primary"),
            ("Nouvelle Vente", "💰", "new_sale", "success"),
            ("Nouveau Fournisseur", "🏢", "add_supplier", "info"),
            ("Rapport Rapide", "📊", "quick_report", "warning"),
            ("Sauvegarde", "💾", "backup", "secondary"),
            ("Paramètres", "⚙️", "settings", "secondary")
        ]
        
        for i, (text, icon, action, btn_type) in enumerate(actions):
            btn = QPushButton(f"{icon} {text}")
            btn.setProperty("class", btn_type)
            btn.setMinimumHeight(50)
            btn.clicked.connect(lambda checked, a=action: self.action_requested.emit(a))
            
            row = i // 2
            col = i % 2
            actions_layout.addWidget(btn, row, col)
        
        layout.addLayout(actions_layout)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def create_system_tab(self):
        """Créer l'onglet système"""
        widget = QWidget()
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        # Statut système
        status_group = QGroupBox("Statut du Système")
        status_layout = QGridLayout()
        
        # Indicateurs
        status_layout.addWidget(QLabel("Base de données:"), 0, 0)
        self.db_status = StatusIndicator("success")
        status_layout.addWidget(self.db_status, 0, 1)
        status_layout.addWidget(QLabel("Connectée"), 0, 2)
        
        status_layout.addWidget(QLabel("Serveur:"), 1, 0)
        self.server_status = StatusIndicator("success")
        status_layout.addWidget(self.server_status, 1, 1)
        status_layout.addWidget(QLabel("En ligne"), 1, 2)
        
        status_layout.addWidget(QLabel("Synchronisation:"), 2, 0)
        self.sync_status = StatusIndicator("info")
        status_layout.addWidget(self.sync_status, 2, 1)
        status_layout.addWidget(QLabel("À jour"), 2, 2)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # Performance
        perf_group = QGroupBox("Performance")
        perf_layout = QVBoxLayout()
        
        perf_layout.addWidget(QLabel("Performance système:"))
        self.performance_bar = EnhancedProgressBar()
        self.performance_bar.setValue(85)
        self.performance_bar.setFormat("85% - Excellent")
        perf_layout.addWidget(self.performance_bar)
        
        perf_group.setLayout(perf_layout)
        layout.addWidget(perf_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_activity_tab(self):
        """Créer l'onglet d'activité"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Zone d'activité avec scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        self.activity_widget = QWidget()
        self.activity_layout = QVBoxLayout(self.activity_widget)
        self.activity_layout.setSpacing(5)
        
        # Ajouter quelques activités initiales
        self.add_activity("📦", "Article 'Laptop Dell' ajouté au stock", "Il y a 2 minutes")
        self.add_activity("💰", "Vente de 3 articles réalisée", "Il y a 5 minutes")
        self.add_activity("📊", "Rapport mensuel généré", "Il y a 15 minutes")
        self.add_activity("⚠️", "Stock faible détecté pour 'Souris USB'", "Il y a 30 minutes")
        
        scroll_area.setWidget(self.activity_widget)
        layout.addWidget(scroll_area)
        
        widget.setLayout(layout)
        return widget
    
    def add_activity(self, icon, text, time):
        """Ajouter une activité à la liste"""
        activity_frame = QFrame()
        activity_frame.setFrameStyle(QFrame.StyledPanel)
        activity_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                padding: 10px;
                margin: 3px;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setContentsMargins(10, 8, 10, 8)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI", 16))
        layout.addWidget(icon_label)
        
        # Texte et heure
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)
        
        text_label = QLabel(text)
        text_label.setFont(QFont("Segoe UI", 10))
        text_layout.addWidget(text_label)
        
        time_label = QLabel(time)
        time_label.setFont(QFont("Segoe UI", 9))
        time_label.setStyleSheet("color: #888;")
        text_layout.addWidget(time_label)
        
        layout.addLayout(text_layout)
        layout.addStretch()
        
        activity_frame.setLayout(layout)
        self.activity_layout.insertWidget(0, activity_frame)
    
    def setup_timers(self):
        """Configurer les timers"""
        # Timer de mise à jour des données
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.update_data)
        self.data_timer.start(30000)  # 30 secondes
        
        # Timer d'activité
        self.activity_timer = QTimer()
        self.activity_timer.timeout.connect(self.add_random_activity)
        self.activity_timer.start(15000)  # 15 secondes
    
    def load_initial_data(self):
        """Charger les données initiales"""
        self.update_data()
    
    def update_data(self):
        """Mettre à jour les données"""
        import random
        
        # Simuler des données
        articles = random.randint(100, 500)
        sales = random.randint(1000, 5000)
        orders = random.randint(10, 50)
        alerts = random.randint(0, 10)
        
        # Mettre à jour les cartes
        self.articles_card.update_value(str(articles), animate=True)
        self.sales_card.update_value(f"€{sales:,}", animate=True)
        self.orders_card.update_value(str(orders), animate=True)
        self.alerts_card.update_value(str(alerts), animate=True)
        
        # Mettre à jour la performance
        performance = random.randint(75, 95)
        self.performance_bar.animate_to_value(performance)
        
        status_text = "Excellent" if performance >= 90 else "Bon" if performance >= 75 else "Moyen"
        self.performance_bar.setFormat(f"{performance}% - {status_text}")
    
    def add_random_activity(self):
        """Ajouter une activité aléatoire"""
        import random
        
        activities = [
            ("📦", "Nouvel article ajouté au stock", "À l'instant"),
            ("💰", "Vente réalisée avec succès", "À l'instant"),
            ("⚠️", "Stock faible détecté", "À l'instant"),
            ("🔄", "Synchronisation automatique", "À l'instant"),
            ("👤", "Nouveau fournisseur ajouté", "À l'instant"),
            ("📊", "Rapport généré automatiquement", "À l'instant")
        ]
        
        icon, text, time = random.choice(activities)
        self.add_activity(icon, text, time)
    
    def refresh_all_data(self):
        """Actualiser toutes les données"""
        self.update_data()
        self.show_message("Données actualisées", "success")
    
    def change_theme(self):
        """Changer le thème"""
        from widgets.theme_selector import show_theme_selector
        if show_theme_selector(self):
            self.show_message("Thème changé", "success")
    
    def show_message(self, message, msg_type="info"):
        """Afficher un message"""
        if FLUENT_AVAILABLE:
            if msg_type == "success":
                InfoBar.success(
                    title="Succès",
                    content=message,
                    position=InfoBarPosition.TOP,
                    duration=2000,
                    parent=self
                )
            else:
                InfoBar.info(
                    title="Information",
                    content=message,
                    position=InfoBarPosition.TOP,
                    duration=2000,
                    parent=self
                )
        else:
            QMessageBox.information(self, "Information", message)
