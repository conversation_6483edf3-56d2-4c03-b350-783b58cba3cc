#!/usr/bin/env python3
"""
Lanceur de Démonstrations GSlim
Interface pour tester tous les thèmes et composants
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QFrame
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from views.unified_demo import UnifiedDemoWindow
from views.cyberpunk_dashboard import CyberpunkDashboard
from views.fluent_dashboard import FluentDashboard
from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode


class DemoLauncher(QMainWindow):
    """Lanceur principal des démonstrations"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎨 GSlim - Lanceur de Démonstrations")
        self.setGeometry(200, 200, 600, 400)
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurer l'interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # Titre
        title = QLabel("🎨 GSlim - Démonstrations des Thèmes")
        title.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("Choisissez une démonstration à lancer")
        subtitle.setFont(QFont("Segoe UI", 14))
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("color: #666; margin-bottom: 20px;")
        layout.addWidget(subtitle)
        
        # Boutons de démonstration
        demos = [
            ("🎨 Démonstration Unifiée", "Tous les thèmes dans une interface", self.launch_unified_demo),
            ("🚀 Interface Cyberpunk", "Thème futuriste avec néons et effets", self.launch_cyberpunk_demo),
            ("🌊 Interface Fluent", "Microsoft Fluent Design System", self.launch_fluent_demo),
            ("💼 Interface Professionnelle", "Design business élégant", self.launch_professional_demo),
            ("✨ Interface Moderne", "Design moderne avec dégradés", self.launch_modern_demo)
        ]
        
        for title_text, desc, callback in demos:
            demo_frame = QFrame()
            demo_frame.setFrameStyle(QFrame.StyledPanel)
            demo_frame.setStyleSheet("""
                QFrame {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 15px;
                }
                QFrame:hover {
                    background-color: #e9ecef;
                    border-color: #0078d4;
                }
            """)
            
            demo_layout = QHBoxLayout()
            demo_layout.setContentsMargins(10, 10, 10, 10)
            
            # Informations
            info_layout = QVBoxLayout()
            
            demo_title = QLabel(title_text)
            demo_title.setFont(QFont("Segoe UI", 14, QFont.Bold))
            info_layout.addWidget(demo_title)
            
            demo_desc = QLabel(desc)
            demo_desc.setStyleSheet("color: #666;")
            info_layout.addWidget(demo_desc)
            
            demo_layout.addLayout(info_layout)
            demo_layout.addStretch()
            
            # Bouton de lancement
            launch_btn = QPushButton("🚀 Lancer")
            launch_btn.setFixedSize(100, 40)
            launch_btn.clicked.connect(callback)
            launch_btn.setStyleSheet("""
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """)
            demo_layout.addWidget(launch_btn)
            
            demo_frame.setLayout(demo_layout)
            layout.addWidget(demo_frame)
        
        central_widget.setLayout(layout)
    
    def launch_unified_demo(self):
        """Lancer la démonstration unifiée"""
        self.unified_window = UnifiedDemoWindow()
        self.unified_window.show()
    
    def launch_cyberpunk_demo(self):
        """Lancer la démonstration cyberpunk"""
        theme_manager = get_theme_manager()
        theme_manager.set_theme(ThemeType.CYBERPUNK, ThemeMode.DARK)
        
        self.cyberpunk_window = CyberpunkDashboard()
        self.cyberpunk_window.show()
    
    def launch_fluent_demo(self):
        """Lancer la démonstration Fluent"""
        theme_manager = get_theme_manager()
        theme_manager.set_theme(ThemeType.FLUENT, ThemeMode.DARK)
        
        try:
            self.fluent_window = FluentDashboard()
            self.fluent_window.show()
        except Exception as e:
            print(f"Erreur Fluent: {e}")
            self.launch_unified_demo()
    
    def launch_professional_demo(self):
        """Lancer la démonstration professionnelle"""
        theme_manager = get_theme_manager()
        theme_manager.set_theme(ThemeType.PROFESSIONAL, ThemeMode.DARK)
        
        self.launch_unified_demo()
    
    def launch_modern_demo(self):
        """Lancer la démonstration moderne"""
        theme_manager = get_theme_manager()
        theme_manager.set_theme(ThemeType.MODERN, ThemeMode.DARK)
        
        self.launch_unified_demo()


def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("GSlim Demo Launcher")
    app.setApplicationVersion("1.0.0")
    
    # Style de base
    app.setStyleSheet("""
        QWidget {
            font-family: 'Segoe UI', Arial, sans-serif;
        }
    """)
    
    # Créer et afficher le lanceur
    launcher = DemoLauncher()
    launcher.show()
    
    print("🚀 Lanceur de démonstrations GSlim démarré")
    print("💡 Choisissez une démonstration à tester")
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
