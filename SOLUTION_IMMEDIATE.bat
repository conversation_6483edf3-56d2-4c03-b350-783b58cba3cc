@echo off
echo.
echo ========================================
echo   🚀 SOLUTION IMMÉDIATE POUR POWERSHELL
echo ========================================
echo.
echo ❌ PROBLÈME IDENTIFIÉ:
echo    PowerShell bloque l'exécution des scripts
echo.
echo ✅ SOLUTION IMMÉDIATE:
echo.

REM Vérifier si l'environnement virtuel existe
if not exist "venv\Scripts\activate.bat" (
    echo ❌ Environnement virtuel non trouvé
    echo 💡 Création de l'environnement virtuel...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Erreur lors de la création de l'environnement
        pause
        exit /b 1
    )
    echo ✅ Environnement virtuel créé
)

echo 🔄 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

echo.
echo ✅ ENVIRONNEMENT VIRTUEL ACTIVÉ !
echo.
echo 📋 Vérification:
python --version
echo.

echo 🔍 Vérification des packages PyQt5...
pip show PyQt5 >nul 2>&1
if errorlevel 1 (
    echo ⚠️ PyQt5 non installé, installation en cours...
    pip install -r requirements.txt
) else (
    echo ✅ PyQt5 installé
)

echo.
echo 🎉 TOUT EST PRÊT !
echo.
echo 🚀 LANCEMENT DE GSLIM...
echo.
echo 🔑 Connexion par défaut:
echo    Utilisateur: admin
echo    Mot de passe: admin123
echo.

python main.py

echo.
echo 📋 Application fermée
pause
