#!/usr/bin/env python3
"""
GSlim - Application de Gestion de Stock
Point d'entrée principal de l'application
"""

import sys
from pathlib import Path

# Ajouter le répertoire src au path Python
sys.path.insert(0, str(Path(__file__).parent / "src"))

from app import GSlimApp

def main():
    """Point d'entrée principal de l'application"""
    try:
        app = GSlimApp()
        app.run()
    except Exception as e:
        print(f"Erreur lors du démarrage de l'application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
