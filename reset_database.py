#!/usr/bin/env python3
"""
Réinitialisation complète de la base de données GSlim
Supprime et recrée la base de données avec des données d'exemple
"""

import os
import sqlite3
from pathlib import Path
from datetime import datetime


def reset_database():
    """Réinitialiser complètement la base de données"""
    print("🔄 RÉINITIALISATION DE LA BASE DE DONNÉES")
    print("="*50)
    
    db_path = "data/gslim.db"
    
    # Supprimer l'ancienne base de données si elle existe
    if os.path.exists(db_path):
        os.remove(db_path)
        print("✅ Ancienne base de données supprimée")
    
    # Créer le répertoire data s'il n'existe pas
    data_dir = Path(db_path).parent
    data_dir.mkdir(exist_ok=True)
    print("✅ Répertoire data créé")
    
    # Créer la nouvelle base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    print("✅ Nouvelle base de données créée")
    
    # Créer les tables
    print("\n📋 Création des tables...")
    
    # Table suppliers
    cursor.execute("""
        CREATE TABLE suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            contact TEXT,
            telephone TEXT,
            email TEXT,
            adresse TEXT,
            date_creation TEXT,
            date_modification TEXT
        )
    """)
    print("✅ Table suppliers créée")
    
    # Table categories
    cursor.execute("""
        CREATE TABLE categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT
        )
    """)
    print("✅ Table categories créée")
    
    # Table articles
    cursor.execute("""
        CREATE TABLE articles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            description TEXT,
            prix_unitaire REAL NOT NULL,
            quantite_stock INTEGER NOT NULL,
            seuil_alerte INTEGER DEFAULT 0,
            categorie_id INTEGER,
            fournisseur_id INTEGER,
            date_creation TEXT,
            date_modification TEXT,
            FOREIGN KEY (categorie_id) REFERENCES categories (id),
            FOREIGN KEY (fournisseur_id) REFERENCES suppliers (id)
        )
    """)
    print("✅ Table articles créée")
    
    # Table sales
    cursor.execute("""
        CREATE TABLE sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            article_id INTEGER,
            quantite INTEGER NOT NULL,
            prix_unitaire REAL NOT NULL,
            total REAL NOT NULL,
            date_vente TEXT,
            FOREIGN KEY (article_id) REFERENCES articles (id)
        )
    """)
    print("✅ Table sales créée")
    
    # Table orders
    cursor.execute("""
        CREATE TABLE orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER,
            statut TEXT DEFAULT 'en_attente',
            date_commande TEXT,
            date_livraison TEXT,
            total REAL,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
    """)
    print("✅ Table orders créée")
    
    # Table movements
    cursor.execute("""
        CREATE TABLE movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            article_id INTEGER,
            type_mouvement TEXT NOT NULL,
            quantite INTEGER NOT NULL,
            date_mouvement TEXT,
            commentaire TEXT,
            FOREIGN KEY (article_id) REFERENCES articles (id)
        )
    """)
    print("✅ Table movements créée")
    
    # Insérer des données d'exemple
    print("\n📊 Insertion des données d'exemple...")
    
    now = datetime.now().isoformat()
    
    # Fournisseurs d'exemple
    suppliers_data = [
        ("TechSupply Pro", "Jean Dupont", "01-23-45-67-89", "<EMAIL>", "123 Rue de la Technologie, Paris", now, now),
        ("Bureau Plus", "Marie Martin", "01-98-76-54-32", "<EMAIL>", "456 Avenue du Bureau, Lyon", now, now),
        ("Informatique Solutions", "Pierre Durand", "01-11-22-33-44", "<EMAIL>", "789 Boulevard Informatique, Marseille", now, now),
        ("Office Supplies", "Sophie Leroy", "01-55-66-77-88", "<EMAIL>", "321 Rue des Fournitures, Toulouse", now, now),
        ("Tech World", "Michel Bernard", "01-44-55-66-77", "<EMAIL>", "654 Avenue Tech, Nice", now, now)
    ]
    
    for supplier in suppliers_data:
        cursor.execute("""
            INSERT INTO suppliers (nom, contact, telephone, email, adresse, date_creation, date_modification)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, supplier)
    
    print("✅ 5 fournisseurs ajoutés")
    
    # Catégories d'exemple
    categories_data = [
        ("Électronique", "Appareils et composants électroniques"),
        ("Bureau", "Fournitures et mobilier de bureau"),
        ("Informatique", "Matériel et logiciels informatiques"),
        ("Consommables", "Produits consommables et jetables"),
        ("Mobilier", "Meubles et équipements")
    ]
    
    for category in categories_data:
        cursor.execute("INSERT INTO categories (name, description) VALUES (?, ?)", category)
    
    print("✅ 5 catégories ajoutées")
    
    # Articles d'exemple
    articles_data = [
        ("Ordinateur Portable Dell", "Laptop 15 pouces, 8GB RAM, 256GB SSD", 899.99, 15, 5, 3, 1, now, now),
        ("Souris Optique Logitech", "Souris USB optique sans fil", 25.50, 50, 10, 3, 1, now, now),
        ("Clavier Mécanique Gaming", "Clavier gaming RGB rétroéclairé", 129.99, 20, 5, 3, 1, now, now),
        ("Écran 24 pouces Samsung", "Moniteur Full HD IPS", 199.99, 8, 3, 1, 2, now, now),
        ("Stylos Bille Bic", "Lot de 10 stylos bille bleus", 5.99, 100, 20, 2, 4, now, now),
        ("Ramette Papier A4", "500 feuilles papier blanc 80g", 8.50, 75, 15, 4, 4, now, now),
        ("Chaise de Bureau", "Chaise ergonomique avec accoudoirs", 159.99, 12, 3, 5, 2, now, now),
        ("Imprimante Laser HP", "Imprimante laser noir et blanc", 189.99, 6, 2, 3, 3, now, now),
        ("Disque Dur Externe", "1TB USB 3.0 portable", 79.99, 25, 5, 3, 1, now, now),
        ("Cahier Spirale", "Cahier 200 pages grands carreaux", 3.50, 80, 20, 2, 4, now, now)
    ]
    
    for article in articles_data:
        cursor.execute("""
            INSERT INTO articles (
                nom, description, prix_unitaire, quantite_stock, 
                seuil_alerte, categorie_id, fournisseur_id, 
                date_creation, date_modification
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, article)
    
    print("✅ 10 articles ajoutés")
    
    # Quelques ventes d'exemple
    sales_data = [
        (1, 2, 899.99, 1799.98, now),  # 2 ordinateurs
        (2, 5, 25.50, 127.50, now),   # 5 souris
        (5, 10, 5.99, 59.90, now),    # 10 lots de stylos
        (6, 3, 8.50, 25.50, now),     # 3 ramettes
        (3, 1, 129.99, 129.99, now)   # 1 clavier
    ]
    
    for sale in sales_data:
        cursor.execute("""
            INSERT INTO sales (article_id, quantite, prix_unitaire, total, date_vente)
            VALUES (?, ?, ?, ?, ?)
        """, sale)
    
    print("✅ 5 ventes ajoutées")
    
    # Quelques mouvements de stock
    movements_data = [
        (1, "entrée", 20, now, "Réception commande fournisseur"),
        (2, "entrée", 100, now, "Stock initial"),
        (1, "sortie", 5, now, "Vente client"),
        (3, "entrée", 25, now, "Réception commande"),
        (5, "sortie", 10, now, "Vente en lot")
    ]
    
    for movement in movements_data:
        cursor.execute("""
            INSERT INTO movements (article_id, type_mouvement, quantite, date_mouvement, commentaire)
            VALUES (?, ?, ?, ?, ?)
        """, movement)
    
    print("✅ 5 mouvements de stock ajoutés")
    
    # Valider toutes les modifications
    conn.commit()
    conn.close()
    
    print(f"\n🎉 BASE DE DONNÉES RÉINITIALISÉE AVEC SUCCÈS !")
    print(f"📍 Emplacement: {os.path.abspath(db_path)}")
    print("\n📊 Données créées:")
    print("   ✅ 5 fournisseurs")
    print("   ✅ 5 catégories")
    print("   ✅ 10 articles")
    print("   ✅ 5 ventes")
    print("   ✅ 5 mouvements de stock")
    
    print("\n🚀 Vous pouvez maintenant relancer l'application !")
    print("   python launch_enhanced.py")
    
    return True


def main():
    """Fonction principale"""
    try:
        reset_database()
        return 0
    except Exception as e:
        print(f"💥 Erreur lors de la réinitialisation: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
