2025-07-31 20:40:04 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 20:41:08 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 20:43:54 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 21:06:14 - ArticleController - ERROR - Erreur lors de la création dans articles: table articles has no column named category_name
2025-07-31 21:06:14 - SupplierController - ERROR - Erreur lors de la création dans suppliers: table suppliers has no column named company_name
2025-07-31 21:06:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: no such column: quantity_in_stock
2025-07-31 21:11:57 - ArticleController - ERROR - Erreur lors de la création dans articles: Error binding parameter 16: type 'decimal.Decimal' is not supported
2025-07-31 21:18:31 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-07-31 21:56:49 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 16:42:36 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 16:42:45 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 16:45:11 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'PACKAGE'
2025-08-01 17:27:55 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: type object 'FluentIcon' has no attribute 'SIGN_OUT'
2025-08-01 17:30:43 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 17:32:07 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 17:32:12 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 17:32:12 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:06:07 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: Can't instantiate abstract class StockMovementController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:12 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: Can't instantiate abstract class StockMovementController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:18 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: Can't instantiate abstract class StockMovementController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:19 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: Can't instantiate abstract class OrderController without an implementation for abstract method 'get_table_name'
2025-08-01 18:06:19 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: Can't instantiate abstract class ReportController without an implementation for abstract method 'get_table_name'
2025-08-01 18:09:07 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:14 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:18 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:09:19 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:09:22 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:24 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:09:24 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:09:25 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:25 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:26 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:28 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:09:33 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:27 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:31 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:35 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:36 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:39 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:10:40 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:41 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:10:42 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:10:43 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:10:44 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: BaseController.__init__() takes 2 positional arguments but 3 were given
2025-08-01 18:13:51 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-01 18:14:13 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:14:13 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:14:18 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:14:18 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:14:19 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:14:19 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:14:20 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:14:21 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:14:23 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:27:37 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-01 18:27:57 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:27:58 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:27:59 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:27:59 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:28:00 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:28:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:28:00 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:28:01 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:28:01 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:28:02 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:29:45 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-01 18:29:53 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:29:54 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:29:56 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:29:56 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:29:57 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:29:57 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:29:58 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:29:58 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:29:59 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:29:59 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: 'ComboBox' object has no attribute 'setEditable'
2025-08-01 18:30:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'get_cursor'
2025-08-01 18:30:00 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_apply_filters'
2025-08-01 18:30:01 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:42:47 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:47 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:42:55 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 18:42:57 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:43:00 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:01 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 18:43:02 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-01 18:43:02 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 18:43:02 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 18:43:03 - views.main_window - ERROR - Erreur lors de l'affichage du module mouvements: wrapped C/C++ object of type MovementsWindow has been deleted
2025-08-01 18:43:03 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: wrapped C/C++ object of type SuppliersWindow has been deleted
2025-08-01 18:43:04 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: wrapped C/C++ object of type ArticlesWindow has been deleted
2025-08-01 18:43:05 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: wrapped C/C++ object of type DashboardWindow has been deleted
2025-08-01 19:30:28 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:28 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:31 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:33 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:34 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:35 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:35 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:35 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:36 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:30:36 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:30:37 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:31:09 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:31:09 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:31:11 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:32:27 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:27 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:33 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:39 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:43 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:43 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:32:45 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:32:59 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:32:59 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:40 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:48 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:53 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:33:55 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:00 - SupplierController - ERROR - Erreur lors de la vérification d'email: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:02 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:11 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:15 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:18 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:34:27 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:35:00 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:32 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors de la récupération des top fournisseurs: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:36 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:45 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:45 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:36:47 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:36:48 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:36:48 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:36:49 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:39:16 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:16 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:19 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:20 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:21 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:39:21 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:39:22 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:39:23 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:23 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:26 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:27 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:27 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:41:29 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:30 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:41:31 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:21 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:22 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:23 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:23 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:23 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:24 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:24 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:43:26 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:28 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:43:30 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - SupplierController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:04 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:07 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:09 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:09 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 19:45:11 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:13 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 19:45:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:21:53 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:21:57 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:21:58 - ArticleController - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:01 - SupplierController - ERROR - Erreur lors du calcul des statistiques fournisseur: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:22:11 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:22:13 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:27:48 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:28:04 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:28:05 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:28:09 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: 'DashboardWindow' object has no attribute 'stats_data'
2025-08-01 20:30:33 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:30:33 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:30:33 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:30:34 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: name 'QSpacerItem' is not defined
2025-08-01 20:33:16 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:33:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:33:16 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:33:16 - views.main_window - ERROR - Erreur lors de l'affichage du module tableau de bord: name 'QSpacerItem' is not defined
2025-08-01 20:37:57 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:37:57 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:37:57 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:37:57 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:37:57 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-01 20:38:06 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:38:08 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:09 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:09 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:09 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:10 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:38:10 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 20:38:11 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-01 20:40:37 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:40:37 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:37 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:37 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-01 20:40:37 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-01 20:40:40 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:41 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:41 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:41 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:42 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-01 20:40:42 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-01 20:40:45 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 10:11:46 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:46 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:46 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:46 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:46 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 10:11:53 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:57 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:57 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 10:11:58 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 10:21:09 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:21:09 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:21:09 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:21:09 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:21:09 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:42 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:42 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 15:48:04 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:48:04 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:48:04 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:48:05 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:48:05 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 15:48:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:48:16 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 15:48:17 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 15:58:50 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:58:50 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:58:50 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:58:51 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:58:51 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:03:50 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:03:50 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:08:50 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:08:50 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:09:08 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:09:08 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:08 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:08 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:09:08 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:23:15 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:15 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:23:15 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:23:18 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:29:51 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:29:51 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:29:51 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:29:52 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:29:52 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 17:30:28 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:35 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:35 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 17:30:37 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 17:50:29 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:50:29 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:29 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:30 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:50:30 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 17:51:05 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:51:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:51:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:52:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:52:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:53:19 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:53:19 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 17:53:20 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 17:53:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:53:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 18:03:48 - utils.error_handler - ERROR - Exception non capturée: Traceback (most recent call last):
  File "D:\projects\GSlim\demo_articles.py", line 210, in <module>
    sys.exit(main())
             ~~~~^^
  File "D:\projects\GSlim\demo_articles.py", line 203, in main
    window = ArticlesDemoWindow()
  File "D:\projects\GSlim\demo_articles.py", line 26, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "D:\projects\GSlim\demo_articles.py", line 42, in setup_ui
    self.articles_window = EnhancedArticlesWindow(self)
                           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "D:\projects\GSlim\src\views\enhanced_articles_window.py", line 321, in __init__
    self.controller = ArticleController(app_instance.get_database_manager())
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\projects\GSlim\demo_articles.py", line 173, in get_database_manager
    return self.db_manager
           ^^^^^^^^^^^^^^^
AttributeError: 'ArticlesDemoWindow' object has no attribute 'db_manager'

2025-08-02 18:10:11 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:10:11 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:10:23 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:10:36 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:11:00 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 18:11:20 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'ArticleController' object has no attribute 'get_all'
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'sqlite3.Cursor' object is not callable
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 18:23:46 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:46 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:49 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:54 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:59 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:47:58 - controllers.supplier_controller - ERROR - Erreur lors du calcul des statistiques: 'NoneType' object is not subscriptable
2025-08-02 18:49:30 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 18:49:30 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 18:49:37 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 18:49:37 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 19:02:00 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: 'DatabaseManager' object has no attribute 'initialize_database'
2025-08-02 19:07:54 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:08 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:15 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:30 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:41 - src.app - ERROR - Erreur lors de la fermeture: 'DatabaseManager' object has no attribute 'close'
2025-08-02 19:16:45 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:16:45 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:16:55 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:16:57 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: 'SupplierController' object has no attribute 'count'
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:16:58 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'ArticleController' object has no attribute 'get_all'
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'sqlite3.Cursor' object is not callable
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:17:01 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:17:01 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:17:02 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-02 19:17:03 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:17:04 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:17:06 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:17:06 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:17:07 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: 'SupplierController' object has no attribute 'count'
2025-08-02 19:26:19 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-02 19:26:19 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 19:26:21 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 19:26:24 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:25 - controllers.article_controller - ERROR - Erreur lors de la récupération des articles: no such column: c.nom
2025-08-02 19:26:25 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'SupplierController' object has no attribute 'get_all'
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:26 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:26 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:26:27 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-02 19:41:35 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-02 19:41:35 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 19:41:38 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 19:41:39 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 19:41:41 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors de la création des cartes de stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 19:41:42 - controllers.article_controller - ERROR - Erreur lors de la récupération des articles: no such column: c.nom
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'SupplierController' object has no attribute 'get_all'
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors du chargement des statistiques: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors de la mise à jour des stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 19:41:43 - views.orders_window - ERROR - Erreur lors de la création des cartes de stats: 'OrderController' object has no attribute 'get_order_statistics'
2025-08-02 19:41:43 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:41:44 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-02 19:41:46 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 20:28:29 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-02 20:28:29 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 20:28:31 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 20:28:32 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-02 20:28:33 - views.movements_window - ERROR - Erreur lors de la création des cartes de stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 20:28:34 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'name'
2025-08-02 20:28:34 - views.movements_window - ERROR - Erreur lors du chargement des statistiques: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 20:28:34 - views.movements_window - ERROR - Erreur lors de la mise à jour des stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-03 16:30:16 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-03 16:30:16 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-03 16:30:48 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-03 16:30:49 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-03 16:30:50 - views.orders_window - ERROR - Erreur lors de la création des cartes de stats: 'OrderController' object has no attribute 'get_order_statistics'
2025-08-03 16:30:50 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-03 16:30:51 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-03 16:31:02 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-03 16:37:27 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-03 16:37:27 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-03 16:37:31 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-03 16:37:32 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors de la création des cartes de stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'name'
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors du chargement des statistiques: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors de la mise à jour des stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-03 16:37:34 - views.orders_window - ERROR - Erreur lors de la création des cartes de stats: 'OrderController' object has no attribute 'get_order_statistics'
2025-08-03 16:37:34 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-03 17:31:37 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:31:37 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:31:37 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:35:07 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:39:49 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: 'dashboard'
2025-08-03 17:49:01 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'MONEY'
2025-08-03 17:49:01 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module clients: type object 'FluentIcon' has no attribute 'STAR'
2025-08-03 17:49:01 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 17:52:05 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'MONEY'
2025-08-03 17:52:06 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module clients: type object 'FluentIcon' has no attribute 'STAR'
2025-08-03 17:52:06 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 17:52:57 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'MONEY'
2025-08-03 17:52:57 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module clients: type object 'FluentIcon' has no attribute 'STAR'
2025-08-03 17:52:57 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 17:55:19 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 17:55:19 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:09:01 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:09:01 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:12:52 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:12:52 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:30:53 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:30:54 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:41:41 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:41:41 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:49:25 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:49:25 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:53:34 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 18:53:34 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 18:57:31 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 18:57:32 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 19:03:17 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 19:08:05 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 19:16:17 - database.manager - ERROR - Erreur lors de la création de la vente: table sales has no column named discount
2025-08-03 19:29:23 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 19:35:27 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 20:20:25 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module settings: name 'QGridLayout' is not defined
2025-08-03 20:20:25 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 20:20:25 - views.modern_main_window - ERROR - Erreur lors de la connexion des signaux: 'QWidget' object has no attribute 'theme_changed'
2025-08-03 20:31:23 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module settings: name 'QGridLayout' is not defined
2025-08-03 20:31:23 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 20:31:23 - views.modern_main_window - ERROR - Erreur lors de la connexion des signaux: 'QWidget' object has no attribute 'theme_changed'
2025-08-04 16:07:48 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module settings: name 'QGridLayout' is not defined
2025-08-04 16:07:48 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-04 16:07:48 - views.modern_main_window - ERROR - Erreur lors de la connexion des signaux: 'QWidget' object has no attribute 'theme_changed'
