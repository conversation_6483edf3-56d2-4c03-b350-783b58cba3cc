"""
Fenêtre de gestion des commandes fournisseurs
Interface pour créer, suivre et gérer les commandes
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QFrame, QSplitter, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QFormLayout, QSpinBox, QDoubleSpinBox,
    QTextEdit, QDateEdit, QGroupBox, QGridLayout, QTabWidget,
    QListWidget, QListWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDate
from PyQt5.QtGui import QFont, QColor, QPalette

try:
    from qfluentwidgets import (
        PushButton, LineEdit, ComboBox, TableWidget,
        SearchLineEdit, CardWidget, TitleLabel, CaptionLabel,
        FluentIcon, InfoBar, InfoBarPosition, DateEdit
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from controllers.order_controller import OrderController
from controllers.article_controller import ArticleController
from controllers.supplier_controller import SupplierController
from utils.logger import setup_logger
from datetime import datetime, date, timedelta
from decimal import Decimal


class OrdersWindow(QWidget):
    """Fenêtre de gestion des commandes fournisseurs"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.logger = setup_logger(__name__)
        
        # Contrôleurs
        self.controller = OrderController(db_manager)
        self.article_controller = ArticleController(db_manager)
        self.supplier_controller = SupplierController(db_manager)
        
        # Variables
        self.orders_data = []
        self.filtered_data = []
        self.selected_order = None
        self.order_items = []  # Articles de la commande en cours de création
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._load_orders)
        self.refresh_timer.start(300000)  # Actualiser toutes les 5 minutes
        
        self._init_ui()
        self._load_orders()
        
        self.logger.info("OrdersWindow initialisé")
    
    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        self._create_header(layout)
        
        # Onglets
        self.tab_widget = QTabWidget()
        
        # Onglet Liste des commandes
        orders_tab = QWidget()
        self._create_orders_tab(orders_tab)
        self.tab_widget.addTab(orders_tab, "Commandes")
        
        # Onglet Nouvelle commande
        new_order_tab = QWidget()
        self._create_new_order_tab(new_order_tab)
        self.tab_widget.addTab(new_order_tab, "Nouvelle Commande")
        
        # Onglet Commandes en attente
        pending_tab = QWidget()
        self._create_pending_orders_tab(pending_tab)
        self.tab_widget.addTab(pending_tab, "En Attente")
        
        # Onglet Commandes en retard
        overdue_tab = QWidget()
        self._create_overdue_orders_tab(overdue_tab)
        self.tab_widget.addTab(overdue_tab, "En Retard")
        
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)
    
    def _create_header(self, layout):
        """Créer l'en-tête"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Commandes")
        else:
            title = QLabel("Gestion des Commandes")
            title.setProperty("class", "title")
            font = QFont("Segoe UI", 24, QFont.Bold)
            title.setFont(font)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Statistiques rapides
        self._create_stats_cards(header_layout)
        
        layout.addLayout(header_layout)
    
    def _create_stats_cards(self, layout):
        """Créer les cartes de statistiques"""
        try:
            stats = self.controller.get_order_statistics()
            
            # Carte total commandes
            total_card = self._create_stat_card("Total", str(stats.get('total_orders', 0)), "#0078d4")
            layout.addWidget(total_card)
            
            # Carte en attente
            pending_card = self._create_stat_card("En attente", str(stats.get('pending_orders', 0)), "#fd7e14")
            layout.addWidget(pending_card)
            
            # Carte reçues
            received_card = self._create_stat_card("Reçues", str(stats.get('received_orders', 0)), "#28a745")
            layout.addWidget(received_card)
            
            # Carte en retard
            overdue_card = self._create_stat_card("En retard", str(stats.get('overdue_orders', 0)), "#dc3545")
            layout.addWidget(overdue_card)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création des cartes de stats: {e}")
    
    def _create_stat_card(self, title: str, value: str, color: str):
        """Créer une carte de statistique"""
        if FLUENT_AVAILABLE:
            card = CardWidget()
        else:
            card = QFrame()
            card.setProperty("class", "stat-card")
            card.setFrameStyle(QFrame.Box)
        
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(10, 10, 10, 10)
        card_layout.setSpacing(5)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = CaptionLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setProperty("class", "stat-title")
        
        title_label.setAlignment(Qt.AlignCenter)
        card_layout.addWidget(title_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setProperty("class", "stat-value")
        
        font = QFont("Segoe UI", 14, QFont.Bold)
        value_label.setFont(font)
        value_label.setStyleSheet(f"color: {color};")
        
        card_layout.addWidget(value_label)
        
        card.setLayout(card_layout)
        card.setFixedSize(100, 70)
        
        return card
    
    def _create_orders_tab(self, tab_widget):
        """Créer l'onglet de liste des commandes"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Filtres
        self._create_filters(layout)
        
        # Table des commandes
        self._create_orders_table(layout)
        
        # Boutons d'action
        self._create_action_buttons(layout)
        
        tab_widget.setLayout(layout)
    
    def _create_filters(self, layout):
        """Créer les filtres de recherche"""
        filters_frame = QFrame()
        filters_frame.setProperty("class", "filters-frame")
        
        filters_layout = QGridLayout()
        filters_layout.setSpacing(10)
        
        # Recherche par numéro de commande
        filters_layout.addWidget(QLabel("N° Commande:"), 0, 0)
        if FLUENT_AVAILABLE:
            self.order_number_filter = SearchLineEdit()
            self.order_number_filter.setPlaceholderText("Rechercher par numéro...")
        else:
            self.order_number_filter = QLineEdit()
            self.order_number_filter.setPlaceholderText("Rechercher par numéro...")
        
        self.order_number_filter.textChanged.connect(self._apply_filters)
        filters_layout.addWidget(self.order_number_filter, 0, 1)
        
        # Filtre par fournisseur
        filters_layout.addWidget(QLabel("Fournisseur:"), 0, 2)
        if FLUENT_AVAILABLE:
            self.supplier_filter = ComboBox()
        else:
            self.supplier_filter = QComboBox()
        
        self.supplier_filter.addItem("Tous les fournisseurs", "")
        self.supplier_filter.currentTextChanged.connect(self._apply_filters)
        filters_layout.addWidget(self.supplier_filter, 0, 3)
        
        # Filtre par statut
        filters_layout.addWidget(QLabel("Statut:"), 1, 0)
        if FLUENT_AVAILABLE:
            self.status_filter = ComboBox()
        else:
            self.status_filter = QComboBox()
        
        statuses = [
            ("Tous les statuts", ""),
            ("En attente", "pending"),
            ("Envoyée", "sent"),
            ("Reçue", "received"),
            ("Annulée", "cancelled")
        ]
        
        for label, value in statuses:
            self.status_filter.addItem(label, value)
        
        self.status_filter.currentTextChanged.connect(self._apply_filters)
        filters_layout.addWidget(self.status_filter, 1, 1)
        
        # Bouton de réinitialisation
        if FLUENT_AVAILABLE:
            reset_btn = PushButton("Réinitialiser")
            reset_btn.setIcon(FluentIcon.CLEAR_SELECTION)
        else:
            reset_btn = QPushButton("Réinitialiser")
        
        reset_btn.clicked.connect(self._reset_filters)
        filters_layout.addWidget(reset_btn, 1, 2)
        
        filters_frame.setLayout(filters_layout)
        layout.addWidget(filters_frame)
    
    def _create_orders_table(self, layout):
        """Créer la table des commandes"""
        if FLUENT_AVAILABLE:
            self.orders_table = TableWidget()
        else:
            self.orders_table = QTableWidget()
        
        # Configuration de la table
        columns = [
            "N° Commande", "Fournisseur", "Date", "Date prévue", 
            "Statut", "Montant", "Articles"
        ]
        
        self.orders_table.setColumnCount(len(columns))
        self.orders_table.setHorizontalHeaderLabels(columns)
        
        # Configuration des colonnes
        header = self.orders_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # N° Commande
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Fournisseur
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Date prévue
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Montant
        
        # Configuration générale
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.orders_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.orders_table.setSortingEnabled(True)
        
        # Connecter les signaux
        self.orders_table.itemSelectionChanged.connect(self._on_order_selected)
        self.orders_table.itemDoubleClicked.connect(self._view_order_details)
        
        layout.addWidget(self.orders_table)
    
    def _create_action_buttons(self, layout):
        """Créer les boutons d'action"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.refresh_button = PushButton("Actualiser")
            self.refresh_button.setIcon(FluentIcon.SYNC)
            
            self.details_button = PushButton("Détails")
            self.details_button.setIcon(FluentIcon.VIEW)
            
            self.update_status_button = PushButton("Changer statut")
            self.update_status_button.setIcon(FluentIcon.EDIT)
            
            self.cancel_button = PushButton("Annuler")
            self.cancel_button.setIcon(FluentIcon.CANCEL)
        else:
            self.refresh_button = QPushButton("Actualiser")
            self.details_button = QPushButton("Détails")
            self.update_status_button = QPushButton("Changer statut")
            self.cancel_button = QPushButton("Annuler")
        
        # Connecter les signaux
        self.refresh_button.clicked.connect(self._load_orders)
        self.details_button.clicked.connect(self._view_order_details)
        self.update_status_button.clicked.connect(self._update_order_status)
        self.cancel_button.clicked.connect(self._cancel_order)
        
        # État initial des boutons
        self.details_button.setEnabled(False)
        self.update_status_button.setEnabled(False)
        self.cancel_button.setEnabled(False)
        
        buttons_layout.addWidget(self.refresh_button)
        buttons_layout.addWidget(self.details_button)
        buttons_layout.addWidget(self.update_status_button)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
    
    def _create_new_order_tab(self, tab_widget):
        """Créer l'onglet de nouvelle commande"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # Informations de la commande
        order_info_frame = QFrame()
        order_info_frame.setProperty("class", "form-frame")
        
        order_info_layout = QFormLayout()
        order_info_layout.setSpacing(15)
        
        # Fournisseur
        if FLUENT_AVAILABLE:
            self.new_supplier_combo = ComboBox()
        else:
            self.new_supplier_combo = QComboBox()
        
        self.new_supplier_combo.currentTextChanged.connect(self._on_supplier_changed)
        order_info_layout.addRow("Fournisseur:", self.new_supplier_combo)
        
        # Date prévue
        if FLUENT_AVAILABLE:
            self.new_expected_date = DateEdit()
        else:
            self.new_expected_date = QDateEdit()
        
        self.new_expected_date.setDate(QDate.currentDate().addDays(7))
        order_info_layout.addRow("Date prévue:", self.new_expected_date)
        
        # Notes
        self.new_order_notes = QTextEdit()
        self.new_order_notes.setMaximumHeight(80)
        order_info_layout.addRow("Notes:", self.new_order_notes)
        
        order_info_frame.setLayout(order_info_layout)
        layout.addWidget(order_info_frame)
        
        # Section d'ajout d'articles
        self._create_add_article_section(layout)
        
        # Liste des articles de la commande
        self._create_order_items_list(layout)
        
        # Boutons de création
        self._create_order_creation_buttons(layout)
        
        tab_widget.setLayout(layout)
        
        # Charger les données
        self._load_new_order_data()
    
    def _create_add_article_section(self, layout):
        """Créer la section d'ajout d'articles"""
        add_frame = QFrame()
        add_frame.setProperty("class", "add-article-frame")
        
        add_layout = QHBoxLayout()
        add_layout.setSpacing(10)
        
        # Sélection d'article
        if FLUENT_AVAILABLE:
            self.article_combo = ComboBox()
        else:
            self.article_combo = QComboBox()
        
        self.article_combo.setEditable(True)
        self.article_combo.currentTextChanged.connect(self._on_article_selected)
        add_layout.addWidget(QLabel("Article:"))
        add_layout.addWidget(self.article_combo)
        
        # Quantité
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(999999)
        self.quantity_spin.setValue(1)
        add_layout.addWidget(QLabel("Quantité:"))
        add_layout.addWidget(self.quantity_spin)
        
        # Prix unitaire
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setMinimum(0.0)
        self.unit_price_spin.setMaximum(999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" €")
        add_layout.addWidget(QLabel("Prix:"))
        add_layout.addWidget(self.unit_price_spin)
        
        # Bouton d'ajout
        if FLUENT_AVAILABLE:
            add_article_btn = PushButton("Ajouter")
            add_article_btn.setIcon(FluentIcon.ADD)
        else:
            add_article_btn = QPushButton("Ajouter")
        
        add_article_btn.clicked.connect(self._add_article_to_order)
        add_layout.addWidget(add_article_btn)
        
        add_frame.setLayout(add_layout)
        layout.addWidget(add_frame)
    
    def _create_order_items_list(self, layout):
        """Créer la liste des articles de la commande"""
        items_frame = QFrame()
        items_frame.setProperty("class", "items-frame")
        
        items_layout = QVBoxLayout()
        
        # Titre
        items_title = QLabel("Articles de la commande")
        items_title.setProperty("class", "section-title")
        font = QFont("Segoe UI", 14, QFont.Bold)
        items_title.setFont(font)
        items_layout.addWidget(items_title)
        
        # Liste des articles
        self.order_items_list = QListWidget()
        self.order_items_list.setMaximumHeight(200)
        items_layout.addWidget(self.order_items_list)
        
        # Boutons de gestion des articles
        items_buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            remove_item_btn = PushButton("Supprimer")
            remove_item_btn.setIcon(FluentIcon.DELETE)
            
            clear_items_btn = PushButton("Tout effacer")
            clear_items_btn.setIcon(FluentIcon.CLEAR_SELECTION)
        else:
            remove_item_btn = QPushButton("Supprimer")
            clear_items_btn = QPushButton("Tout effacer")
        
        remove_item_btn.clicked.connect(self._remove_selected_item)
        clear_items_btn.clicked.connect(self._clear_order_items)
        
        items_buttons_layout.addWidget(remove_item_btn)
        items_buttons_layout.addWidget(clear_items_btn)
        items_buttons_layout.addStretch()
        
        # Total de la commande
        self.total_label = QLabel("Total: 0.00 €")
        self.total_label.setProperty("class", "total-label")
        font = QFont("Segoe UI", 14, QFont.Bold)
        self.total_label.setFont(font)
        items_buttons_layout.addWidget(self.total_label)
        
        items_layout.addLayout(items_buttons_layout)
        
        items_frame.setLayout(items_layout)
        layout.addWidget(items_frame)
    
    def _create_order_creation_buttons(self, layout):
        """Créer les boutons de création de commande"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.create_order_btn = PushButton("Créer la commande")
            self.create_order_btn.setIcon(FluentIcon.ADD)
            
            self.clear_order_btn = PushButton("Effacer tout")
            self.clear_order_btn.setIcon(FluentIcon.CLEAR_SELECTION)
        else:
            self.create_order_btn = QPushButton("Créer la commande")
            self.clear_order_btn = QPushButton("Effacer tout")
        
        self.create_order_btn.clicked.connect(self._create_order)
        self.clear_order_btn.clicked.connect(self._clear_new_order_form)
        
        buttons_layout.addWidget(self.create_order_btn)
        buttons_layout.addWidget(self.clear_order_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)

    def _create_pending_orders_tab(self, tab_widget):
        """Créer l'onglet des commandes en attente"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # Table des commandes en attente
        if FLUENT_AVAILABLE:
            self.pending_orders_table = TableWidget()
        else:
            self.pending_orders_table = QTableWidget()

        columns = ["N° Commande", "Fournisseur", "Date", "Date prévue", "Montant", "Actions"]
        self.pending_orders_table.setColumnCount(len(columns))
        self.pending_orders_table.setHorizontalHeaderLabels(columns)

        # Configuration
        header = self.pending_orders_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.pending_orders_table.setAlternatingRowColors(True)

        layout.addWidget(self.pending_orders_table)

        tab_widget.setLayout(layout)

    def _create_overdue_orders_tab(self, tab_widget):
        """Créer l'onglet des commandes en retard"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # Table des commandes en retard
        if FLUENT_AVAILABLE:
            self.overdue_orders_table = TableWidget()
        else:
            self.overdue_orders_table = QTableWidget()

        columns = ["N° Commande", "Fournisseur", "Date prévue", "Retard (jours)", "Montant", "Actions"]
        self.overdue_orders_table.setColumnCount(len(columns))
        self.overdue_orders_table.setHorizontalHeaderLabels(columns)

        # Configuration
        header = self.overdue_orders_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.overdue_orders_table.setAlternatingRowColors(True)

        layout.addWidget(self.overdue_orders_table)

        tab_widget.setLayout(layout)

    def _load_orders(self):
        """Charger la liste des commandes"""
        try:
            # Récupérer toutes les commandes
            self.orders_data = self.controller.get_all()
            self._apply_filters()

            # Charger les commandes en attente
            self._load_pending_orders()

            # Charger les commandes en retard
            self._load_overdue_orders()

            # Mettre à jour les statistiques
            self._update_stats_cards()

            self.logger.info(f"{len(self.orders_data)} commandes chargées")

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des commandes: {e}")
            self._show_error("Erreur", f"Impossible de charger les commandes: {e}")

    def _load_pending_orders(self):
        """Charger les commandes en attente"""
        try:
            pending_orders = self.controller.get_pending_orders()
            self.pending_orders_table.setRowCount(len(pending_orders))

            for row, order in enumerate(pending_orders):
                self.pending_orders_table.setItem(row, 0, QTableWidgetItem(order.get('order_number', '')))
                self.pending_orders_table.setItem(row, 1, QTableWidgetItem(order.get('supplier_name', '')))

                # Date de commande
                order_date = order.get('order_date', '')
                if order_date:
                    try:
                        date_obj = datetime.fromisoformat(order_date)
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = order_date
                else:
                    formatted_date = ''
                self.pending_orders_table.setItem(row, 2, QTableWidgetItem(formatted_date))

                # Date prévue
                expected_date = order.get('expected_date', '')
                if expected_date:
                    try:
                        date_obj = datetime.fromisoformat(expected_date)
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = expected_date
                else:
                    formatted_date = ''
                self.pending_orders_table.setItem(row, 3, QTableWidgetItem(formatted_date))

                self.pending_orders_table.setItem(row, 4, QTableWidgetItem(f"{order.get('total_amount', 0):.2f} €"))

                # Boutons d'action
                actions_widget = self._create_order_actions_widget(order)
                self.pending_orders_table.setCellWidget(row, 5, actions_widget)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des commandes en attente: {e}")

    def _load_overdue_orders(self):
        """Charger les commandes en retard"""
        try:
            overdue_orders = self.controller.get_overdue_orders()
            self.overdue_orders_table.setRowCount(len(overdue_orders))

            for row, order in enumerate(overdue_orders):
                self.overdue_orders_table.setItem(row, 0, QTableWidgetItem(order.get('order_number', '')))
                self.overdue_orders_table.setItem(row, 1, QTableWidgetItem(order.get('supplier_name', '')))

                # Date prévue
                expected_date = order.get('expected_date', '')
                if expected_date:
                    try:
                        date_obj = datetime.fromisoformat(expected_date)
                        formatted_date = date_obj.strftime('%d/%m/%Y')

                        # Calculer le retard
                        today = datetime.now().date()
                        delay_days = (today - date_obj.date()).days
                    except:
                        formatted_date = expected_date
                        delay_days = 0
                else:
                    formatted_date = ''
                    delay_days = 0

                self.overdue_orders_table.setItem(row, 2, QTableWidgetItem(formatted_date))
                self.overdue_orders_table.setItem(row, 3, QTableWidgetItem(str(delay_days)))
                self.overdue_orders_table.setItem(row, 4, QTableWidgetItem(f"{order.get('total_amount', 0):.2f} €"))

                # Boutons d'action
                actions_widget = self._create_order_actions_widget(order)
                self.overdue_orders_table.setCellWidget(row, 5, actions_widget)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des commandes en retard: {e}")

    def _create_order_actions_widget(self, order: dict):
        """Créer un widget avec les actions pour une commande"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        if FLUENT_AVAILABLE:
            mark_sent_btn = PushButton("Envoyée")
            mark_sent_btn.setIcon(FluentIcon.SEND)

            mark_received_btn = PushButton("Reçue")
            mark_received_btn.setIcon(FluentIcon.ACCEPT)
        else:
            mark_sent_btn = QPushButton("Envoyée")
            mark_received_btn = QPushButton("Reçue")

        # Connecter les signaux avec l'ID de la commande
        order_id = order.get('id')
        mark_sent_btn.clicked.connect(lambda: self._mark_order_sent(order_id))
        mark_received_btn.clicked.connect(lambda: self._mark_order_received(order_id))

        layout.addWidget(mark_sent_btn)
        layout.addWidget(mark_received_btn)

        widget.setLayout(layout)
        return widget

    def _apply_filters(self):
        """Appliquer les filtres à la liste des commandes"""
        try:
            self.filtered_data = self.orders_data.copy()

            # Filtre par numéro de commande
            order_number_filter = self.order_number_filter.text().lower()
            if order_number_filter:
                self.filtered_data = [
                    o for o in self.filtered_data
                    if order_number_filter in o.get('order_number', '').lower()
                ]

            # Filtre par fournisseur
            supplier_filter = self.supplier_filter.currentData()
            if supplier_filter:
                self.filtered_data = [
                    o for o in self.filtered_data
                    if o.get('supplier_id') == supplier_filter
                ]

            # Filtre par statut
            status_filter = self.status_filter.currentData()
            if status_filter:
                self.filtered_data = [
                    o for o in self.filtered_data
                    if o.get('status') == status_filter
                ]

            # Mettre à jour la table
            self._update_orders_table()

        except Exception as e:
            self.logger.error(f"Erreur lors de l'application des filtres: {e}")

    def _update_orders_table(self):
        """Mettre à jour la table des commandes"""
        try:
            self.orders_table.setRowCount(len(self.filtered_data))

            for row, order in enumerate(self.filtered_data):
                self.orders_table.setItem(row, 0, QTableWidgetItem(order.get('order_number', '')))
                self.orders_table.setItem(row, 1, QTableWidgetItem(order.get('supplier_name', '')))

                # Date de commande
                order_date = order.get('order_date', '')
                if order_date:
                    try:
                        date_obj = datetime.fromisoformat(order_date)
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = order_date
                else:
                    formatted_date = ''
                self.orders_table.setItem(row, 2, QTableWidgetItem(formatted_date))

                # Date prévue
                expected_date = order.get('expected_date', '')
                if expected_date:
                    try:
                        date_obj = datetime.fromisoformat(expected_date)
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = expected_date
                else:
                    formatted_date = ''
                self.orders_table.setItem(row, 3, QTableWidgetItem(formatted_date))

                # Statut avec couleur
                status = order.get('status', '')
                status_item = QTableWidgetItem(self._get_status_label(status))
                status_item.setBackground(self._get_status_color(status))
                self.orders_table.setItem(row, 4, status_item)

                self.orders_table.setItem(row, 5, QTableWidgetItem(f"{order.get('total_amount', 0):.2f} €"))

                # Nombre d'articles (à calculer)
                # TODO: Récupérer le nombre d'articles de la commande
                self.orders_table.setItem(row, 6, QTableWidgetItem("N/A"))

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de la table: {e}")

    def _get_status_label(self, status: str) -> str:
        """Obtenir le libellé du statut"""
        status_labels = {
            'pending': 'En attente',
            'sent': 'Envoyée',
            'received': 'Reçue',
            'cancelled': 'Annulée'
        }
        return status_labels.get(status, status)

    def _get_status_color(self, status: str):
        """Obtenir la couleur du statut"""
        from PyQt5.QtGui import QColor
        status_colors = {
            'pending': QColor(255, 255, 200),  # Jaune clair
            'sent': QColor(200, 200, 255),     # Bleu clair
            'received': QColor(200, 255, 200), # Vert clair
            'cancelled': QColor(255, 200, 200) # Rouge clair
        }
        return status_colors.get(status, QColor(255, 255, 255))


    def _reset_filters(self):
        """Réinitialiser les filtres"""
        try:
            if hasattr(self, 'status_filter'):
                self.status_filter.setCurrentIndex(0)
            if hasattr(self, 'date_filter'):
                self.date_filter.setCurrentIndex(0)
            if hasattr(self, 'search_input'):
                self.search_input.clear()
            
            # Recharger les données
            self.load_orders()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la réinitialisation des filtres: {e}")
