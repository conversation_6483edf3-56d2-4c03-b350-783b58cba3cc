#!/usr/bin/env python3
"""
Test spécifique de la fenêtre principale
"""

import sys
import traceback

# Ajouter src au path
sys.path.insert(0, 'src')

def test_main_window():
    """Tester la fenêtre principale"""
    print("🖼️ Test de la fenêtre principale...")
    
    try:
        # Test des imports
        print("1. Import de l'app...")
        from src.app import GSlimApp
        
        print("2. Import de la fenêtre moderne...")
        from views.modern_main_window import ModernMainWindow
        
        print("3. Création de l'app...")
        app_instance = GSlimApp()
        
        print("4. Test de création de la fenêtre...")
        # Simuler un utilisateur connecté
        app_instance.current_user = {
            'id': 1,
            'username': 'admin',
            'is_admin': True
        }
        
        # Créer la fenêtre principale
        main_window = ModernMainWindow(app_instance)
        print("✅ Fenêtre principale créée avec succès")
        
        # Tester la définition de l'utilisateur
        main_window.set_current_user(app_instance.current_user)
        print("✅ Utilisateur défini avec succès")
        
        print("✅ Test de la fenêtre principale réussi")
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        traceback.print_exc()
        return False

def test_modules_individually():
    """Tester chaque module individuellement"""
    print("\n🧩 Test des modules individuellement...")
    
    try:
        from src.app import GSlimApp
        app_instance = GSlimApp()
        
        modules_to_test = [
            ('DashboardWidget', 'views.dashboard_widget'),
            ('ProductsWidget', 'views.products_widget'),
            ('ClientsWidget', 'views.clients_widget'),
            ('SuppliersWidget', 'views.suppliers_widget'),
            ('SalesWidget', 'views.sales_widget'),
            ('SettingsWidget', 'views.settings_widget')
        ]
        
        for widget_name, module_path in modules_to_test:
            try:
                print(f"   Test {widget_name}...")
                module = __import__(module_path, fromlist=[widget_name])
                widget_class = getattr(module, widget_name)
                widget = widget_class(app_instance)
                print(f"   ✅ {widget_name} OK")
            except Exception as e:
                print(f"   ❌ {widget_name}: {e}")
                
        return True
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        return False

def main():
    """Test principal"""
    print("🧪 Test de la Fenêtre Principale")
    print("=" * 40)
    
    success1 = test_modules_individually()
    success2 = test_main_window()
    
    if success1 and success2:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        return True
    else:
        print("\n❌ Certains tests ont échoué")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
