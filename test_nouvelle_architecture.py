#!/usr/bin/env python3
"""
Test de la nouvelle architecture conforme au cahier des charges
Version 1.0.2 avec modules modernes
"""

import sys
import os

# Ajouter src au path
sys.path.insert(0, 'src')

def test_nouvelle_architecture():
    """Tester la nouvelle architecture selon le cahier des charges"""
    print("🏗️ Test de la Nouvelle Architecture GSlim v1.0.2")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    # Test 1: Configuration mise à jour
    print("\n📋 Test de la configuration...")
    total_tests += 1
    try:
        from config.settings import config
        assert config.APP_VERSION == "1.0.2"
        assert config.APP_AUTHOR == "Gnuslim/GSCOM"
        assert config.DATABASE_NAME == "gestion_stock.sqlite"
        print("✅ Configuration conforme au cahier des charges")
        success_count += 1
    except Exception as e:
        print(f"❌ Configuration: {e}")
    
    # Test 2: DatabaseManager Singleton
    print("\n🗄️ Test du DatabaseManager Singleton...")
    total_tests += 1
    try:
        from database.manager import DatabaseManager
        
        # Test du pattern Singleton
        db1 = DatabaseManager()
        db2 = DatabaseManager()
        assert db1 is db2, "Le DatabaseManager doit être un Singleton"
        
        # Test des méthodes de sécurité
        assert hasattr(db1, 'hash_password'), "Méthode hash_password manquante"
        assert hasattr(db1, 'verify_password'), "Méthode verify_password manquante"
        assert hasattr(db1, 'create_default_admin'), "Méthode create_default_admin manquante"
        
        print("✅ DatabaseManager Singleton avec sécurité bcrypt")
        success_count += 1
    except Exception as e:
        print(f"❌ DatabaseManager: {e}")
    
    # Test 3: Nouvelle fenêtre principale moderne
    print("\n🖼️ Test de la fenêtre principale moderne...")
    total_tests += 1
    try:
        from views.modern_main_window import ModernMainWindow
        print("✅ Fenêtre principale moderne importée")
        success_count += 1
    except Exception as e:
        print(f"❌ Fenêtre principale: {e}")
    
    # Test 4: Widgets des modules selon spécifications
    print("\n🧩 Test des widgets des modules...")
    total_tests += 1
    try:
        from views.dashboard_widget import DashboardWidget
        from views.products_widget import ProductsWidget
        from views.clients_widget import ClientsWidget
        from views.suppliers_widget import SuppliersWidget
        from views.sales_widget import SalesWidget
        from views.settings_widget import SettingsWidget
        
        print("✅ Tous les widgets des modules importés")
        success_count += 1
    except Exception as e:
        print(f"❌ Widgets des modules: {e}")
    
    # Test 5: Dialogue de produit selon spécifications
    print("\n📝 Test du dialogue de produit...")
    total_tests += 1
    try:
        from views.dialogs.product_dialog import ProductDialog
        print("✅ Dialogue de produit conforme aux spécifications")
        success_count += 1
    except Exception as e:
        print(f"❌ Dialogue de produit: {e}")
    
    # Test 6: Application principale mise à jour
    print("\n🚀 Test de l'application principale...")
    total_tests += 1
    try:
        from src.app import GSlimApp
        
        # Vérifier que l'app utilise la nouvelle architecture
        app = GSlimApp()
        assert hasattr(app, '_apply_fallback_theme'), "Méthode de thème de fallback manquante"
        
        print("✅ Application principale mise à jour")
        success_count += 1
    except Exception as e:
        print(f"❌ Application principale: {e}")
    
    # Test 7: Fluent Widgets (optionnel)
    print("\n🎨 Test des Fluent Widgets...")
    total_tests += 1
    try:
        from qfluentwidgets import FluentWindow, NavigationInterface, FluentIcon
        print("✅ PyQt-Fluent-Widgets disponible")
        success_count += 1
    except ImportError:
        print("⚠️ PyQt-Fluent-Widgets non disponible (mode fallback)")
        success_count += 1  # Pas d'erreur, c'est prévu
    except Exception as e:
        print(f"❌ Fluent Widgets: {e}")
    
    # Résumé
    print("\n" + "=" * 60)
    print(f"📊 RÉSULTATS: {success_count}/{total_tests} tests réussis")
    
    if success_count == total_tests:
        print("🎉 NOUVELLE ARCHITECTURE PARFAITEMENT FONCTIONNELLE !")
        print("✅ Conforme au cahier des charges v1.0.2")
        print("✅ Pattern Singleton implémenté")
        print("✅ Sécurité bcrypt intégrée")
        print("✅ Interface moderne avec Fluent Design")
        print("✅ Modules séparés selon spécifications")
        print("\n🚀 PRÊT POUR LE DÉVELOPPEMENT COMPLET !")
        return True
    else:
        print(f"⚠️ {total_tests - success_count} problème(s) détecté(s)")
        return False

def test_base_de_donnees():
    """Tester le nouveau schéma de base de données"""
    print("\n🗄️ Test du nouveau schéma de base de données...")
    
    try:
        from database.manager import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        
        # Vérifier que les nouvelles tables existent
        cursor = db.cursor
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = [
            'users', 'categories', 'suppliers', 'clients', 
            'products', 'sales', 'sale_items', 'stock_movements'
        ]
        
        missing_tables = [table for table in required_tables if table not in tables]
        
        if not missing_tables:
            print("✅ Toutes les tables du nouveau schéma créées")
            
            # Test de l'admin par défaut
            cursor.execute("SELECT COUNT(*) FROM users WHERE username = ?", (config.DEFAULT_ADMIN_USERNAME,))
            admin_count = cursor.fetchone()[0]
            
            if admin_count > 0:
                print("✅ Utilisateur admin par défaut créé")
            else:
                print("⚠️ Utilisateur admin par défaut non trouvé")
            
            return True
        else:
            print(f"❌ Tables manquantes: {missing_tables}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def main():
    """Fonction principale de test"""
    try:
        # Test de l'architecture
        arch_success = test_nouvelle_architecture()
        
        # Test de la base de données
        db_success = test_base_de_donnees()
        
        if arch_success and db_success:
            print("\n🎊 VALIDATION COMPLÈTE RÉUSSIE !")
            print("🚀 Votre nouvelle architecture est prête !")
            print("\n📋 Prochaines étapes:")
            print("   1. Implémenter les méthodes CRUD des modules")
            print("   2. Développer les dialogues manquants")
            print("   3. Ajouter les graphiques au dashboard")
            print("   4. Implémenter le système de ventes complet")
            print("\n🔑 Connexion par défaut:")
            print(f"   Utilisateur: {config.DEFAULT_ADMIN_USERNAME}")
            print(f"   Mot de passe: {config.DEFAULT_ADMIN_PASSWORD}")
            
            return True
        else:
            print("\n⚠️ Certains tests ont échoué")
            return False
            
    except Exception as e:
        print(f"\n❌ Erreur critique: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
