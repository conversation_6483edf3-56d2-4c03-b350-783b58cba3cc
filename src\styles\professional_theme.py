"""
Thème professionnel moderne pour GSlim
Design élégant avec dégradés et couleurs harmonieuses
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QPalette, QFont


class ProfessionalTheme:
    """Thème professionnel moderne avec design élégant"""
    
    # === COULEURS PRINCIPALES ===
    PRIMARY_GRADIENT = "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    SECONDARY_GRADIENT = "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
    SUCCESS_GRADIENT = "linear-gradient(135deg, #11998e 0%, #38ef7d 100%)"
    WARNING_GRADIENT = "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)"
    ERROR_GRADIENT = "linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)"
    INFO_GRADIENT = "linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)"
    
    # === ARRIÈRE-PLANS ===
    MAIN_BACKGROUND = "linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)"
    CARD_BACKGROUND = "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)"
    SIDEBAR_BACKGROUND = "linear-gradient(180deg, #667eea 0%, #764ba2 100%)"
    
    # === COULEURS DE TEXTE ===
    TEXT_PRIMARY = "#2d3748"
    TEXT_SECONDARY = "#4a5568"
    TEXT_MUTED = "#718096"
    TEXT_WHITE = "#ffffff"
    
    # === OMBRES ===
    SHADOW_LIGHT = "0 4px 16px rgba(0, 0, 0, 0.06)"
    SHADOW_MEDIUM = "0 8px 32px rgba(0, 0, 0, 0.08)"
    SHADOW_HEAVY = "0 16px 48px rgba(0, 0, 0, 0.12)"
    SHADOW_COLORED = "0 8px 32px rgba(102, 126, 234, 0.2)"
    
    @classmethod
    def get_professional_stylesheet(cls):
        """Retourner le stylesheet professionnel"""
        return f"""
        /* === APPLICATION PRINCIPALE === */
        QMainWindow {{
            background: {cls.MAIN_BACKGROUND}; */ */ */
            color: {cls.TEXT_PRIMARY}; */ */ */
            font-family: 'Segoe UI', 'Inter', 'Roboto', sans-serif; */ */ */
            font-size: 14px; */ */ */
        }}
        
        QWidget {{
            background-color: transparent; */ */ */
            color: {cls.TEXT_PRIMARY}; */ */ */
        }}
        
        /* === CONTENEURS PRINCIPAUX === */
        QWidget[class="main-container"] {{
            background: {cls.MAIN_BACKGROUND}; */ */ */
            border-radius: 0px; */ */ */
        }}
        
        QWidget[class="content-wrapper"] {{
            background: rgba(255, 255, 255, 0.95); */ */ */
            border-radius: 20px; */ */ */
            margin: 16px; */ */ */
            /* box-shadow: {cls.SHADOW_MEDIUM}; */ */ */
            /* backdrop-/* filter: blur(10px); */ */ */
        }}
        
        /* === SIDEBAR/NAVIGATION === */
        QWidget[class="sidebar"] {{
            background: {cls.SIDEBAR_BACKGROUND}; */ */ */
            border-radius: 0px 20px 20px 0px; */ */ */
            /* box-shadow: {cls.SHADOW_MEDIUM}; */ */ */
        }}
        
        /* === CARTES MODERNES === */
        QWidget[class="card"] {{
            background: {cls.CARD_BACKGROUND}; */ */ */
            border: 1px solid rgba(102, 126, 234, 0.1); */ */ */
            border-radius: 20px; */ */ */
            padding: 24px; */ */ */
            margin: 12px; */ */ */
            /* box-shadow: {cls.SHADOW_LIGHT}; */ */ */
        }}
        
        QWidget[class="card"]:hover {{
            border-color: rgba(102, 126, 234, 0.3); */ */ */
            /* box-shadow: {cls.SHADOW_MEDIUM}; */ */ */
            /* transform: translateY(-2px); */ */ */
            /* transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */ */ */
        }}
        
        /* === CARTE PRINCIPALE DASHBOARD === */
        QWidget[class="dashboard-main"] {{
            background: {cls.CARD_BACKGROUND}; */ */ */
            border: none; */ */ */
            border-radius: 24px; */ */ */
            padding: 32px; */ */ */
            margin: 20px; */ */ */
            /* box-shadow: {cls.SHADOW_HEAVY}; */ */ */
        }}
        
        /* === HEADER ÉLÉGANT === */
        QWidget[class="header"] {{
            background: {cls.PRIMARY_GRADIENT}; */ */ */
            border-radius: 20px; */ */ */
            padding: 24px 32px; */ */ */
            margin-bottom: 24px; */ */ */
            /* box-shadow: {cls.SHADOW_COLORED}; */ */ */
        }}
        
        QLabel[class="header-title"] {{
            color: {cls.TEXT_WHITE}; */ */ */
            font-size: 32px; */ */ */
            font-weight: 700; */ */ */
            margin-bottom: 8px; */ */ */
            letter-spacing: -0.5px; */ */ */
        }}
        
        QLabel[class="header-subtitle"] {{
            color: rgba(255, 255, 255, 0.85); */ */ */
            font-size: 16px; */ */ */
            font-weight: 400; */ */ */
            letter-spacing: 0.2px; */ */ */
        }}
        
        /* === CARTES DE STATISTIQUES === */
        QWidget[class="stat-card"] {{
            background: {cls.CARD_BACKGROUND}; */ */ */
            border: 1px solid rgba(102, 126, 234, 0.08); */ */ */
            border-radius: 20px; */ */ */
            padding: 28px; */ */ */
            margin: 12px; */ */ */
            min-height: 160px; */ */ */
            /* box-shadow: {cls.SHADOW_LIGHT}; */ */ */
        }}
        
        QWidget[class="stat-card"]:hover {{
            /* transform: translateY(-4px); */ */ */
            /* box-shadow: {cls.SHADOW_MEDIUM}; */ */ */
            border-color: rgba(102, 126, 234, 0.2); */ */ */
            /* transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */ */ */
        }}
        
        QWidget[class="stat-card-primary"] {{
            background: {cls.PRIMARY_GRADIENT}; */ */ */
            color: {cls.TEXT_WHITE}; */ */ */
            border: none; */ */ */
            /* box-shadow: 0 8px 32px rgba(102, 126, 234, 0.25); */ */ */
        }}
        
        QWidget[class="stat-card-success"] {{
            background: {cls.SUCCESS_GRADIENT}; */ */ */
            color: {cls.TEXT_WHITE}; */ */ */
            border: none; */ */ */
            /* box-shadow: 0 8px 32px rgba(17, 153, 142, 0.25); */ */ */
        }}
        
        QWidget[class="stat-card-warning"] {{
            background: {cls.WARNING_GRADIENT}; */ */ */
            color: {cls.TEXT_PRIMARY}; */ */ */
            border: none; */ */ */
            /* box-shadow: 0 8px 32px rgba(255, 236, 210, 0.4); */ */ */
        }}
        
        QWidget[class="stat-card-error"] {{
            background: {cls.ERROR_GRADIENT}; */ */ */
            color: {cls.TEXT_WHITE}; */ */ */
            border: none; */ */ */
            /* box-shadow: 0 8px 32px rgba(255, 107, 107, 0.25); */ */ */
        }}
        
        QWidget[class="stat-card-info"] {{
            background: {cls.INFO_GRADIENT}; */ */ */
            color: {cls.TEXT_WHITE}; */ */ */
            border: none; */ */ */
            /* box-shadow: 0 8px 32px rgba(116, 185, 255, 0.25); */ */ */
        }}
        
        /* === BOUTONS PROFESSIONNELS === */
        QPushButton {{
            background: {cls.PRIMARY_GRADIENT}; */ */ */
            color: {cls.TEXT_WHITE}; */ */ */
            border: none; */ */ */
            border-radius: 12px; */ */ */
            padding: 16px 32px; */ */ */
            font-size: 14px; */ */ */
            font-weight: 600; */ */ */
            min-height: 20px; */ */ */
            /* box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3); */ */ */
            /* transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); */ */ */
        }}
        
        QPushButton:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%); */ */ */
            /* transform: translateY(-2px); */ */ */
            /* box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4); */ */ */
        }}
        
        QPushButton:pressed {{
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%); */ */ */
            /* transform: translateY(0px); */ */ */
            /* box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); */ */ */
        }}
        
        QPushButton[class="secondary"] {{
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); */ */ */
            color: {cls.TEXT_PRIMARY}; */ */ */
            border: 1px solid rgba(102, 126, 234, 0.2); */ */ */
            /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06); */ */ */
        }}
        
        QPushButton[class="secondary"]:hover {{
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%); */ */ */
            border-color: rgba(102, 126, 234, 0.4); */ */ */
            /* box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); */ */ */
        }}
        
        QPushButton[class="success"] {{
            background: {cls.SUCCESS_GRADIENT}; */ */ */
            /* box-shadow: 0 4px 16px rgba(17, 153, 142, 0.3); */ */ */
        }}
        
        QPushButton[class="warning"] {{
            background: {cls.WARNING_GRADIENT}; */ */ */
            color: {cls.TEXT_PRIMARY}; */ */ */
            /* box-shadow: 0 4px 16px rgba(255, 236, 210, 0.4); */ */ */
        }}
        
        QPushButton[class="danger"] {{
            background: {cls.ERROR_GRADIENT}; */ */ */
            /* box-shadow: 0 4px 16px rgba(255, 107, 107, 0.3); */ */ */
        }}
        
        /* === LABELS ET TEXTE === */
        QLabel[class="title"] {{
            color: {cls.TEXT_PRIMARY}; */ */ */
            font-size: 28px; */ */ */
            font-weight: 700; */ */ */
            margin-bottom: 12px; */ */ */
            letter-spacing: -0.5px; */ */ */
        }}
        
        QLabel[class="subtitle"] {{
            color: {cls.TEXT_SECONDARY}; */ */ */
            font-size: 18px; */ */ */
            font-weight: 600; */ */ */
            margin-bottom: 16px; */ */ */
            letter-spacing: -0.2px; */ */ */
        }}
        
        QLabel[class="body"] {{
            color: {cls.TEXT_SECONDARY}; */ */ */
            font-size: 14px; */ */ */
            font-weight: 400; */ */ */
            line-height: 1.6; */ */ */
        }}
        
        QLabel[class="caption"] {{
            color: {cls.TEXT_MUTED}; */ */ */
            font-size: 12px; */ */ */
            font-weight: 400; */ */ */
        }}
        
        QLabel[class="stat-value"] {{
            color: inherit; */ */ */
            font-size: 36px; */ */ */
            font-weight: 700; */ */ */
            margin-bottom: 8px; */ */ */
            letter-spacing: -1px; */ */ */
        }}
        
        QLabel[class="stat-label"] {{
            color: inherit; */ */ */
            font-size: 14px; */ */ */
            font-weight: 500; */ */ */
            opacity: 0.9; */ */ */
        }}
        
        /* === CHAMPS DE SAISIE === */
        QLineEdit, QTextEdit {{
            background: {cls.CARD_BACKGROUND}; */ */ */
            border: 2px solid rgba(102, 126, 234, 0.1); */ */ */
            border-radius: 12px; */ */ */
            padding: 14px 16px; */ */ */
            font-size: 14px; */ */ */
            color: {cls.TEXT_PRIMARY}; */ */ */
            /* box-shadow: {cls.SHADOW_LIGHT}; */ */ */
        }}
        
        QLineEdit:focus, QTextEdit:focus {{
            border-color: rgba(102, 126, 234, 0.5); */ */ */
            /* box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); */ */ */
            outline: none; */ */ */
        }}
        
        /* === SCROLLBARS === */
        QScrollBar:vertical {{
            background: rgba(255, 255, 255, 0.5); */ */ */
            width: 12px; */ */ */
            border-radius: 6px; */ */ */
            margin: 0; */ */ */
        }}
        
        QScrollBar::handle:vertical {{
            background: rgba(102, 126, 234, 0.3); */ */ */
            border-radius: 6px; */ */ */
            min-height: 20px; */ */ */
            margin: 2px; */ */ */
        }}
        
        QScrollBar::handle:vertical:hover {{
            background: rgba(102, 126, 234, 0.5); */ */ */
        }}
        
        /* === TABLES === */
        QTableWidget {{
            background: {cls.CARD_BACKGROUND}; */ */ */
            border: 1px solid rgba(102, 126, 234, 0.1); */ */ */
            border-radius: 16px; */ */ */
            gridline-color: rgba(102, 126, 234, 0.1); */ */ */
            selection-background-color: rgba(102, 126, 234, 0.1); */ */ */
        }}
        
        QHeaderView::section {{
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); */ */ */
            color: {cls.TEXT_SECONDARY}; */ */ */
            padding: 16px 12px; */ */ */
            border: none; */ */ */
            border-bottom: 2px solid rgba(102, 126, 234, 0.1); */ */ */
            font-weight: 600; */ */ */
            font-size: 13px; */ */ */
        }}
        
        /* === TOOLTIPS === */
        QToolTip {{
            background: {cls.TEXT_PRIMARY}; */ */ */
            color: {cls.TEXT_WHITE}; */ */ */
            border: none; */ */ */
            border-radius: 8px; */ */ */
            padding: 8px 12px; */ */ */
            font-size: 12px; */ */ */
            /* box-shadow: {cls.SHADOW_MEDIUM}; */ */ */
        }}
        """
    
    @classmethod
    def get_dark_professional_stylesheet(cls):
        """Version sombre du thème professionnel"""
        return cls.get_professional_stylesheet().replace(
            cls.MAIN_BACKGROUND, "linear-gradient(135deg, #1a202c 0%, #2d3748 100%)"
        ).replace(
            cls.CARD_BACKGROUND, "linear-gradient(145deg, #2d3748 0%, #4a5568 100%)"
        ).replace(
            cls.TEXT_PRIMARY, "#f7fafc"
        ).replace(
            cls.TEXT_SECONDARY, "#e2e8f0"
        )
