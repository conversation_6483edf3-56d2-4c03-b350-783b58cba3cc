#!/usr/bin/env python3
"""
Test de debug pour identifier les problèmes
"""

import sys
import traceback

# Ajouter src au path
sys.path.insert(0, 'src')

def test_imports():
    """Tester les imports un par un"""
    print("🔍 Test des imports...")
    
    try:
        print("1. Test config...")
        from config.settings import config
        print(f"   ✅ Config OK - Version: {config.APP_VERSION}")
    except Exception as e:
        print(f"   ❌ Config: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("2. Test bcrypt...")
        import bcrypt
        print("   ✅ bcrypt OK")
    except Exception as e:
        print(f"   ❌ bcrypt: {e}")
        return False
    
    try:
        print("3. Test DatabaseManager...")
        from database.manager import DatabaseManager
        print("   ✅ DatabaseManager importé")
        
        # Test d'instanciation
        db = DatabaseManager()
        print("   ✅ DatabaseManager instancié")
        
        # Test Singleton
        db2 = DatabaseManager()
        if db is db2:
            print("   ✅ Pattern Singleton OK")
        else:
            print("   ❌ Pattern Singleton échoué")
            
    except Exception as e:
        print(f"   ❌ DatabaseManager: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("4. Test nouvelle fenêtre...")
        from views.modern_main_window import ModernMainWindow
        print("   ✅ ModernMainWindow importée")
    except Exception as e:
        print(f"   ❌ ModernMainWindow: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("5. Test widgets...")
        from views.dashboard_widget import DashboardWidget
        from views.products_widget import ProductsWidget
        print("   ✅ Widgets importés")
    except Exception as e:
        print(f"   ❌ Widgets: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_database():
    """Tester la base de données"""
    print("\n🗄️ Test de la base de données...")
    
    try:
        from database.manager import DatabaseManager
        
        db = DatabaseManager()
        print("   ✅ DatabaseManager créé")
        
        # Test d'initialisation
        db.initialize_database()
        print("   ✅ Base de données initialisée")
        
        # Test de connexion
        db.connect()
        print("   ✅ Connexion établie")
        
        # Test des tables
        cursor = db.cursor
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   ✅ Tables créées: {len(tables)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Base de données: {e}")
        traceback.print_exc()
        return False

def main():
    """Test principal"""
    print("🧪 Test de Debug GSlim")
    print("=" * 40)
    
    if test_imports():
        print("\n✅ Tous les imports OK")
        
        if test_database():
            print("\n🎉 TOUS LES TESTS RÉUSSIS !")
            return True
        else:
            print("\n❌ Problème avec la base de données")
            return False
    else:
        print("\n❌ Problème avec les imports")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
