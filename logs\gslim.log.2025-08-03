2025-08-03 16:23:54 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 16:23:54 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 16:23:54 - database.manager - INFO - Tables créées avec succès
2025-08-03 16:23:54 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 16:23:54 - database.manager - INFO - Connexion à la base de données fermée
2025-08-03 16:28:42 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 16:28:42 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 16:28:42 - database.manager - INFO - Tables créées avec succès
2025-08-03 16:28:42 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 16:28:43 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 16:28:43 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 16:28:43 - database.manager - INFO - Tables créées avec succès
2025-08-03 16:28:43 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 16:28:43 - src.app - INFO - Base de données initialisée avec succès
2025-08-03 16:30:11 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 16:30:11 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 16:30:11 - database.manager - INFO - Tables créées avec succès
2025-08-03 16:30:11 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 16:30:11 - src.app - INFO - Base de données initialisée avec succès
2025-08-03 16:30:11 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 16:30:14 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 16:30:14 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 16:30:16 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-03 16:30:16 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-03 16:30:16 - controllers.order_controller - INFO - OrderController initialisé
2025-08-03 16:30:16 - controllers.report_controller - INFO - ReportController initialisé
2025-08-03 16:30:16 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-03 16:30:16 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-03 16:30:16 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-03 16:30:16 - views.main_window - INFO - Module tableau de bord affiché
2025-08-03 16:30:16 - src.app - INFO - Fenêtre principale ouverte
2025-08-03 16:30:44 - views.main_window - INFO - Thème changé: classic (dark)
2025-08-03 16:30:46 - views.main_window - INFO - Thème changé: classic (dark)
2025-08-03 16:30:46 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-03 16:30:48 - views.main_window - INFO - Changement vers la page: articles
2025-08-03 16:30:48 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-03 16:30:49 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-03 16:30:49 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-03 16:30:50 - views.main_window - INFO - Changement vers la page: orders
2025-08-03 16:30:50 - controllers.order_controller - INFO - OrderController initialisé
2025-08-03 16:30:50 - views.orders_window - ERROR - Erreur lors de la création des cartes de stats: 'OrderController' object has no attribute 'get_order_statistics'
2025-08-03 16:30:50 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-03 16:30:51 - views.main_window - INFO - Changement vers la page: reports
2025-08-03 16:30:51 - controllers.report_controller - INFO - ReportController initialisé
2025-08-03 16:30:51 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-03 16:30:51 - controllers.order_controller - INFO - OrderController initialisé
2025-08-03 16:30:51 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-03 16:30:54 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-03 16:30:59 - views.main_window - INFO - Thème changé: classic (light)
2025-08-03 16:30:59 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-03 16:31:01 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-03 16:31:01 - views.main_window - INFO - Module tableau de bord affiché
2025-08-03 16:31:02 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-03 16:31:02 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-03 16:31:05 - src.app - INFO - Fermeture de l'application
2025-08-03 16:31:05 - database.manager - INFO - Connexion à la base de données fermée
2025-08-03 16:31:13 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 16:31:13 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 16:31:13 - database.manager - INFO - Tables créées avec succès
2025-08-03 16:31:13 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 16:31:13 - database.manager - INFO - Connexion à la base de données fermée
2025-08-03 16:31:13 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 16:31:13 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 16:31:13 - database.manager - INFO - Tables créées avec succès
2025-08-03 16:31:13 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 16:31:13 - src.app - INFO - Base de données initialisée avec succès
2025-08-03 16:37:22 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 16:37:22 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 16:37:22 - database.manager - INFO - Tables créées avec succès
2025-08-03 16:37:22 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 16:37:22 - src.app - INFO - Base de données initialisée avec succès
2025-08-03 16:37:22 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 16:37:25 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 16:37:25 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 16:37:27 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-03 16:37:27 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-03 16:37:27 - controllers.order_controller - INFO - OrderController initialisé
2025-08-03 16:37:27 - controllers.report_controller - INFO - ReportController initialisé
2025-08-03 16:37:27 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-03 16:37:27 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-03 16:37:27 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-03 16:37:27 - views.main_window - INFO - Module tableau de bord affiché
2025-08-03 16:37:27 - src.app - INFO - Fenêtre principale ouverte
2025-08-03 16:37:31 - views.main_window - INFO - Changement vers la page: articles
2025-08-03 16:37:31 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-03 16:37:32 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-03 16:37:32 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-03 16:37:33 - views.main_window - INFO - Changement vers la page: movements
2025-08-03 16:37:33 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors de la création des cartes de stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'name'
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors du chargement des statistiques: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-03 16:37:33 - views.movements_window - ERROR - Erreur lors de la mise à jour des stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-03 16:37:33 - views.movements_window - INFO - 5 mouvements chargés
2025-08-03 16:37:33 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-03 16:37:33 - views.main_window - INFO - Module mouvements affiché
2025-08-03 16:37:34 - views.main_window - INFO - Changement vers la page: orders
2025-08-03 16:37:34 - controllers.order_controller - INFO - OrderController initialisé
2025-08-03 16:37:34 - views.orders_window - ERROR - Erreur lors de la création des cartes de stats: 'OrderController' object has no attribute 'get_order_statistics'
2025-08-03 16:37:34 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-03 17:31:36 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:31:36 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:31:37 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:31:37 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:31:37 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:31:37 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:35:07 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:35:07 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:35:07 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: no such column: name
2025-08-03 17:36:08 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:36:08 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:36:10 - database.manager - INFO - Index créés avec succès
2025-08-03 17:36:11 - database.manager - INFO - Utilisateur admin par défaut créé: admin
2025-08-03 17:36:11 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:36:11 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:36:30 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:36:30 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:36:30 - database.manager - INFO - Index créés avec succès
2025-08-03 17:36:30 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:36:30 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:36:30 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 17:36:30 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 17:39:41 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:39:41 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:39:41 - database.manager - INFO - Index créés avec succès
2025-08-03 17:39:41 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:39:41 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:39:41 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 17:39:41 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 17:39:47 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 17:39:47 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 17:39:49 - src.app - ERROR - Erreur lors de l'ouverture de la fenêtre principale: 'dashboard'
2025-08-03 17:48:56 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:48:56 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:48:56 - database.manager - INFO - Index créés avec succès
2025-08-03 17:48:56 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:48:56 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:48:56 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 17:48:56 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 17:48:59 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 17:48:59 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 17:49:01 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'MONEY'
2025-08-03 17:49:01 - views.products_widget - INFO - 0 produits chargés
2025-08-03 17:49:01 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 17:49:01 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 17:49:01 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module clients: type object 'FluentIcon' has no attribute 'STAR'
2025-08-03 17:49:01 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 17:49:01 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 17:49:01 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 17:49:01 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 17:49:01 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 17:49:01 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 17:49:01 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 17:49:01 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 17:49:01 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 17:49:13 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 17:51:11 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:51:11 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:51:11 - database.manager - INFO - Index créés avec succès
2025-08-03 17:51:11 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:51:11 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:51:11 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 17:51:52 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:51:52 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:51:54 - database.manager - INFO - Index créés avec succès
2025-08-03 17:51:55 - database.manager - INFO - Utilisateur admin par défaut créé: admin
2025-08-03 17:51:55 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:51:55 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:51:55 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 17:51:55 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 17:52:04 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 17:52:04 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 17:52:05 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'MONEY'
2025-08-03 17:52:06 - views.products_widget - INFO - 0 produits chargés
2025-08-03 17:52:06 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 17:52:06 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 17:52:06 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module clients: type object 'FluentIcon' has no attribute 'STAR'
2025-08-03 17:52:06 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 17:52:06 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 17:52:06 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 17:52:06 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 17:52:06 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 17:52:06 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 17:52:06 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 17:52:06 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 17:52:06 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 17:52:09 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 17:52:51 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:52:51 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:52:51 - database.manager - INFO - Index créés avec succès
2025-08-03 17:52:51 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:52:51 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:52:51 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 17:52:51 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 17:52:54 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 17:52:54 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 17:52:57 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'MONEY'
2025-08-03 17:52:57 - views.products_widget - INFO - 0 produits chargés
2025-08-03 17:52:57 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 17:52:57 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 17:52:57 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module clients: type object 'FluentIcon' has no attribute 'STAR'
2025-08-03 17:52:57 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 17:52:57 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 17:52:57 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 17:52:57 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 17:52:57 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 17:52:57 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 17:52:57 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 17:52:57 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 17:52:57 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 17:53:02 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 17:54:53 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 17:54:53 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 17:54:53 - database.manager - INFO - Index créés avec succès
2025-08-03 17:54:53 - database.manager - INFO - Tables créées avec succès
2025-08-03 17:54:53 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 17:54:53 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 17:54:53 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 17:55:17 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 17:55:17 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 17:55:19 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 17:55:19 - views.products_widget - INFO - 0 produits chargés
2025-08-03 17:55:19 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 17:55:19 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 17:55:19 - views.clients_widget - INFO - 0 clients chargés
2025-08-03 17:55:19 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 17:55:19 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 17:55:19 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 17:55:19 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 17:55:19 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 17:55:19 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 17:55:19 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 17:55:19 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 17:55:19 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 17:55:19 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 17:55:19 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 17:55:24 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 18:06:44 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:06:44 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:06:44 - database.manager - INFO - Index créés avec succès
2025-08-03 18:06:44 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:06:44 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:06:44 - database.manager - INFO - Catégorie créée: Électronique (ID: 1)
2025-08-03 18:06:44 - database.manager - INFO - Catégorie créée: Informatique (ID: 2)
2025-08-03 18:06:44 - database.manager - INFO - Catégorie créée: Mobilier (ID: 3)
2025-08-03 18:06:44 - database.manager - INFO - Catégorie créée: Papeterie (ID: 4)
2025-08-03 18:06:44 - database.manager - INFO - Catégorie créée: Alimentaire (ID: 5)
2025-08-03 18:06:45 - database.manager - INFO - Produit créé: Ordinateur Portable Dell (ID: 1)
2025-08-03 18:06:45 - database.manager - INFO - Produit créé: Souris Logitech (ID: 2)
2025-08-03 18:06:45 - database.manager - INFO - Produit créé: Chaise de Bureau (ID: 3)
2025-08-03 18:06:45 - database.manager - INFO - Produit créé: Smartphone Samsung (ID: 4)
2025-08-03 18:06:45 - database.manager - INFO - Produit créé: Ramette Papier A4 (ID: 5)
2025-08-03 18:06:45 - database.manager - INFO - Produit créé: Café en Grains (ID: 6)
2025-08-03 18:06:46 - database.manager - INFO - Produit créé: Écran 24 pouces (ID: 7)
2025-08-03 18:06:46 - database.manager - INFO - Produit créé: Stylos Bic (ID: 8)
2025-08-03 18:06:46 - database.manager - INFO - Produit créé: Tablette iPad (ID: 9)
2025-08-03 18:06:46 - database.manager - INFO - Produit créé: Bureau en Bois (ID: 10)
2025-08-03 18:08:56 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:08:56 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:08:56 - database.manager - INFO - Index créés avec succès
2025-08-03 18:08:56 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:08:56 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:08:56 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 18:08:56 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 18:08:59 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 18:08:59 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 18:09:01 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:09:01 - views.products_widget - INFO - 10 produits chargés
2025-08-03 18:09:01 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 18:09:01 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 18:09:01 - views.clients_widget - INFO - 0 clients chargés
2025-08-03 18:09:01 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 18:09:01 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 18:09:01 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 18:09:01 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 18:09:01 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 18:09:01 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 18:09:01 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 18:09:01 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:09:01 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 18:09:01 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 18:09:01 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 18:09:06 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 18:12:48 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:12:48 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:12:48 - database.manager - INFO - Index créés avec succès
2025-08-03 18:12:48 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:12:48 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:12:48 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 18:12:48 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 18:12:51 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 18:12:51 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 18:12:52 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:12:52 - views.products_widget - INFO - 10 produits chargés
2025-08-03 18:12:52 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 18:12:52 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 18:12:52 - views.clients_widget - INFO - 0 clients chargés
2025-08-03 18:12:52 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 18:12:52 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 18:12:52 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 18:12:52 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 18:12:52 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 18:12:52 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 18:12:52 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 18:12:52 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:12:52 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 18:12:52 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 18:12:52 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 18:12:56 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 18:26:47 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:26:47 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:26:47 - database.manager - INFO - Index créés avec succès
2025-08-03 18:26:47 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:26:47 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:26:47 - database.manager - INFO - Client créé: Jean Dupont (ID: 1)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: Marie Martin (ID: 2)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: Pierre Durand (ID: 3)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: Sophie Leroy (ID: 4)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: Michel Bernard (ID: 5)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: Entreprise TechSolutions (ID: 6)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: Isabelle Moreau (ID: 7)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: François Petit (ID: 8)
2025-08-03 18:26:48 - database.manager - INFO - Client créé: Association Les Amis du Livre (ID: 9)
2025-08-03 18:26:49 - database.manager - INFO - Client créé: Catherine Rousseau (ID: 10)
2025-08-03 18:30:49 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:30:49 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:30:49 - database.manager - INFO - Index créés avec succès
2025-08-03 18:30:49 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:30:49 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:30:49 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 18:30:49 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 18:30:52 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 18:30:52 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 18:30:53 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:30:54 - views.products_widget - INFO - 10 produits chargés
2025-08-03 18:30:54 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 18:30:54 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 18:30:54 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 18:30:54 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 18:30:54 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 18:30:54 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 18:30:54 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 18:30:54 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 18:30:54 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 18:30:54 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 18:30:54 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:30:54 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 18:30:54 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 18:30:54 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 18:31:19 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 18:40:20 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:40:20 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:40:20 - database.manager - INFO - Index créés avec succès
2025-08-03 18:40:20 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:40:20 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:40:20 - database.manager - INFO - Fournisseur créé: TechDistrib France (ID: 1)
2025-08-03 18:40:20 - database.manager - INFO - Fournisseur créé: Informatique Solutions (ID: 2)
2025-08-03 18:40:20 - database.manager - INFO - Fournisseur créé: Bureau & Mobilier Pro (ID: 3)
2025-08-03 18:40:20 - database.manager - INFO - Fournisseur créé: Papeterie Centrale (ID: 4)
2025-08-03 18:40:20 - database.manager - INFO - Fournisseur créé: ElectroDistrib (ID: 5)
2025-08-03 18:40:21 - database.manager - INFO - Fournisseur créé: Alimentaire Gourmet (ID: 6)
2025-08-03 18:40:21 - database.manager - INFO - Fournisseur créé: Import-Export Global (ID: 7)
2025-08-03 18:40:21 - database.manager - INFO - Fournisseur créé: Équipements Professionnels (ID: 8)
2025-08-03 18:40:21 - database.manager - INFO - Fournisseur créé: Fournitures Express (ID: 9)
2025-08-03 18:40:21 - database.manager - INFO - Fournisseur créé: Eco-Fournisseurs (ID: 10)
2025-08-03 18:41:13 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:41:13 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:41:13 - database.manager - INFO - Index créés avec succès
2025-08-03 18:41:13 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:41:13 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:41:13 - src.app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 18:41:13 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 18:41:39 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 18:41:39 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 18:41:41 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:41:41 - views.products_widget - INFO - 10 produits chargés
2025-08-03 18:41:41 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 18:41:41 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 18:41:41 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 18:41:41 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 18:41:41 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 18:41:41 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 18:41:41 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 18:41:41 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 18:41:41 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 18:41:41 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 18:41:41 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 18:41:41 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 18:41:41 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:41:41 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 18:41:41 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 18:41:41 - src.app - INFO - Fenêtre principale moderne ouverte
2025-08-03 18:41:47 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 18:48:42 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:48:42 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:48:42 - database.manager - INFO - Index créés avec succès
2025-08-03 18:48:42 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:48:42 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:48:42 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 18:48:42 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 18:49:23 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 18:49:23 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 18:49:25 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'DOLLAR'
2025-08-03 18:49:25 - views.products_widget - INFO - 10 produits chargés
2025-08-03 18:49:25 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 18:49:25 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 18:49:25 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 18:49:25 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 18:49:25 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 18:49:25 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 18:49:25 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 18:49:25 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 18:49:25 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 18:49:25 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 18:49:25 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 18:49:25 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 18:49:25 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: The object name of `interface` can't be empty string.
2025-08-03 18:49:25 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 18:49:25 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 18:49:25 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 18:49:41 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 18:52:09 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:52:09 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:52:09 - database.manager - INFO - Index créés avec succès
2025-08-03 18:52:09 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:52:09 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:52:09 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 18:52:09 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 18:53:33 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 18:53:33 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 18:53:34 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 18:53:34 - views.products_widget - INFO - 10 produits chargés
2025-08-03 18:53:34 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 18:53:34 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 18:53:34 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 18:53:34 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 18:53:34 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 18:53:34 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 18:53:34 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 18:53:34 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 18:53:34 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 18:53:34 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 18:53:34 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 18:53:34 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 18:53:34 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 18:53:34 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 18:53:34 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 18:53:34 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 18:53:42 - views.dialogs.product_dialog - INFO - Dialogue produit ouvert (édition)
2025-08-03 18:53:49 - views.dialogs.client_dialog - INFO - Dialogue client ouvert (édition)
2025-08-03 18:53:55 - views.dialogs.client_dialog - INFO - Dialogue client ouvert (création)
2025-08-03 18:54:00 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 18:57:25 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 18:57:25 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 18:57:25 - database.manager - INFO - Index créés avec succès
2025-08-03 18:57:25 - database.manager - INFO - Tables créées avec succès
2025-08-03 18:57:25 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 18:57:25 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 18:57:25 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 18:57:30 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 18:57:30 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 18:57:31 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module dashboard: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 18:57:31 - views.products_widget - INFO - 10 produits chargés
2025-08-03 18:57:31 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 18:57:31 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 18:57:32 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 18:57:32 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 18:57:32 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 18:57:32 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 18:57:32 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 18:57:32 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 18:57:32 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 18:57:32 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 18:57:32 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 18:57:32 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 18:57:32 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: type object 'FluentIcon' has no attribute 'CONTACT'
2025-08-03 18:57:32 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 18:57:32 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 18:57:32 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 18:57:41 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 19:03:12 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 19:03:12 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 19:03:12 - database.manager - INFO - Index créés avec succès
2025-08-03 19:03:12 - database.manager - INFO - Tables créées avec succès
2025-08-03 19:03:12 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 19:03:12 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 19:03:12 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 19:03:15 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 19:03:15 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 19:03:17 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:03:17 - views.dashboard_widget - INFO - Dashboard initialisé
2025-08-03 19:03:17 - views.modern_main_window - INFO - Module dashboard initialisé: Tableau de bord avec KPIs
2025-08-03 19:03:17 - views.products_widget - INFO - 10 produits chargés
2025-08-03 19:03:17 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 19:03:17 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 19:03:17 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 19:03:17 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 19:03:17 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 19:03:17 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 19:03:17 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 19:03:17 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 19:03:17 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 19:03:17 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 19:03:17 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 19:03:17 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 19:03:17 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 19:03:17 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 19:03:17 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:03:17 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 19:03:17 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 19:03:41 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 19:06:06 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 19:06:06 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 19:06:06 - database.manager - INFO - Index créés avec succès
2025-08-03 19:06:06 - database.manager - INFO - Tables créées avec succès
2025-08-03 19:06:06 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 19:06:06 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 19:07:59 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 19:07:59 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 19:07:59 - database.manager - INFO - Index créés avec succès
2025-08-03 19:07:59 - database.manager - INFO - Tables créées avec succès
2025-08-03 19:07:59 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 19:07:59 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 19:07:59 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 19:08:02 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 19:08:02 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 19:08:04 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:08:04 - views.dashboard_widget - INFO - Dashboard initialisé
2025-08-03 19:08:04 - views.modern_main_window - INFO - Module dashboard initialisé: Tableau de bord avec KPIs
2025-08-03 19:08:04 - views.products_widget - INFO - 10 produits chargés
2025-08-03 19:08:04 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 19:08:04 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 19:08:04 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 19:08:04 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 19:08:04 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 19:08:04 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 19:08:04 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 19:08:04 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 19:08:04 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 19:08:04 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 19:08:04 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 19:08:04 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 19:08:05 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 19:08:05 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 19:08:05 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:08:05 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 19:08:05 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 19:08:16 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 19:16:17 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 19:16:17 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 19:16:17 - database.manager - INFO - Index créés avec succès
2025-08-03 19:16:17 - database.manager - INFO - Tables créées avec succès
2025-08-03 19:16:17 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 19:16:17 - database.manager - ERROR - Erreur lors de la création de la vente: table sales has no column named discount
2025-08-03 19:27:53 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 19:27:53 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 19:27:53 - database.manager - INFO - Index créés avec succès
2025-08-03 19:27:53 - database.manager - INFO - Tables créées avec succès
2025-08-03 19:27:53 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 19:27:53 - database.manager - INFO - Vente créée: 91.7898€ (ID: 1)
2025-08-03 19:27:53 - database.manager - INFO - Vente créée: 1533.5352000000003€ (ID: 2)
2025-08-03 19:27:54 - database.manager - INFO - Vente créée: 2815.1184000000003€ (ID: 3)
2025-08-03 19:27:54 - database.manager - INFO - Vente créée: 377.94000000000005€ (ID: 4)
2025-08-03 19:27:54 - database.manager - INFO - Vente créée: 191.91599999999997€ (ID: 5)
2025-08-03 19:27:54 - database.manager - INFO - Vente créée: 1919.976€ (ID: 6)
2025-08-03 19:27:54 - database.manager - INFO - Vente créée: 1217.88€ (ID: 7)
2025-08-03 19:27:54 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:54 - database.manager - INFO - Vente créée: 683.9544€ (ID: 8)
2025-08-03 19:27:55 - database.manager - INFO - Vente créée: 797.9886€ (ID: 9)
2025-08-03 19:27:55 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:55 - database.manager - INFO - Vente créée: 3148.6277999999998€ (ID: 10)
2025-08-03 19:27:55 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:55 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:55 - database.manager - INFO - Vente créée: 853.1784€ (ID: 11)
2025-08-03 19:27:55 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:55 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:55 - database.manager - INFO - Vente créée: 21.369€ (ID: 12)
2025-08-03 19:27:55 - database.manager - INFO - Vente créée: 3050.8704000000002€ (ID: 13)
2025-08-03 19:27:55 - database.manager - INFO - Vente créée: 924.4602€ (ID: 14)
2025-08-03 19:27:55 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:55 - database.manager - INFO - Vente créée: 25.449000000000005€ (ID: 15)
2025-08-03 19:27:55 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 17.043€ (ID: 16)
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 911.9886€ (ID: 17)
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 50.9796€ (ID: 18)
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 591.5592€ (ID: 19)
2025-08-03 19:27:56 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:56 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 3191.9544€ (ID: 20)
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 3455.9568€ (ID: 21)
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 173.1432€ (ID: 22)
2025-08-03 19:27:56 - database.manager - INFO - Vente créée: 1965.5136000000002€ (ID: 23)
2025-08-03 19:27:57 - database.manager - INFO - Vente créée: 4121.0088€ (ID: 24)
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:57 - database.manager - INFO - Vente créée: 1681.4430000000002€ (ID: 25)
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:57 - database.manager - INFO - Vente créée: 3072.1481999999996€ (ID: 26)
2025-08-03 19:27:57 - database.manager - INFO - Vente créée: 1927.6811999999998€ (ID: 27)
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:57 - database.manager - INFO - Vente créée: 89.964€ (ID: 28)
2025-08-03 19:27:57 - database.manager - INFO - Vente créée: 458.94899999999996€ (ID: 29)
2025-08-03 19:27:57 - database.manager - INFO - Vente créée: 1621.7286000000001€ (ID: 30)
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:57 - database.manager - WARNING - Stock insuffisant pour le produit ID: 1
2025-08-03 19:27:58 - database.manager - INFO - Vente créée: 170.9886€ (ID: 31)
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:58 - database.manager - INFO - Vente créée: 305.9796€ (ID: 32)
2025-08-03 19:27:58 - database.manager - INFO - Vente créée: 102.5886€ (ID: 33)
2025-08-03 19:27:58 - database.manager - INFO - Vente créée: 3.4086€ (ID: 34)
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 9
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 7
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 3
2025-08-03 19:27:58 - database.manager - WARNING - Stock insuffisant pour le produit ID: 1
2025-08-03 19:27:58 - database.manager - INFO - Vente créée: 79.51919999999998€ (ID: 35)
2025-08-03 19:29:18 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 19:29:18 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 19:29:18 - database.manager - INFO - Index créés avec succès
2025-08-03 19:29:18 - database.manager - INFO - Tables créées avec succès
2025-08-03 19:29:18 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 19:29:18 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 19:29:18 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 19:29:21 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 19:29:21 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 19:29:23 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:29:23 - views.dashboard_widget - INFO - Dashboard initialisé
2025-08-03 19:29:23 - views.modern_main_window - INFO - Module dashboard initialisé: Tableau de bord avec KPIs
2025-08-03 19:29:23 - views.products_widget - INFO - 10 produits chargés
2025-08-03 19:29:23 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 19:29:23 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 19:29:23 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 19:29:23 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 19:29:23 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 19:29:23 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 19:29:23 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 19:29:23 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 19:29:23 - views.sales_widget - INFO - 35 ventes chargées
2025-08-03 19:29:23 - views.sales_widget - INFO - Widget ventes initialisé
2025-08-03 19:29:23 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 19:29:23 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 19:29:23 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 19:29:23 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 19:29:23 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 19:29:23 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 19:29:23 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:29:23 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 19:29:23 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 19:29:31 - views.dialogs.supplier_dialog - INFO - Dialogue fournisseur ouvert (édition)
2025-08-03 19:29:52 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:30:04 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 19:35:11 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 19:35:11 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 19:35:11 - database.manager - INFO - Index créés avec succès
2025-08-03 19:35:11 - database.manager - INFO - Tables créées avec succès
2025-08-03 19:35:11 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 19:35:11 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 19:35:11 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 19:35:14 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 19:35:14 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 19:35:27 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:35:27 - views.dashboard_widget - INFO - Dashboard initialisé
2025-08-03 19:35:27 - views.modern_main_window - INFO - Module dashboard initialisé: Tableau de bord avec KPIs
2025-08-03 19:35:27 - views.products_widget - INFO - 10 produits chargés
2025-08-03 19:35:27 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 19:35:27 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 19:35:27 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 19:35:27 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 19:35:27 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 19:35:27 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 19:35:27 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 19:35:27 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 19:35:27 - views.sales_widget - INFO - 35 ventes chargées
2025-08-03 19:35:27 - views.sales_widget - INFO - Widget ventes initialisé
2025-08-03 19:35:27 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 19:35:27 - views.settings_widget - INFO - Widget paramètres initialisé
2025-08-03 19:35:27 - views.modern_main_window - INFO - Module settings initialisé: Configuration et thèmes
2025-08-03 19:35:27 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 19:35:27 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 19:35:27 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 19:35:27 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:35:27 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 19:35:28 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 19:35:54 - views.dialogs.client_dialog - INFO - Dialogue client ouvert (création)
2025-08-03 19:35:56 - views.dialogs.loyalty_points_dialog - INFO - Dialogue points de fidélité ouvert pour: Marie Martin
2025-08-03 19:35:57 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:36:10 - views.dialogs.client_dialog - INFO - Dialogue client ouvert (édition)
2025-08-03 19:36:13 - views.dialogs.client_dialog - INFO - Dialogue client ouvert (édition)
2025-08-03 19:36:22 - views.dialogs.supplier_dialog - INFO - Dialogue fournisseur ouvert (édition)
2025-08-03 19:36:27 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:36:57 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 19:37:07 - views.dialogs.sale_dialog - INFO - Dialogue vente ouvert (création)
2025-08-03 20:20:20 - ui.theme_manager - INFO - 0 thèmes personnalisés chargés
2025-08-03 20:20:20 - ui.theme_manager - INFO - Gestionnaire de thèmes moderne initialisé
2025-08-03 20:20:20 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 20:20:20 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 20:20:20 - database.manager - INFO - Index créés avec succès
2025-08-03 20:20:20 - database.manager - INFO - Tables créées avec succès
2025-08-03 20:20:20 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 20:20:20 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 20:20:20 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 20:20:23 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 20:20:23 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 20:20:25 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:20:25 - views.dashboard_widget - INFO - Dashboard initialisé
2025-08-03 20:20:25 - views.modern_main_window - INFO - Module dashboard initialisé: Tableau de bord avec KPIs
2025-08-03 20:20:25 - views.products_widget - INFO - 10 produits chargés
2025-08-03 20:20:25 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 20:20:25 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 20:20:25 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 20:20:25 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 20:20:25 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 20:20:25 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 20:20:25 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 20:20:25 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 20:20:25 - views.sales_widget - INFO - 35 ventes chargées
2025-08-03 20:20:25 - views.sales_widget - INFO - Widget ventes initialisé
2025-08-03 20:20:25 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 20:20:25 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module settings: name 'QGridLayout' is not defined
2025-08-03 20:20:25 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 20:20:25 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 20:20:25 - views.modern_main_window - ERROR - Erreur lors de la connexion des signaux: 'QWidget' object has no attribute 'theme_changed'
2025-08-03 20:20:25 - views.modern_main_window - INFO - Animations configurées
2025-08-03 20:20:25 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 20:20:25 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:20:25 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 20:20:25 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 20:20:41 - views.dialogs.product_dialog - INFO - Dialogue produit ouvert (édition)
2025-08-03 20:20:46 - views.dialogs.client_dialog - INFO - Dialogue client ouvert (édition)
2025-08-03 20:20:50 - views.dialogs.supplier_dialog - INFO - Dialogue fournisseur ouvert (édition)
2025-08-03 20:20:55 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:21:09 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
2025-08-03 20:31:18 - ui.theme_manager - INFO - 0 thèmes personnalisés chargés
2025-08-03 20:31:18 - ui.theme_manager - INFO - Gestionnaire de thèmes moderne initialisé
2025-08-03 20:31:18 - database.manager - INFO - Initialisation de la base de données...
2025-08-03 20:31:18 - database.manager - INFO - Connexion à la base de données établie
2025-08-03 20:31:18 - database.manager - INFO - Index créés avec succès
2025-08-03 20:31:18 - database.manager - INFO - Tables créées avec succès
2025-08-03 20:31:18 - database.manager - INFO - Base de données initialisée avec succès
2025-08-03 20:31:18 - app - INFO - Base de données initialisée avec le nouveau schéma
2025-08-03 20:31:18 - app - INFO - Démarrage de GSlim v1.0.0
2025-08-03 20:31:21 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-03 20:31:21 - app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-03 20:31:22 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:31:22 - views.dashboard_widget - INFO - Dashboard initialisé
2025-08-03 20:31:22 - views.modern_main_window - INFO - Module dashboard initialisé: Tableau de bord avec KPIs
2025-08-03 20:31:22 - views.products_widget - INFO - 10 produits chargés
2025-08-03 20:31:22 - views.products_widget - INFO - Widget produits initialisé
2025-08-03 20:31:22 - views.modern_main_window - INFO - Module products initialisé: Gestion complète des produits
2025-08-03 20:31:22 - views.clients_widget - INFO - 10 clients chargés
2025-08-03 20:31:22 - views.clients_widget - INFO - Widget clients initialisé
2025-08-03 20:31:22 - views.modern_main_window - INFO - Module clients initialisé: Gestion avec points de fidélité
2025-08-03 20:31:22 - views.suppliers_widget - INFO - 10 fournisseurs chargés
2025-08-03 20:31:22 - views.suppliers_widget - INFO - Widget fournisseurs initialisé
2025-08-03 20:31:22 - views.modern_main_window - INFO - Module suppliers initialisé: Gestion avec évaluation
2025-08-03 20:31:23 - views.sales_widget - INFO - 35 ventes chargées
2025-08-03 20:31:23 - views.sales_widget - INFO - Widget ventes initialisé
2025-08-03 20:31:23 - views.modern_main_window - INFO - Module sales initialisé: Système complet de ventes
2025-08-03 20:31:23 - views.modern_main_window - ERROR - Erreur lors de l'initialisation du module settings: name 'QGridLayout' is not defined
2025-08-03 20:31:23 - views.modern_main_window - INFO - 6 modules initialisés
2025-08-03 20:31:23 - views.modern_main_window - ERROR - Erreur lors de l'ajout des éléments de navigation: 'NavigationItemPosition' object has no attribute 'objectName'
2025-08-03 20:31:23 - views.modern_main_window - ERROR - Erreur lors de la connexion des signaux: 'QWidget' object has no attribute 'theme_changed'
2025-08-03 20:31:23 - views.modern_main_window - INFO - Animations configurées
2025-08-03 20:31:23 - views.modern_main_window - INFO - Fenêtre principale moderne initialisée
2025-08-03 20:31:23 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:31:23 - views.modern_main_window - INFO - Utilisateur défini: admin
2025-08-03 20:31:23 - app - INFO - Fenêtre principale moderne ouverte
2025-08-03 20:31:52 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:32:22 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:32:52 - views.dashboard_widget - INFO - Données du dashboard actualisées
2025-08-03 20:33:20 - views.modern_main_window - INFO - Fermeture de la fenêtre principale
