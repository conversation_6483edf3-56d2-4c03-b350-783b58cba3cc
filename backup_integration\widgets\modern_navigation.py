"""
Navigation moderne avec animations et effets visuels
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QSizePolicy, QSpacerItem
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect,
    QTimer, QParallelAnimationGroup, QSize, QPoint
)
from PyQt5.QtGui import (
    QFont, QPainter, QColor, QPen, QBrush, QPainterPath,
    QLinearGradient, QIcon, QPixmap
)

try:
    from qfluentwidgets import FluentIcon, NavigationInterface
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from styles.modern_theme import ModernTheme


class ModernNavigationItem(QPushButton):
    """Élément de navigation moderne avec animations"""
    
    activated = pyqtSignal(str)
    
    def __init__(self, route_key: str, text: str, icon=None, parent=None):
        super().__init__(text, parent)
        self.route_key = route_key
        self.text = text
        self.icon = icon
        self.is_active = False
        self.is_hovered = False
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setFixedHeight(48)
        self.setMinimumWidth(200)
        self.setCursor(Qt.PointingHandCursor)
        
        # Style de base
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: transparent;
                border: none;
                border-radius: {ModernTheme.BORDER_RADIUS_MD};
                padding: 12px 16px;
                text-align: left;
                font-size: 14px;
                font-weight: 500;
                color: {ModernTheme.LIGHT_TEXT_SECONDARY};
            }}
            QPushButton:hover {{
                background-color: {ModernTheme.LIGHT_BACKGROUND_TERTIARY};
                color: {ModernTheme.LIGHT_TEXT_PRIMARY};
            }}
        """)
        
        # Indicateur d'activation
        self.active_indicator = QFrame(self)
        self.active_indicator.setFixedSize(4, 24)
        self.active_indicator.setStyleSheet(f"""
            background-color: {ModernTheme.PRIMARY_600};
            border-radius: 2px;
        """)
        self.active_indicator.move(0, 12)
        self.active_indicator.hide()
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de l'indicateur actif
        self.indicator_animation = QPropertyAnimation(self.active_indicator, b"geometry")
        self.indicator_animation.setDuration(200)
        self.indicator_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Animation de survol
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(150)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def set_active(self, active: bool):
        """Définir l'état actif avec animation"""
        self.is_active = active
        
        if active:
            # Afficher l'indicateur avec animation
            self.active_indicator.show()
            start_rect = QRect(0, 12, 0, 24)
            end_rect = QRect(0, 12, 4, 24)
            
            self.indicator_animation.setStartValue(start_rect)
            self.indicator_animation.setEndValue(end_rect)
            self.indicator_animation.start()
            
            # Style actif
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: {ModernTheme.PRIMARY_50};
                    border: none;
                    border-radius: {ModernTheme.BORDER_RADIUS_MD};
                    padding: 12px 16px;
                    text-align: left;
                    font-size: 14px;
                    font-weight: 600;
                    color: {ModernTheme.PRIMARY_700};
                }}
            """)
        else:
            # Masquer l'indicateur
            self.active_indicator.hide()
            
            # Style inactif
            self.setStyleSheet(f"""
                QPushButton {{
                    background-color: transparent;
                    border: none;
                    border-radius: {ModernTheme.BORDER_RADIUS_MD};
                    padding: 12px 16px;
                    text-align: left;
                    font-size: 14px;
                    font-weight: 500;
                    color: {ModernTheme.LIGHT_TEXT_SECONDARY};
                }}
                QPushButton:hover {{
                    background-color: {ModernTheme.LIGHT_BACKGROUND_TERTIARY};
                    color: {ModernTheme.LIGHT_TEXT_PRIMARY};
                }}
            """)
    
    def enterEvent(self, event):
        """Animation d'entrée de survol"""
        if not self.is_active:
            self.is_hovered = True
            current_rect = self.geometry()
            new_rect = QRect(
                current_rect.x() + 4,
                current_rect.y(),
                current_rect.width(),
                current_rect.height()
            )
            
            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(new_rect)
            self.hover_animation.start()
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation de sortie de survol"""
        if not self.is_active:
            self.is_hovered = False
            current_rect = self.geometry()
            new_rect = QRect(
                current_rect.x() - 4,
                current_rect.y(),
                current_rect.width(),
                current_rect.height()
            )
            
            self.hover_animation.setStartValue(current_rect)
            self.hover_animation.setEndValue(new_rect)
            self.hover_animation.start()
        
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Gérer le clic"""
        if event.button() == Qt.LeftButton:
            self.activated.emit(self.route_key)
        super().mousePressEvent(event)


class ModernNavigationPanel(QWidget):
    """Panneau de navigation moderne"""
    
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.navigation_items = {}
        self.current_active = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setFixedWidth(250)
        self.setProperty("class", "navigation")
        
        # Layout principal
        layout = QVBoxLayout()
        layout.setContentsMargins(16, 20, 16, 20)
        layout.setSpacing(8)
        
        # En-tête avec logo/titre
        self.create_header(layout)
        
        # Zone de navigation
        self.nav_scroll = QScrollArea()
        self.nav_scroll.setWidgetResizable(True)
        self.nav_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.nav_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.nav_scroll.setFrameShape(QFrame.NoFrame)
        
        self.nav_widget = QWidget()
        self.nav_layout = QVBoxLayout(self.nav_widget)
        self.nav_layout.setContentsMargins(0, 0, 0, 0)
        self.nav_layout.setSpacing(4)
        
        self.nav_scroll.setWidget(self.nav_widget)
        layout.addWidget(self.nav_scroll)
        
        # Spacer pour pousser le contenu vers le haut
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # Pied de page (optionnel)
        self.create_footer(layout)
        
        self.setLayout(layout)
    
    def create_header(self, layout):
        """Créer l'en-tête de navigation"""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 16)
        header_layout.setSpacing(8)
        
        # Logo/Titre
        title_label = QLabel("GSlim")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: 700;
            color: {ModernTheme.LIGHT_TEXT_PRIMARY};
            margin-bottom: 4px;
        """)
        header_layout.addWidget(title_label)
        
        # Sous-titre
        subtitle_label = QLabel("Gestion de Stock")
        subtitle_label.setStyleSheet(f"""
            font-size: 12px;
            color: {ModernTheme.LIGHT_TEXT_MUTED};
            margin-bottom: 8px;
        """)
        header_layout.addWidget(subtitle_label)
        
        # Ligne de séparation
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet(f"""
            background-color: {ModernTheme.LIGHT_BORDER_COLOR};
            border: none;
            height: 1px;
        """)
        header_layout.addWidget(separator)
        
        layout.addWidget(header_frame)
    
    def create_footer(self, layout):
        """Créer le pied de page de navigation"""
        footer_frame = QFrame()
        footer_layout = QVBoxLayout(footer_frame)
        footer_layout.setContentsMargins(0, 16, 0, 0)
        
        # Version
        version_label = QLabel("Version 1.0.0")
        version_label.setStyleSheet(f"""
            font-size: 11px;
            color: {ModernTheme.LIGHT_TEXT_MUTED};
            text-align: center;
        """)
        version_label.setAlignment(Qt.AlignCenter)
        footer_layout.addWidget(version_label)
        
        layout.addWidget(footer_frame)
    
    def add_navigation_item(self, route_key: str, text: str, icon=None):
        """Ajouter un élément de navigation"""
        nav_item = ModernNavigationItem(route_key, text, icon)
        nav_item.activated.connect(self.on_item_activated)
        
        self.navigation_items[route_key] = nav_item
        self.nav_layout.addWidget(nav_item)
        
        return nav_item
    
    def on_item_activated(self, route_key: str):
        """Gérer l'activation d'un élément"""
        # Désactiver l'élément précédent
        if self.current_active and self.current_active in self.navigation_items:
            self.navigation_items[self.current_active].set_active(False)
        
        # Activer le nouvel élément
        if route_key in self.navigation_items:
            self.navigation_items[route_key].set_active(True)
            self.current_active = route_key
            self.page_changed.emit(route_key)
    
    def set_active_page(self, route_key: str):
        """Définir la page active"""
        self.on_item_activated(route_key)
