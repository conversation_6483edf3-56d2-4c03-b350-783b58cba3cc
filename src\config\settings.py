"""
Configuration de l'application GSlim - Version 1.0.2
Conforme au cahier des charges moderne
"""

import os
from pathlib import Path

# Essayer de charger dotenv, sinon utiliser les valeurs par défaut
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("python-dotenv non installé, utilisation des valeurs par défaut")

class Config:
    """Configuration de base de l'application selon le cahier des charges"""

    # Répertoire racine du projet
    ROOT_DIR = Path(__file__).parent.parent.parent

    # Application - Version 1.0.2
    APP_NAME = os.getenv("APP_NAME", "GSlim")
    APP_VERSION = os.getenv("APP_VERSION", "1.0.2")
    APP_AUTHOR = "Gnuslim/GSCOM"
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"

    # Base de données SQLite selon spécifications
    DATABASE_NAME = "gestion_stock.sqlite"
    DATABASE_PATH = ROOT_DIR / os.getenv("DATABASE_PATH", f"data/{DATABASE_NAME}")

    # Sécurité - Amélioration avec bcrypt
    SECRET_KEY = os.getenv("SECRET_KEY", "gslim-secret-key-change-this-in-production")
    BCRYPT_ROUNDS = int(os.getenv("BCRYPT_ROUNDS", "12"))

    # Utilisateur admin par défaut selon spécifications
    DEFAULT_ADMIN_USERNAME = os.getenv("DEFAULT_ADMIN_USERNAME", "admin")
    DEFAULT_ADMIN_PASSWORD = os.getenv("DEFAULT_ADMIN_PASSWORD", "admin123")

    # Interface utilisateur - Fluent Design
    DEFAULT_THEME = os.getenv("DEFAULT_THEME", "light")  # light/dark/system
    LANGUAGE = os.getenv("LANGUAGE", "fr")

    # CSS personnalisé selon spécifications
    CUSTOM_CSS_PATH = ROOT_DIR / "resources/styles/custom.css"

    # Devise selon spécifications
    DEFAULT_CURRENCY = os.getenv("DEFAULT_CURRENCY", "EUR")
    CURRENCY_SYMBOL = os.getenv("CURRENCY_SYMBOL", "€")

    # Répertoires
    REPORTS_DIR = ROOT_DIR / os.getenv("REPORTS_DIR", "reports")
    TEMP_DIR = ROOT_DIR / os.getenv("TEMP_DIR", "temp")
    DATA_DIR = ROOT_DIR / "data"
    RESOURCES_DIR = ROOT_DIR / "resources"
    STYLES_DIR = RESOURCES_DIR / "styles"

    # Interface utilisateur - Dimensions
    WINDOW_WIDTH = 1400
    WINDOW_HEIGHT = 900
    MIN_WIDTH = 1000
    MIN_HEIGHT = 700

    # Pagination et performance
    DEFAULT_PAGE_SIZE = 50
    MAX_PAGE_SIZE = 200

    # Seuils d'alerte stock
    DEFAULT_LOW_STOCK_THRESHOLD = 5
    DEFAULT_CRITICAL_STOCK_THRESHOLD = 0

    # TVA par défaut
    DEFAULT_TAX_RATE = 20.0  # 20% TVA

    # Email (optionnel pour notifications futures)
    SMTP_SERVER = os.getenv("SMTP_SERVER")
    SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USERNAME = os.getenv("SMTP_USERNAME")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD")
    SMTP_USE_TLS = os.getenv("SMTP_USE_TLS", "True").lower() == "true"
    
    @classmethod
    def ensure_directories(cls):
        """Créer les répertoires nécessaires s'ils n'existent pas"""
        directories = [
            cls.DATA_DIR,
            cls.REPORTS_DIR,
            cls.TEMP_DIR,
            cls.DATABASE_PATH.parent
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

# Instance de configuration globale
config = Config()

# Créer les répertoires au chargement du module
config.ensure_directories()
