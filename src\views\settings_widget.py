"""
Widget de paramètres conforme au cahier des charges
Configuration et thèmes selon les spécifications
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
    QPushButton, QFrame, QGroupBox
)
from PyQt5.QtCore import pyqtSignal
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        TitleLabel, CardWidget, ComboBox, PushButton, 
        FluentIcon, BodyLabel, setTheme, Theme
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from config.settings import config
from utils.logger import setup_logger
from ui.theme_manager import theme_manager
from ui.modern_components import AnimatedCard, ModernSearchBox


class SettingsWidget(QWidget):
    """
    Widget de paramètres selon les spécifications
    Changement de thème, gestion des catégories, utilisateurs
    """
    
    # Signaux
    error_occurred = pyqtSignal(str)
    success_message = pyqtSignal(str)
    info_message = pyqtSignal(str)
    theme_changed = pyqtSignal(str)
    logout_requested = pyqtSignal()

    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.current_user = None
        
        # Initialiser l'interface
        self._init_ui()
        
        self.logger.info("Widget paramètres initialisé")

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # En-tête
        self._create_header(main_layout)
        
        # Section thèmes
        self._create_theme_section(main_layout)
        
        # Section gestion
        self._create_management_section(main_layout)
        
        # Section utilisateur
        self._create_user_section(main_layout)
        
        main_layout.addStretch()

    def _create_header(self, layout):
        """Créer l'en-tête du module"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Paramètres")
        else:
            title = QLabel("Paramètres")
            title.setFont(QFont("Segoe UI", 20, QFont.Bold))
        
        layout.addWidget(title)

    def _create_theme_section(self, layout):
        """Créer la section de changement de thème moderne avec prévisualisation"""
        # Utiliser notre composant AnimatedCard moderne
        theme_card = AnimatedCard(
            "Apparence et Thèmes",
            "Personnalisez l'apparence de l'application avec des thèmes modernes"
        )

        # Layout pour le contenu de la carte
        content_layout = QVBoxLayout()
        theme_card.layout().addLayout(content_layout)

        # Grille de sélection de thèmes
        themes_grid = QGridLayout()
        themes_grid.setSpacing(15)

        # Récupérer les thèmes disponibles
        available_themes = theme_manager.get_available_themes()

        # Créer une carte pour chaque thème
        self.theme_cards = {}
        row, col = 0, 0

        for theme_key, theme_name in available_themes.items():
            theme_preview_card = self._create_theme_preview_card(theme_key, theme_name)
            self.theme_cards[theme_key] = theme_preview_card

            themes_grid.addWidget(theme_preview_card, row, col)

            col += 1
            if col >= 3:  # 3 colonnes
                col = 0
                row += 1

        content_layout.addLayout(themes_grid)

        # Section d'options avancées
        advanced_section = QFrame()
        advanced_layout = QVBoxLayout(advanced_section)

        # Titre des options avancées
        advanced_title = QLabel("Options Avancées")
        advanced_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        advanced_layout.addWidget(advanced_title)

        # Checkbox pour les animations
        from PyQt5.QtWidgets import QCheckBox
        self.animations_checkbox = QCheckBox("Activer les animations")
        self.animations_checkbox.setChecked(True)
        self.animations_checkbox.stateChanged.connect(self._on_animations_changed)
        advanced_layout.addWidget(self.animations_checkbox)

        # Checkbox pour les effets d'ombre
        self.shadows_checkbox = QCheckBox("Activer les effets d'ombre")
        self.shadows_checkbox.setChecked(True)
        self.shadows_checkbox.stateChanged.connect(self._on_shadows_changed)
        advanced_layout.addWidget(self.shadows_checkbox)

        content_layout.addWidget(advanced_section)

        layout.addWidget(theme_card)

    def _create_management_section(self, layout):
        """Créer la section de gestion selon spécifications"""
        if FLUENT_AVAILABLE:
            mgmt_card = CardWidget()
        else:
            mgmt_card = QGroupBox("Gestion")
        
        mgmt_layout = QVBoxLayout(mgmt_card)
        mgmt_layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre de section
        if FLUENT_AVAILABLE:
            section_title = BodyLabel("Gestion des Données")
            section_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        else:
            section_title = QLabel("Gestion des Données")
            section_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        mgmt_layout.addWidget(section_title)
        
        # Boutons de gestion
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.categories_btn = PushButton("Gérer les Catégories")
            self.categories_btn.setIcon(FluentIcon.TAG)
            
            self.users_btn = PushButton("Gérer les Utilisateurs")
            self.users_btn.setIcon(FluentIcon.PEOPLE)
        else:
            self.categories_btn = QPushButton("Gérer les Catégories")
            self.users_btn = QPushButton("Gérer les Utilisateurs")
        
        self.categories_btn.clicked.connect(self._on_manage_categories)
        self.users_btn.clicked.connect(self._on_manage_users)
        
        buttons_layout.addWidget(self.categories_btn)
        buttons_layout.addWidget(self.users_btn)
        buttons_layout.addStretch()
        
        mgmt_layout.addLayout(buttons_layout)
        layout.addWidget(mgmt_card)

    def _create_user_section(self, layout):
        """Créer la section utilisateur"""
        if FLUENT_AVAILABLE:
            user_card = CardWidget()
        else:
            user_card = QGroupBox("Utilisateur")
        
        user_layout = QVBoxLayout(user_card)
        user_layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre de section
        if FLUENT_AVAILABLE:
            section_title = BodyLabel("Session Utilisateur")
            section_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        else:
            section_title = QLabel("Session Utilisateur")
            section_title.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        user_layout.addWidget(section_title)
        
        # Informations utilisateur
        self.user_info_label = QLabel("Aucun utilisateur connecté")
        user_layout.addWidget(self.user_info_label)
        
        # Bouton de déconnexion
        logout_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.logout_btn = PushButton("Se Déconnecter")
            self.logout_btn.setIcon(FluentIcon.POWER_BUTTON)
        else:
            self.logout_btn = QPushButton("Se Déconnecter")
        
        self.logout_btn.clicked.connect(self._on_logout)
        
        logout_layout.addWidget(self.logout_btn)
        logout_layout.addStretch()
        
        user_layout.addLayout(logout_layout)
        layout.addWidget(user_card)

    def _on_theme_changed(self, theme_display_name):
        """Gérer le changement de thème"""
        try:
            # Récupérer la valeur du thème
            theme_value = self.theme_combo.currentData()
            
            # Appliquer le thème si Fluent est disponible
            if FLUENT_AVAILABLE:
                if theme_value == "dark":
                    setTheme(Theme.DARK)
                elif theme_value == "light":
                    setTheme(Theme.LIGHT)
                else:  # system
                    setTheme(Theme.AUTO)
            
            # Émettre le signal
            self.theme_changed.emit(theme_value)
            
            self.logger.info(f"Thème changé vers: {theme_value}")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du changement de thème: {e}")
            self.error_occurred.emit(f"Erreur de thème: {e}")

    def _on_manage_categories(self):
        """Gérer les catégories selon spécifications"""
        try:
            from views.dialogs.categories_management_dialog import CategoriesManagementDialog

            dialog = CategoriesManagementDialog(self, self.app_instance)
            dialog.exec_()

            self.success_message.emit("Gestion des catégories terminée")

        except Exception as e:
            self.logger.error(f"Erreur gestion catégories: {e}")
            self.error_occurred.emit(f"Erreur gestion catégories: {e}")

    def _on_manage_users(self):
        """Gérer les utilisateurs (si admin) selon spécifications"""
        try:
            if not self.current_user or not self.current_user.get('is_admin'):
                self.error_occurred.emit("Accès réservé aux administrateurs")
                return
            
            # TODO: Implémenter le dialogue de gestion des utilisateurs
            self.info_message.emit("Gestion des utilisateurs à implémenter")
            
        except Exception as e:
            self.logger.error(f"Erreur gestion utilisateurs: {e}")
            self.error_occurred.emit(f"Erreur gestion utilisateurs: {e}")

    def _on_logout(self):
        """Gérer la déconnexion"""
        self.logout_requested.emit()

    def set_current_user(self, user_data):
        """Définir l'utilisateur actuel"""
        self.current_user = user_data
        
        if user_data:
            username = user_data.get('username', 'Inconnu')
            role = "Administrateur" if user_data.get('is_admin') else "Utilisateur"
            self.user_info_label.setText(f"Connecté en tant que: {username} ({role})")
            
            # Afficher/masquer le bouton de gestion des utilisateurs
            self.users_btn.setVisible(user_data.get('is_admin', False))
        else:
            self.user_info_label.setText("Aucun utilisateur connecté")
            self.users_btn.setVisible(False)

    def _create_theme_preview_card(self, theme_key: str, theme_name: str):
        """Créer une carte de prévisualisation de thème"""
        preview_card = QFrame()
        preview_card.setFixedSize(120, 100)
        preview_card.setCursor(Qt.PointingHandCursor)
        preview_card.setFrameStyle(QFrame.Box)

        # Layout de la carte
        card_layout = QVBoxLayout(preview_card)
        card_layout.setContentsMargins(8, 8, 8, 8)
        card_layout.setSpacing(5)

        # Nom du thème
        name_label = QLabel(theme_name)
        name_label.setFont(QFont("Segoe UI", 9, QFont.Bold))
        name_label.setAlignment(Qt.AlignCenter)

        # Prévisualisation des couleurs
        colors_frame = QFrame()
        colors_frame.setFixedHeight(40)
        colors_layout = QHBoxLayout(colors_frame)
        colors_layout.setContentsMargins(0, 0, 0, 0)
        colors_layout.setSpacing(2)

        # Obtenir les couleurs du thème
        theme_info = theme_manager.get_theme_info(theme_key)
        colors = theme_info.get('colors', {})

        # Créer des échantillons de couleurs
        color_samples = [
            colors.get('primary', '#0078d4'),
            colors.get('background', '#ffffff'),
            colors.get('surface', '#f9f9f9'),
            colors.get('text_primary', '#323130')
        ]

        for color in color_samples:
            color_sample = QFrame()
            color_sample.setFixedSize(20, 30)
            color_sample.setStyleSheet(f"background-color: {color}; border: 1px solid #ccc;")
            colors_layout.addWidget(color_sample)

        card_layout.addWidget(name_label)
        card_layout.addWidget(colors_frame)

        # Style de la carte
        if theme_key == theme_manager.current_theme:
            # Thème actuel - bordure bleue
            preview_card.setStyleSheet("""
                QFrame {
                    border: 3px solid #0078d4;
                    border-radius: 8px;
                    background-color: white;
                }
            """)
        else:
            # Thème non sélectionné
            preview_card.setStyleSheet("""
                QFrame {
                    border: 1px solid #e1dfdd;
                    border-radius: 8px;
                    background-color: white;
                }
                QFrame:hover {
                    border-color: #0078d4;
                    background-color: #f9f9f9;
                }
            """)

        # Connecter le clic
        preview_card.mousePressEvent = lambda event: self._on_theme_preview_clicked(theme_key)

        return preview_card

    def _on_theme_preview_clicked(self, theme_key: str):
        """Gérer le clic sur une prévisualisation de thème"""
        try:
            # Appliquer le thème avec animation
            theme_manager.apply_theme(theme_key, self, animate=True)

            # Mettre à jour l'apparence des cartes de prévisualisation
            self._update_theme_cards_appearance(theme_key)

            # Afficher une notification
            theme_name = theme_manager.get_available_themes().get(theme_key, theme_key)
            self.success_message.emit(f"Thème '{theme_name}' appliqué avec succès")

            self.logger.info(f"Thème changé vers: {theme_key}")

        except Exception as e:
            self.logger.error(f"Erreur lors du changement de thème: {e}")
            self.error_occurred.emit(f"Erreur lors du changement de thème: {e}")

    def _update_theme_cards_appearance(self, selected_theme: str):
        """Mettre à jour l'apparence des cartes de thème"""
        for theme_key, card in self.theme_cards.items():
            if theme_key == selected_theme:
                # Thème sélectionné
                card.setStyleSheet("""
                    QFrame {
                        border: 3px solid #0078d4;
                        border-radius: 8px;
                        background-color: white;
                    }
                """)
            else:
                # Thème non sélectionné
                card.setStyleSheet("""
                    QFrame {
                        border: 1px solid #e1dfdd;
                        border-radius: 8px;
                        background-color: white;
                    }
                    QFrame:hover {
                        border-color: #0078d4;
                        background-color: #f9f9f9;
                    }
                """)

    def _on_animations_changed(self, state):
        """Gérer le changement d'état des animations"""
        enabled = state == 2  # Qt.Checked
        # TODO: Implémenter la désactivation des animations
        self.logger.info(f"Animations {'activées' if enabled else 'désactivées'}")

    def _on_shadows_changed(self, state):
        """Gérer le changement d'état des effets d'ombre"""
        enabled = state == 2  # Qt.Checked
        # TODO: Implémenter la désactivation des ombres
        self.logger.info(f"Effets d'ombre {'activés' if enabled else 'désactivés'}")
