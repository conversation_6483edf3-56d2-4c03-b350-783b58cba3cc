#!/usr/bin/env python3
"""
Script pour créer des données de test dans GSlim
"""

import sys
import os

# Ajouter src au path
sys.path.insert(0, 'src')

def create_test_data():
    """Créer des données de test"""
    print("🧪 Création de données de test pour GSlim...")
    
    try:
        from database.manager import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        
        print("✅ Base de données initialisée")
        
        # Créer des catégories de test
        categories = [
            ("Électronique", "Appareils électroniques et accessoires"),
            ("Informatique", "Matériel informatique et logiciels"),
            ("Mobilier", "Meubles et équipements de bureau"),
            ("Papeterie", "Fournitures de bureau et papeterie"),
            ("Alimentaire", "Produits alimentaires et boissons")
        ]
        
        category_ids = {}
        for name, description in categories:
            category_id = db.create_category(name, description)
            if category_id:
                category_ids[name] = category_id
                print(f"✅ Catégorie créée: {name}")
        
        # Créer des produits de test
        products = [
            {
                'name': 'Ordinateur Portable Dell',
                'description': 'Ordinateur portable Dell Inspiron 15, 8GB RAM, 256GB SSD',
                'category_id': category_ids.get('Informatique'),
                'price': 699.99,
                'stock_quantity': 15,
                'minimum_stock': 5,
                'barcode': '1234567890123',
                'unit': 'pièce'
            },
            {
                'name': 'Souris Logitech',
                'description': 'Souris optique sans fil Logitech MX Master 3',
                'category_id': category_ids.get('Informatique'),
                'price': 89.99,
                'stock_quantity': 25,
                'minimum_stock': 10,
                'barcode': '1234567890124',
                'unit': 'pièce'
            },
            {
                'name': 'Chaise de Bureau',
                'description': 'Chaise ergonomique avec support lombaire',
                'category_id': category_ids.get('Mobilier'),
                'price': 199.99,
                'stock_quantity': 8,
                'minimum_stock': 3,
                'barcode': '1234567890125',
                'unit': 'pièce'
            },
            {
                'name': 'Smartphone Samsung',
                'description': 'Samsung Galaxy S23, 128GB, Noir',
                'category_id': category_ids.get('Électronique'),
                'price': 799.99,
                'stock_quantity': 12,
                'minimum_stock': 5,
                'barcode': '1234567890126',
                'unit': 'pièce'
            },
            {
                'name': 'Ramette Papier A4',
                'description': 'Ramette de papier blanc A4, 500 feuilles',
                'category_id': category_ids.get('Papeterie'),
                'price': 4.99,
                'stock_quantity': 50,
                'minimum_stock': 20,
                'barcode': '1234567890127',
                'unit': 'ramette'
            },
            {
                'name': 'Café en Grains',
                'description': 'Café arabica premium, 1kg',
                'category_id': category_ids.get('Alimentaire'),
                'price': 24.99,
                'stock_quantity': 30,
                'minimum_stock': 10,
                'barcode': '1234567890128',
                'unit': 'kg'
            },
            {
                'name': 'Écran 24 pouces',
                'description': 'Moniteur LED 24" Full HD, HDMI',
                'category_id': category_ids.get('Informatique'),
                'price': 149.99,
                'stock_quantity': 6,
                'minimum_stock': 3,
                'barcode': '1234567890129',
                'unit': 'pièce'
            },
            {
                'name': 'Stylos Bic',
                'description': 'Lot de 10 stylos bille bleus',
                'category_id': category_ids.get('Papeterie'),
                'price': 2.99,
                'stock_quantity': 100,
                'minimum_stock': 50,
                'barcode': '1234567890130',
                'unit': 'lot'
            },
            {
                'name': 'Tablette iPad',
                'description': 'Apple iPad Air, 64GB, WiFi',
                'category_id': category_ids.get('Électronique'),
                'price': 599.99,
                'stock_quantity': 4,
                'minimum_stock': 2,
                'barcode': '1234567890131',
                'unit': 'pièce'
            },
            {
                'name': 'Bureau en Bois',
                'description': 'Bureau en chêne massif, 120x60cm',
                'category_id': category_ids.get('Mobilier'),
                'price': 299.99,
                'stock_quantity': 0,  # En rupture pour tester
                'minimum_stock': 2,
                'barcode': '1234567890132',
                'unit': 'pièce'
            }
        ]
        
        for product_data in products:
            product_id = db.create_product(product_data)
            if product_id:
                print(f"✅ Produit créé: {product_data['name']}")
            else:
                print(f"❌ Erreur création produit: {product_data['name']}")
        
        print(f"\n🎉 Données de test créées avec succès !")
        print(f"📊 {len(categories)} catégories créées")
        print(f"📦 {len(products)} produits créés")
        print("\n🚀 Vous pouvez maintenant tester l'application avec des données réelles !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données de test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🧪 Script de Création de Données de Test")
    print("=" * 50)
    
    success = create_test_data()
    
    if success:
        print("\n✅ SUCCÈS ! Données de test créées")
        print("\n📋 Prochaines étapes:")
        print("   1. Lancez l'application: python main.py")
        print("   2. Connectez-vous: admin / admin123")
        print("   3. Explorez le module Produits")
        print("   4. Consultez le Dashboard avec les vraies statistiques")
        return True
    else:
        print("\n❌ ÉCHEC de la création des données")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
