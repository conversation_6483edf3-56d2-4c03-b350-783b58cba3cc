# 🔐 GUIDE DE CONNEXION - GSLIM

## 🎯 **IDENTIFIANTS DE CONNEXION DISPONIBLES**

### **👑 Administrateur**
- **Nom d'utilisateur:** `admin`
- **Mot de passe:** `admin123`
- **Rôle:** Administrateur complet
- **Accès:** Toutes les fonctionnalités

### **👤 Utilisateurs de Test**
- **Nom d'utilisateur:** `test` | **Mot de passe:** `test`
- **Nom d'utilisateur:** `demo` | **Mot de passe:** `demo`  
- **Nom d'utilisateur:** `user` | **Mot de passe:** `123`
- **Rôle:** Utilisateur standard
- **Accès:** Fonctionnalités de base

## 🚀 **COMMENT SE CONNECTER**

### **1. Lancer l'Application**
```bash
# Activer l'environnement virtuel
venv\Scripts\activate.bat

# Lancer l'application
python main.py
```

### **2. É<PERSON>ran de Connexion**
1. **Entrez vos identifiants** dans les champs
2. **Cliquez sur "Se connecter"**
3. **Accédez à l'interface principale**

### **3. Identifiants Recommandés**
- **Pour l'administration:** `admin` / `admin123`
- **Pour les tests:** `test` / `test`
- **Pour les démonstrations:** `demo` / `demo`

## 🎨 **FONCTIONNALITÉS DISPONIBLES**

### **✅ Interface Moderne**
- 🏠 **Dashboard** avec statistiques temps réel
- 📦 **Module Articles** avec recherche avancée
- 🏢 **Module Fournisseurs** avec gestion complète
- 🎨 **5 Thèmes** révolutionnaires

### **✅ Gestion Complète**
- 📊 **Statistiques** en temps réel
- 🔍 **Recherche et filtrage** avancés
- 📈 **Rapports** détaillés
- 🔔 **Alertes** de stock

## 🔧 **DÉPANNAGE**

### **Problèmes de Connexion**
- ✅ Vérifiez que vous utilisez les bons identifiants
- ✅ Respectez la casse (minuscules)
- ✅ Assurez-vous que l'environnement virtuel est activé

### **Identifiants Oubliés**
- 👑 **Admin:** `admin` / `admin123`
- 👤 **Test:** `test` / `test`
- 🎮 **Demo:** `demo` / `demo`

## 🎉 **PROFITEZ DE VOTRE INTERFACE !**

Une fois connecté, explorez toutes les fonctionnalités de votre interface GSlim révolutionnaire !

---

*Guide de connexion GSlim - Interface Révolutionnaire 2.0*
