"""
Composants Cyberpunk Avancés pour GSlim
Widgets futuristes avec effets néon, hologrammes et animations sci-fi
"""

import random
import math
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame,
    QGraphicsDropShadowEffect, QGraphicsOpacityEffect, QProgressBar
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QTimer,
    QParallelAnimationGroup, QSequentialAnimationGroup, QVariantAnimation,
    QPoint, QSize
)
from PyQt5.QtGui import (
    QFont, QPainter, QColor, QLinearGradient, QRadialGradient, QPen,
    QBrush, QPainterPath, QPixmap, QPolygonF, QConicalGradient
)

from styles.futuristic_theme import FuturisticTheme


class HologramWidget(QWidget):
    """Widget holographique avec effet de scintillement"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.opacity_value = 1.0
        self.scan_line_position = 0
        
        self.setup_hologram_effect()
    
    def setup_hologram_effect(self):
        """Configurer l'effet holographique"""
        # Animation de scintillement
        self.flicker_animation = QVariantAnimation()
        self.flicker_animation.setDuration(2000)
        self.flicker_animation.setStartValue(0.7)
        self.flicker_animation.setEndValue(1.0)
        self.flicker_animation.setLoopCount(-1)
        self.flicker_animation.valueChanged.connect(self.update_opacity)
        self.flicker_animation.start()
        
        # Animation de ligne de scan
        self.scan_animation = QVariantAnimation()
        self.scan_animation.setDuration(3000)
        self.scan_animation.setStartValue(0)
        self.scan_animation.setEndValue(self.height())
        self.scan_animation.setLoopCount(-1)
        self.scan_animation.valueChanged.connect(self.update_scan_line)
        self.scan_animation.start()
    
    def update_opacity(self, value):
        """Mettre à jour l'opacité pour l'effet de scintillement"""
        self.opacity_value = value
        self.update()
    
    def update_scan_line(self, value):
        """Mettre à jour la position de la ligne de scan"""
        self.scan_line_position = value
        self.update()
    
    def paintEvent(self, event):
        """Dessiner l'effet holographique"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Effet de transparence
        painter.setOpacity(self.opacity_value)
        
        # Ligne de scan holographique
        if self.scan_line_position < self.height():
            scan_gradient = QLinearGradient(0, self.scan_line_position - 10, 0, self.scan_line_position + 10)
            scan_gradient.setColorAt(0, QColor(0, 255, 255, 0))
            scan_gradient.setColorAt(0.5, QColor(0, 255, 255, 100))
            scan_gradient.setColorAt(1, QColor(0, 255, 255, 0))
            
            painter.fillRect(0, self.scan_line_position - 10, self.width(), 20, QBrush(scan_gradient))
        
        super().paintEvent(event)


class NeonProgressBar(QProgressBar):
    """Barre de progression avec effet néon"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.neon_color = FuturisticTheme.NEON_CYAN
        self.pulse_intensity = 1.0
        
        self.setup_neon_style()
        self.setup_pulse_animation()
    
    def setup_neon_style(self):
        """Configurer le style néon"""
        self.setFixedHeight(12)
        self.setTextVisible(False)
        
        self.setStyleSheet(f"""
            QProgressBar {{
                background-color: rgba(15, 15, 26, 0.8);
                border: 1px solid rgba(0, 255, 255, 0.3);
                border-radius: 6px;
            }}
            QProgressBar::chunk {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {self.neon_color},
                    stop:0.5 rgba(255, 255, 255, 0.8),
                    stop:1 {self.neon_color});
                border-radius: 5px;
                box-shadow: 0 0 10px {self.neon_color};
            }}
        """)
    
    def setup_pulse_animation(self):
        """Configurer l'animation de pulsation"""
        self.pulse_animation = QVariantAnimation()
        self.pulse_animation.setDuration(1500)
        self.pulse_animation.setStartValue(0.6)
        self.pulse_animation.setEndValue(1.0)
        self.pulse_animation.setLoopCount(-1)
        self.pulse_animation.valueChanged.connect(self.update_pulse)
        self.pulse_animation.start()
    
    def update_pulse(self, value):
        """Mettre à jour la pulsation"""
        self.pulse_intensity = value
        # Mettre à jour la couleur avec l'intensité
        alpha = int(255 * value)
        color = QColor(self.neon_color)
        color.setAlpha(alpha)
    
    def set_neon_color(self, color: str):
        """Changer la couleur néon"""
        self.neon_color = color
        self.setup_neon_style()


class QuantumButton(QPushButton):
    """Bouton quantique avec effets de particules"""
    
    def __init__(self, text: str, parent=None):
        super().__init__(text, parent)
        self.particles = []
        self.is_quantum_active = False
        
        self.setup_quantum_style()
        self.setup_particle_system()
    
    def setup_quantum_style(self):
        """Configurer le style quantique"""
        self.setMinimumHeight(50)
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(0, 255, 255, 0.1),
                    stop:0.5 rgba(128, 0, 255, 0.15),
                    stop:1 rgba(255, 0, 255, 0.1));
                border: 2px solid transparent;
                border-image: linear-gradient(45deg, 
                    {FuturisticTheme.NEON_CYAN}, 
                    {FuturisticTheme.NEON_PURPLE}, 
                    {FuturisticTheme.NEON_PINK}) 1;
                border-radius: 15px;
                color: {FuturisticTheme.HOLOGRAM_WHITE};
                font-family: 'Orbitron', monospace;
                font-weight: 600;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 0, 255, 0.2),
                    stop:0.5 rgba(0, 255, 255, 0.25),
                    stop:1 rgba(0, 255, 65, 0.2));
                box-shadow: 0 0 30px rgba(255, 0, 255, 0.5);
            }}
        """)
    
    def setup_particle_system(self):
        """Configurer le système de particules"""
        self.particle_timer = QTimer()
        self.particle_timer.timeout.connect(self.update_particles)
        self.particle_timer.start(50)  # 20 FPS
        
        # Créer des particules initiales
        for _ in range(10):
            self.create_particle()
    
    def create_particle(self):
        """Créer une nouvelle particule"""
        particle = {
            'x': random.uniform(0, self.width()),
            'y': random.uniform(0, self.height()),
            'vx': random.uniform(-2, 2),
            'vy': random.uniform(-2, 2),
            'life': random.uniform(0.5, 1.0),
            'color': random.choice([
                FuturisticTheme.NEON_CYAN,
                FuturisticTheme.NEON_PINK,
                FuturisticTheme.NEON_PURPLE
            ])
        }
        self.particles.append(particle)
    
    def update_particles(self):
        """Mettre à jour les particules"""
        if not self.is_quantum_active:
            return
        
        # Mettre à jour les particules existantes
        for particle in self.particles[:]:
            particle['x'] += particle['vx']
            particle['y'] += particle['vy']
            particle['life'] -= 0.02
            
            # Supprimer les particules mortes
            if particle['life'] <= 0:
                self.particles.remove(particle)
        
        # Créer de nouvelles particules
        if len(self.particles) < 15:
            self.create_particle()
        
        self.update()
    
    def activate_quantum_mode(self):
        """Activer le mode quantique"""
        self.is_quantum_active = True
        
        # Animation de rotation
        self.rotation_animation = QVariantAnimation()
        self.rotation_animation.setDuration(2000)
        self.rotation_animation.setStartValue(0)
        self.rotation_animation.setEndValue(360)
        self.rotation_animation.setLoopCount(-1)
        self.rotation_animation.start()
    
    def paintEvent(self, event):
        """Dessiner le bouton avec particules"""
        super().paintEvent(event)
        
        if self.is_quantum_active and self.particles:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.Antialiasing)
            
            for particle in self.particles:
                color = QColor(particle['color'])
                color.setAlphaF(particle['life'])
                painter.setPen(QPen(color, 2))
                painter.drawPoint(int(particle['x']), int(particle['y']))
    
    def mousePressEvent(self, event):
        """Activer l'effet quantique au clic"""
        if event.button() == Qt.LeftButton:
            self.activate_quantum_mode()
        super().mousePressEvent(event)


class CyberDataStream(QWidget):
    """Flux de données cyberpunk en temps réel"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_lines = []
        self.matrix_chars = "01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン"
        
        self.setup_data_stream()
    
    def setup_data_stream(self):
        """Configurer le flux de données"""
        self.setMinimumHeight(200)
        self.setStyleSheet(f"""
            background-color: rgba(10, 10, 15, 0.9);
            border: 1px solid {FuturisticTheme.NEON_GREEN};
            border-radius: 10px;
        """)
        
        # Timer pour générer de nouvelles lignes
        self.stream_timer = QTimer()
        self.stream_timer.timeout.connect(self.generate_data_line)
        self.stream_timer.start(500)  # Nouvelle ligne toutes les 500ms
        
        # Timer pour l'animation
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.animate_stream)
        self.animation_timer.start(100)  # Animation à 10 FPS
    
    def generate_data_line(self):
        """Générer une nouvelle ligne de données"""
        # Types de données cyberpunk
        data_types = [
            "QUANTUM_FLUX",
            "NEURAL_PATTERN",
            "CRYPTO_HASH",
            "MATRIX_CODE",
            "ENERGY_LEVEL",
            "SECURITY_KEY",
            "DATA_PACKET",
            "SYSTEM_LOG"
        ]
        
        data_type = random.choice(data_types)
        
        # Générer des données aléatoires
        if data_type == "MATRIX_CODE":
            data = ''.join(random.choices(self.matrix_chars, k=20))
        elif data_type == "CRYPTO_HASH":
            data = ''.join(random.choices('0123456789ABCDEF', k=16))
        else:
            data = f"{random.randint(1000, 9999)}.{random.randint(100, 999)}"
        
        line = {
            'type': data_type,
            'data': data,
            'y': 0,
            'alpha': 1.0,
            'color': random.choice([
                FuturisticTheme.NEON_GREEN,
                FuturisticTheme.NEON_CYAN,
                FuturisticTheme.MATRIX_GREEN
            ])
        }
        
        self.data_lines.append(line)
        
        # Limiter le nombre de lignes
        if len(self.data_lines) > 10:
            self.data_lines.pop(0)
    
    def animate_stream(self):
        """Animer le flux de données"""
        for line in self.data_lines[:]:
            line['y'] += 2
            line['alpha'] -= 0.02
            
            # Supprimer les lignes qui sortent de l'écran
            if line['y'] > self.height() or line['alpha'] <= 0:
                self.data_lines.remove(line)
        
        self.update()
    
    def paintEvent(self, event):
        """Dessiner le flux de données"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        font = QFont("Consolas", 10)
        painter.setFont(font)
        
        for line in self.data_lines:
            color = QColor(line['color'])
            color.setAlphaF(line['alpha'])
            painter.setPen(color)
            
            text = f"[{line['type']}] {line['data']}"
            painter.drawText(10, int(line['y']), text)


class HexagonalButton(QPushButton):
    """Bouton hexagonal futuriste"""
    
    def __init__(self, text: str, parent=None):
        super().__init__(text, parent)
        self.hex_color = FuturisticTheme.NEON_CYAN
        self.is_glowing = False
        
        self.setup_hexagon_style()
        self.setup_glow_animation()
    
    def setup_hexagon_style(self):
        """Configurer le style hexagonal"""
        self.setFixedSize(100, 100)
        self.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: white;
                font-weight: bold;
                font-size: 12px;
            }
        """)
    
    def setup_glow_animation(self):
        """Configurer l'animation de lueur"""
        self.glow_animation = QVariantAnimation()
        self.glow_animation.setDuration(2000)
        self.glow_animation.setStartValue(0.3)
        self.glow_animation.setEndValue(1.0)
        self.glow_animation.setLoopCount(-1)
        self.glow_animation.valueChanged.connect(self.update_glow)
        self.glow_animation.start()
        
        self.glow_intensity = 0.5
    
    def update_glow(self, value):
        """Mettre à jour l'intensité de la lueur"""
        self.glow_intensity = value
        self.update()
    
    def paintEvent(self, event):
        """Dessiner l'hexagone"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Créer l'hexagone
        center = self.rect().center()
        radius = min(self.width(), self.height()) // 2 - 10
        
        hexagon = QPolygonF()
        for i in range(6):
            angle = i * 60 * math.pi / 180
            x = center.x() + radius * math.cos(angle)
            y = center.y() + radius * math.sin(angle)
            hexagon.append(QPoint(int(x), int(y)))
        
        # Couleur avec intensité de lueur
        color = QColor(self.hex_color)
        color.setAlphaF(self.glow_intensity)
        
        # Dessiner l'hexagone
        painter.setPen(QPen(color, 3))
        painter.setBrush(QBrush(QColor(color.red(), color.green(), color.blue(), 30)))
        painter.drawPolygon(hexagon)
        
        # Dessiner le texte
        painter.setPen(QColor(FuturisticTheme.HOLOGRAM_WHITE))
        painter.drawText(self.rect(), Qt.AlignCenter, self.text())
    
    def enterEvent(self, event):
        """Effet de survol"""
        self.hex_color = FuturisticTheme.NEON_PINK
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Fin de survol"""
        self.hex_color = FuturisticTheme.NEON_CYAN
        super().leaveEvent(event)


class QuantumLoader(QWidget):
    """Loader quantique avec effet de rotation"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.rotation_angle = 0
        self.quantum_rings = 3
        
        self.setup_quantum_loader()
    
    def setup_quantum_loader(self):
        """Configurer le loader quantique"""
        self.setFixedSize(80, 80)
        
        # Animation de rotation
        self.rotation_animation = QVariantAnimation()
        self.rotation_animation.setDuration(2000)
        self.rotation_animation.setStartValue(0)
        self.rotation_animation.setEndValue(360)
        self.rotation_animation.setLoopCount(-1)
        self.rotation_animation.valueChanged.connect(self.update_rotation)
        self.rotation_animation.start()
    
    def update_rotation(self, value):
        """Mettre à jour la rotation"""
        self.rotation_angle = value
        self.update()
    
    def paintEvent(self, event):
        """Dessiner le loader quantique"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        center = self.rect().center()
        painter.translate(center)
        painter.rotate(self.rotation_angle)
        
        # Dessiner les anneaux quantiques
        colors = [
            FuturisticTheme.NEON_CYAN,
            FuturisticTheme.NEON_PINK,
            FuturisticTheme.NEON_PURPLE
        ]
        
        for i in range(self.quantum_rings):
            radius = 15 + i * 10
            color = QColor(colors[i])
            color.setAlpha(150)
            
            painter.setPen(QPen(color, 3))
            painter.drawEllipse(-radius, -radius, radius * 2, radius * 2)
            
            # Particules sur l'anneau
            for j in range(6):
                angle = j * 60 + i * 20
                x = radius * math.cos(math.radians(angle))
                y = radius * math.sin(math.radians(angle))
                
                painter.setBrush(QBrush(color))
                painter.drawEllipse(int(x-2), int(y-2), 4, 4)
