"""
Widget de gestion des produits conforme au cahier des charges
Interface complète avec tableau, recherche, filtrage et dialogues
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLineEdit, QComboBox, QPushButton, QHeaderView, QAbstractItemView,
    QMessageBox, QLabel, QFrame
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        TableWidget, LineEdit, ComboBox, PushButton, SearchLineEdit,
        TitleLabel, CardWidget, FluentIcon, InfoBar, InfoBarPosition
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from config.settings import config
from utils.logger import setup_logger
from .dialogs.product_dialog import ProductDialog


class ProductsWidget(QWidget):
    """
    Widget de gestion des produits selon les spécifications
    Fonctionnalités: CRUD, recherche, filtrage par catégorie
    """
    
    # Signaux
    error_occurred = pyqtSignal(str)
    success_message = pyqtSignal(str)
    info_message = pyqtSignal(str)

    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.current_user = None
        
        # Variables d'instance
        self.products_data = []
        self.categories_data = []
        self.filtered_products = []
        
        # Initialiser l'interface
        self._init_ui()
        
        # Charger les données initiales
        self.refresh_data()
        
        self.logger.info("Widget produits initialisé")

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # En-tête
        self._create_header(main_layout)
        
        # Barre d'outils avec recherche et filtres
        self._create_toolbar(main_layout)
        
        # Tableau des produits
        self._create_products_table(main_layout)
        
        # Barre d'actions
        self._create_action_bar(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du module"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Produits")
        else:
            title = QLabel("Gestion des Produits")
            title.setFont(QFont("Segoe UI", 20, QFont.Bold))
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Bouton d'actualisation
        if FLUENT_AVAILABLE:
            refresh_btn = PushButton("Actualiser")
            refresh_btn.setIcon(FluentIcon.SYNC)
        else:
            refresh_btn = QPushButton("Actualiser")
        
        refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)

    def _create_toolbar(self, layout):
        """Créer la barre d'outils avec recherche et filtres"""
        if FLUENT_AVAILABLE:
            toolbar_card = CardWidget()
        else:
            toolbar_card = QFrame()
            toolbar_card.setFrameStyle(QFrame.Box)
        
        toolbar_layout = QHBoxLayout(toolbar_card)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        
        # Recherche selon spécifications
        search_label = QLabel("Recherche:")
        if FLUENT_AVAILABLE:
            self.search_input = SearchLineEdit()
            self.search_input.setPlaceholderText("Rechercher par nom ou description...")
        else:
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("Rechercher par nom ou description...")
        
        self.search_input.textChanged.connect(self._on_search_changed)
        
        # Filtre par catégorie selon spécifications
        filter_label = QLabel("Catégorie:")
        if FLUENT_AVAILABLE:
            self.category_filter = ComboBox()
        else:
            self.category_filter = QComboBox()
        
        self.category_filter.currentTextChanged.connect(self._on_filter_changed)
        
        # Assemblage
        toolbar_layout.addWidget(search_label)
        toolbar_layout.addWidget(self.search_input, 1)
        toolbar_layout.addWidget(filter_label)
        toolbar_layout.addWidget(self.category_filter)
        
        layout.addWidget(toolbar_card)

    def _create_products_table(self, layout):
        """Créer le tableau des produits selon les spécifications"""
        if FLUENT_AVAILABLE:
            self.products_table = TableWidget()
        else:
            self.products_table = QTableWidget()
        
        # Configuration du tableau selon spécifications
        columns = [
            "ID", "Nom", "Description", "Prix de Vente (€)", 
            "Quantité en Stock", "Nom Catégorie", "Fournisseur"
        ]
        
        self.products_table.setColumnCount(len(columns))
        self.products_table.setHorizontalHeaderLabels(columns)
        
        # Configuration de l'affichage
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.products_table.setAlternatingRowColors(True)
        
        # Ajustement des colonnes
        header = self.products_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description
        
        # Double-clic pour modifier
        self.products_table.doubleClicked.connect(self._on_product_double_click)
        
        layout.addWidget(self.products_table, 1)

    def _create_action_bar(self, layout):
        """Créer la barre d'actions selon les spécifications"""
        actions_layout = QHBoxLayout()
        
        # Boutons d'action selon spécifications
        if FLUENT_AVAILABLE:
            self.add_btn = PushButton("Ajouter")
            self.add_btn.setIcon(FluentIcon.ADD)
            
            self.edit_btn = PushButton("Modifier")
            self.edit_btn.setIcon(FluentIcon.EDIT)
            
            self.delete_btn = PushButton("Supprimer")
            self.delete_btn.setIcon(FluentIcon.DELETE)
        else:
            self.add_btn = QPushButton("Ajouter")
            self.edit_btn = QPushButton("Modifier")
            self.delete_btn = QPushButton("Supprimer")
        
        # État initial des boutons
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        
        # Connecter les signaux
        self.add_btn.clicked.connect(self._on_add_product)
        self.edit_btn.clicked.connect(self._on_edit_product)
        self.delete_btn.clicked.connect(self._on_delete_product)
        
        # Gestion de la sélection
        self.products_table.itemSelectionChanged.connect(self._on_selection_changed)
        
        # Assemblage
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)

    def _on_search_changed(self, text):
        """Gérer le changement de recherche"""
        self._apply_filters()

    def _on_filter_changed(self, category):
        """Gérer le changement de filtre de catégorie"""
        self._apply_filters()

    def _apply_filters(self):
        """Appliquer les filtres de recherche et catégorie"""
        search_text = self.search_input.text().lower()
        selected_category = self.category_filter.currentText()
        
        self.filtered_products = []
        
        for product in self.products_data:
            # Filtre de recherche
            if search_text:
                searchable_text = f"{product.get('name', '')} {product.get('description', '')}".lower()
                if search_text not in searchable_text:
                    continue
            
            # Filtre de catégorie
            if selected_category and selected_category != "Toutes":
                if product.get('category_name', '') != selected_category:
                    continue
            
            self.filtered_products.append(product)
        
        self._update_table_display()

    def _update_table_display(self):
        """Mettre à jour l'affichage du tableau"""
        self.products_table.setRowCount(len(self.filtered_products))
        
        for row, product in enumerate(self.filtered_products):
            # Colonnes selon spécifications
            self.products_table.setItem(row, 0, QTableWidgetItem(str(product.get('id', ''))))
            self.products_table.setItem(row, 1, QTableWidgetItem(product.get('name', '')))
            self.products_table.setItem(row, 2, QTableWidgetItem(product.get('description', '')))
            self.products_table.setItem(row, 3, QTableWidgetItem(f"{product.get('price', 0):.2f}"))
            self.products_table.setItem(row, 4, QTableWidgetItem(str(product.get('stock_quantity', 0))))
            self.products_table.setItem(row, 5, QTableWidgetItem(product.get('category_name', '')))
            self.products_table.setItem(row, 6, QTableWidgetItem(product.get('supplier_name', '')))

    def _on_selection_changed(self):
        """Gérer le changement de sélection"""
        has_selection = len(self.products_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _on_product_double_click(self):
        """Gérer le double-clic sur un produit"""
        self._on_edit_product()

    def _on_add_product(self):
        """Ajouter un nouveau produit"""
        try:
            dialog = ProductDialog(self, self.categories_data)
            if dialog.exec_() == dialog.Accepted:
                product_data = dialog.get_product_data()

                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                product_id = db_manager.create_product(product_data)

                if product_id:
                    self.refresh_data()
                    self.success_message.emit("Produit ajouté avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la sauvegarde du produit")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout: {e}")
            self.error_occurred.emit(f"Erreur lors de l'ajout: {e}")

    def _on_edit_product(self):
        """Modifier le produit sélectionné"""
        try:
            selected_row = self.products_table.currentRow()
            if selected_row < 0:
                return

            product = self.filtered_products[selected_row]

            dialog = ProductDialog(self, self.categories_data, product)
            if dialog.exec_() == dialog.Accepted:
                product_data = dialog.get_product_data()

                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.update_product(product['id'], product_data)

                if success:
                    self.refresh_data()
                    self.success_message.emit("Produit modifié avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la mise à jour du produit")

        except Exception as e:
            self.logger.error(f"Erreur lors de la modification: {e}")
            self.error_occurred.emit(f"Erreur lors de la modification: {e}")

    def _on_delete_product(self):
        """Supprimer le produit sélectionné"""
        try:
            selected_row = self.products_table.currentRow()
            if selected_row < 0:
                return

            product = self.filtered_products[selected_row]

            # Dialogue de confirmation selon spécifications
            reply = QMessageBox.question(
                self,
                "Confirmer la suppression",
                f"Êtes-vous sûr de vouloir supprimer le produit '{product.get('name', '')}'?\n\n"
                f"Stock actuel: {product.get('stock_quantity', 0)}",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Supprimer de la base de données (avec vérification du stock)
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.delete_product(product['id'])

                if success:
                    self.refresh_data()
                    self.success_message.emit("Produit supprimé avec succès")
                else:
                    self.error_occurred.emit("Impossible de supprimer le produit (vérifiez le stock)")

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression: {e}")
            self.error_occurred.emit(f"Erreur lors de la suppression: {e}")

    def refresh_data(self):
        """Actualiser les données depuis la base de données"""
        try:
            db_manager = self.app_instance.get_database_manager()
            
            # Charger les produits
            self.products_data = self._load_products(db_manager)
            
            # Charger les catégories pour le filtre
            self.categories_data = self._load_categories(db_manager)
            self._update_category_filter()
            
            # Appliquer les filtres
            self._apply_filters()
            
            self.logger.info(f"{len(self.products_data)} produits chargés")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement: {e}")
            self.error_occurred.emit(f"Erreur de chargement: {e}")

    def _load_products(self, db_manager):
        """Charger les produits depuis la base de données"""
        try:
            # Récupérer les filtres actuels
            search_term = self.search_input.text() if hasattr(self, 'search_input') else ""

            # Récupérer l'ID de la catégorie sélectionnée
            category_id = None
            if hasattr(self, 'category_filter') and self.category_filter.currentData():
                category_id = self.category_filter.currentData()

            # Charger les produits avec filtres
            return db_manager.get_products(search_term, category_id)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des produits: {e}")
            return []

    def _load_categories(self, db_manager):
        """Charger les catégories depuis la base de données"""
        try:
            return db_manager.get_categories()
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des catégories: {e}")
            return []

    def _update_category_filter(self):
        """Mettre à jour le filtre de catégories"""
        self.category_filter.clear()
        self.category_filter.addItem("Toutes")
        
        for category in self.categories_data:
            self.category_filter.addItem(category.get('name', ''))

    def set_current_user(self, user_data):
        """Définir l'utilisateur actuel"""
        self.current_user = user_data
