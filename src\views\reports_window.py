"""
Fenêtre de génération et visualisation des rapports
Interface pour créer des rapports PDF/Excel avec graphiques
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QGridLayout, QGroupBox, QFormLayout, QComboBox,
    QDateEdit, QCheckBox, QTextEdit, QProgressBar, QListWidget,
    QListWidgetItem, QFileDialog, QMessageBox, QTabWidget,
    QScrollArea, QSizePolicy
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDate, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap

try:
    from qfluentwidgets import (
        PushButton, ComboBox, CardWidget, TitleLabel, CaptionLabel,
        FluentIcon, InfoBar, InfoBarPosition, DateEdit, CheckBox,
        ProgressBar
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from controllers.report_controller import ReportController
from controllers.article_controller import ArticleController
from controllers.supplier_controller import SupplierController
from controllers.stock_movement_controller import StockMovementController
from controllers.order_controller import OrderController
from utils.logger import setup_logger
from datetime import datetime, date, timedelta
import os


class ReportGenerationThread(QThread):
    """Thread pour la génération de rapports en arrière-plan"""
    
    progress_updated = pyqtSignal(int)
    report_generated = pyqtSignal(bool, str, str)  # success, filepath, message
    
    def __init__(self, report_controller, report_type, params):
        super().__init__()
        self.report_controller = report_controller
        self.report_type = report_type
        self.params = params
    
    def run(self):
        """Exécuter la génération du rapport"""
        try:
            self.progress_updated.emit(10)
            
            if self.report_type == "stock":
                result = self.report_controller.generate_stock_report(
                    self.params.get('format', 'pdf'),
                    self.params.get('include_charts', True)
                )
            elif self.report_type == "movements":
                result = self.report_controller.generate_movements_report(
                    self.params['start_date'],
                    self.params['end_date'],
                    self.params.get('format', 'pdf')
                )
            elif self.report_type == "suppliers":
                result = self.report_controller.generate_suppliers_report(
                    self.params.get('format', 'pdf')
                )
            else:
                result = None
            
            self.progress_updated.emit(100)
            
            if result and result.success:
                self.report_generated.emit(True, result.data, result.message)
            else:
                message = result.message if result else "Type de rapport non supporté"
                self.report_generated.emit(False, "", message)
                
        except Exception as e:
            self.progress_updated.emit(100)
            self.report_generated.emit(False, "", str(e))


class ReportsWindow(QWidget):
    """Fenêtre de génération et visualisation des rapports"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.logger = setup_logger(__name__)
        
        # Contrôleurs
        self.report_controller = ReportController(db_manager)
        self.article_controller = ArticleController(db_manager)
        self.supplier_controller = SupplierController(db_manager)
        self.movement_controller = StockMovementController(db_manager)
        self.order_controller = OrderController(db_manager)
        
        # Variables
        self.generated_reports = []
        self.generation_thread = None
        
        self._init_ui()
        self._load_generated_reports()
        
        self.logger.info("ReportsWindow initialisé")
    
    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # En-tête
        self._create_header(layout)
        
        # Onglets
        self.tab_widget = QTabWidget()
        
        # Onglet Génération de rapports
        generation_tab = QWidget()
        self._create_generation_tab(generation_tab)
        self.tab_widget.addTab(generation_tab, "Générer Rapports")
        
        # Onglet Rapports générés
        reports_tab = QWidget()
        self._create_reports_tab(reports_tab)
        self.tab_widget.addTab(reports_tab, "Rapports Générés")
        
        # Onglet Graphiques
        charts_tab = QWidget()
        self._create_charts_tab(charts_tab)
        self.tab_widget.addTab(charts_tab, "Graphiques")
        
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)
    
    def _create_header(self, layout):
        """Créer l'en-tête"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Génération de Rapports")
        else:
            title = QLabel("Génération de Rapports")
            title.setProperty("class", "title")
            font = QFont("Segoe UI", 24, QFont.Bold)
            title.setFont(font)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Bouton d'actualisation
        if FLUENT_AVAILABLE:
            refresh_btn = PushButton("Actualiser")
            refresh_btn.setIcon(FluentIcon.SYNC)
        else:
            refresh_btn = QPushButton("Actualiser")
        
        refresh_btn.clicked.connect(self._load_generated_reports)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)
    
    def _create_generation_tab(self, tab_widget):
        """Créer l'onglet de génération de rapports"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)
        
        # Zone de défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout()
        content_layout.setSpacing(20)
        
        # Rapport de stock
        self._create_stock_report_section(content_layout)
        
        # Rapport des mouvements
        self._create_movements_report_section(content_layout)
        
        # Rapport des fournisseurs
        self._create_suppliers_report_section(content_layout)
        
        # Barre de progression
        self._create_progress_section(content_layout)
        
        content_layout.addStretch()
        content_widget.setLayout(content_layout)
        scroll_area.setWidget(content_widget)
        
        layout.addWidget(scroll_area)
        tab_widget.setLayout(layout)
    
    def _create_stock_report_section(self, layout):
        """Créer la section du rapport de stock"""
        if FLUENT_AVAILABLE:
            stock_group = CardWidget()
        else:
            stock_group = QGroupBox("Rapport de Stock")
        
        stock_layout = QFormLayout()
        stock_layout.setSpacing(15)
        
        # Titre
        if FLUENT_AVAILABLE:
            stock_title = TitleLabel("Rapport de Stock")
            stock_layout.addRow(stock_title)
        
        # Format
        if FLUENT_AVAILABLE:
            self.stock_format_combo = ComboBox()
        else:
            self.stock_format_combo = QComboBox()
        
        self.stock_format_combo.addItem("PDF", "pdf")
        self.stock_format_combo.addItem("Excel", "excel")
        stock_layout.addRow("Format:", self.stock_format_combo)
        
        # Inclure les graphiques
        if FLUENT_AVAILABLE:
            self.stock_charts_check = CheckBox("Inclure les graphiques")
        else:
            self.stock_charts_check = QCheckBox("Inclure les graphiques")
        
        self.stock_charts_check.setChecked(True)
        stock_layout.addRow(self.stock_charts_check)
        
        # Bouton de génération
        if FLUENT_AVAILABLE:
            stock_generate_btn = PushButton("Générer Rapport de Stock")
            stock_generate_btn.setIcon(FluentIcon.DOCUMENT)
        else:
            stock_generate_btn = QPushButton("Générer Rapport de Stock")
        
        stock_generate_btn.clicked.connect(self._generate_stock_report)
        stock_layout.addRow(stock_generate_btn)
        
        stock_group.setLayout(stock_layout)
        layout.addWidget(stock_group)
    
    def _create_movements_report_section(self, layout):
        """Créer la section du rapport des mouvements"""
        if FLUENT_AVAILABLE:
            movements_group = CardWidget()
        else:
            movements_group = QGroupBox("Rapport des Mouvements")
        
        movements_layout = QFormLayout()
        movements_layout.setSpacing(15)
        
        # Titre
        if FLUENT_AVAILABLE:
            movements_title = TitleLabel("Rapport des Mouvements")
            movements_layout.addRow(movements_title)
        
        # Période
        period_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.movements_start_date = DateEdit()
            self.movements_end_date = DateEdit()
        else:
            self.movements_start_date = QDateEdit()
            self.movements_end_date = QDateEdit()
        
        self.movements_start_date.setDate(QDate.currentDate().addDays(-30))
        self.movements_end_date.setDate(QDate.currentDate())
        
        period_layout.addWidget(QLabel("Du:"))
        period_layout.addWidget(self.movements_start_date)
        period_layout.addWidget(QLabel("Au:"))
        period_layout.addWidget(self.movements_end_date)
        
        movements_layout.addRow("Période:", period_layout)
        
        # Format
        if FLUENT_AVAILABLE:
            self.movements_format_combo = ComboBox()
        else:
            self.movements_format_combo = QComboBox()
        
        self.movements_format_combo.addItem("PDF", "pdf")
        self.movements_format_combo.addItem("Excel", "excel")
        movements_layout.addRow("Format:", self.movements_format_combo)
        
        # Bouton de génération
        if FLUENT_AVAILABLE:
            movements_generate_btn = PushButton("Générer Rapport des Mouvements")
            movements_generate_btn.setIcon(FluentIcon.DOCUMENT)
        else:
            movements_generate_btn = QPushButton("Générer Rapport des Mouvements")
        
        movements_generate_btn.clicked.connect(self._generate_movements_report)
        movements_layout.addRow(movements_generate_btn)
        
        movements_group.setLayout(movements_layout)
        layout.addWidget(movements_group)
    
    def _create_suppliers_report_section(self, layout):
        """Créer la section du rapport des fournisseurs"""
        if FLUENT_AVAILABLE:
            suppliers_group = CardWidget()
        else:
            suppliers_group = QGroupBox("Rapport des Fournisseurs")
        
        suppliers_layout = QFormLayout()
        suppliers_layout.setSpacing(15)
        
        # Titre
        if FLUENT_AVAILABLE:
            suppliers_title = TitleLabel("Rapport des Fournisseurs")
            suppliers_layout.addRow(suppliers_title)
        
        # Format
        if FLUENT_AVAILABLE:
            self.suppliers_format_combo = ComboBox()
        else:
            self.suppliers_format_combo = QComboBox()
        
        self.suppliers_format_combo.addItem("PDF", "pdf")
        self.suppliers_format_combo.addItem("Excel", "excel")
        suppliers_layout.addRow("Format:", self.suppliers_format_combo)
        
        # Bouton de génération
        if FLUENT_AVAILABLE:
            suppliers_generate_btn = PushButton("Générer Rapport des Fournisseurs")
            suppliers_generate_btn.setIcon(FluentIcon.DOCUMENT)
        else:
            suppliers_generate_btn = QPushButton("Générer Rapport des Fournisseurs")
        
        suppliers_generate_btn.clicked.connect(self._generate_suppliers_report)
        suppliers_layout.addRow(suppliers_generate_btn)
        
        suppliers_group.setLayout(suppliers_layout)
        layout.addWidget(suppliers_group)
    
    def _create_progress_section(self, layout):
        """Créer la section de progression"""
        progress_frame = QFrame()
        progress_frame.setProperty("class", "progress-frame")
        
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(10)
        
        # Label de statut
        self.status_label = QLabel("Prêt à générer des rapports")
        self.status_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.status_label)
        
        # Barre de progression
        if FLUENT_AVAILABLE:
            self.progress_bar = ProgressBar()
        else:
            self.progress_bar = QProgressBar()
        
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        progress_frame.setLayout(progress_layout)
        layout.addWidget(progress_frame)
    
    def _create_reports_tab(self, tab_widget):
        """Créer l'onglet des rapports générés"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Liste des rapports
        self.reports_list = QListWidget()
        self.reports_list.itemDoubleClicked.connect(self._open_report)
        layout.addWidget(self.reports_list)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            open_btn = PushButton("Ouvrir")
            open_btn.setIcon(FluentIcon.VIEW)
            
            open_folder_btn = PushButton("Ouvrir dossier")
            open_folder_btn.setIcon(FluentIcon.FOLDER)
            
            delete_btn = PushButton("Supprimer")
            delete_btn.setIcon(FluentIcon.DELETE)
        else:
            open_btn = QPushButton("Ouvrir")
            open_folder_btn = QPushButton("Ouvrir dossier")
            delete_btn = QPushButton("Supprimer")
        
        open_btn.clicked.connect(self._open_selected_report)
        open_folder_btn.clicked.connect(self._open_reports_folder)
        delete_btn.clicked.connect(self._delete_selected_report)
        
        buttons_layout.addWidget(open_btn)
        buttons_layout.addWidget(open_folder_btn)
        buttons_layout.addWidget(delete_btn)
        buttons_layout.addStretch()
        
        layout.addLayout(buttons_layout)
        tab_widget.setLayout(layout)
    
    def _create_charts_tab(self, tab_widget):
        """Créer l'onglet des graphiques"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)
        
        # Boutons de génération de graphiques
        charts_buttons_layout = QGridLayout()
        
        if FLUENT_AVAILABLE:
            stock_evolution_btn = PushButton("Évolution du Stock")
            stock_evolution_btn.setIcon(FluentIcon.VIEW)

            movements_chart_btn = PushButton("Mouvements par Type")
            movements_chart_btn.setIcon(FluentIcon.CHART)

            top_articles_btn = PushButton("Top Articles")
            top_articles_btn.setIcon(FluentIcon.VIEW)

            stock_value_btn = PushButton("Valeur par Catégorie")
            stock_value_btn.setIcon(FluentIcon.CHART)
        else:
            stock_evolution_btn = QPushButton("Évolution du Stock")
            movements_chart_btn = QPushButton("Mouvements par Type")
            top_articles_btn = QPushButton("Top Articles")
            stock_value_btn = QPushButton("Valeur par Catégorie")
        
        stock_evolution_btn.clicked.connect(self._generate_stock_evolution_chart)
        movements_chart_btn.clicked.connect(self._generate_movements_chart)
        top_articles_btn.clicked.connect(self._generate_top_articles_chart)
        stock_value_btn.clicked.connect(self._generate_stock_value_chart)
        
        charts_buttons_layout.addWidget(stock_evolution_btn, 0, 0)
        charts_buttons_layout.addWidget(movements_chart_btn, 0, 1)
        charts_buttons_layout.addWidget(top_articles_btn, 1, 0)
        charts_buttons_layout.addWidget(stock_value_btn, 1, 1)
        
        layout.addLayout(charts_buttons_layout)
        
        # Zone d'affichage des graphiques
        self.charts_display = QLabel("Sélectionnez un graphique à générer")
        self.charts_display.setAlignment(Qt.AlignCenter)
        self.charts_display.setMinimumHeight(400)
        self.charts_display.setStyleSheet("border: 1px solid gray; background-color: white;")
        layout.addWidget(self.charts_display)
        
        tab_widget.setLayout(layout)

    def _generate_stock_report(self):
        """Générer le rapport de stock"""
        try:
            format_type = self.stock_format_combo.currentData()
            include_charts = self.stock_charts_check.isChecked()

            params = {
                'format': format_type,
                'include_charts': include_charts
            }

            self._start_report_generation("stock", params)

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport de stock: {e}")
            self._show_error("Erreur", f"Erreur lors de la génération: {e}")

    def _generate_movements_report(self):
        """Générer le rapport des mouvements"""
        try:
            start_date = self.movements_start_date.date().toPyDate()
            end_date = self.movements_end_date.date().toPyDate()
            format_type = self.movements_format_combo.currentData()

            if start_date > end_date:
                self._show_warning("Dates invalides", "La date de début doit être antérieure à la date de fin")
                return

            params = {
                'start_date': start_date,
                'end_date': end_date,
                'format': format_type
            }

            self._start_report_generation("movements", params)

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport des mouvements: {e}")
            self._show_error("Erreur", f"Erreur lors de la génération: {e}")

    def _generate_suppliers_report(self):
        """Générer le rapport des fournisseurs"""
        try:
            format_type = self.suppliers_format_combo.currentData()

            params = {
                'format': format_type
            }

            self._start_report_generation("suppliers", params)

        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport des fournisseurs: {e}")
            self._show_error("Erreur", f"Erreur lors de la génération: {e}")

    def _start_report_generation(self, report_type: str, params: dict):
        """Démarrer la génération d'un rapport en arrière-plan"""
        if self.generation_thread and self.generation_thread.isRunning():
            self._show_warning("Génération en cours", "Un rapport est déjà en cours de génération")
            return

        # Préparer l'interface
        self.status_label.setText(f"Génération du rapport {report_type} en cours...")
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(True)

        # Créer et démarrer le thread
        self.generation_thread = ReportGenerationThread(
            self.report_controller,
            report_type,
            params
        )

        self.generation_thread.progress_updated.connect(self._update_progress)
        self.generation_thread.report_generated.connect(self._on_report_generated)
        self.generation_thread.start()

    @pyqtSlot(int)
    def _update_progress(self, value: int):
        """Mettre à jour la barre de progression"""
        self.progress_bar.setValue(value)

    @pyqtSlot(bool, str, str)
    def _on_report_generated(self, success: bool, filepath: str, message: str):
        """Gérer la fin de génération d'un rapport"""
        self.progress_bar.setVisible(False)

        if success:
            self.status_label.setText(f"Rapport généré avec succès: {os.path.basename(filepath)}")
            self._show_success("Succès", f"Rapport généré: {os.path.basename(filepath)}")
            self._load_generated_reports()
        else:
            self.status_label.setText("Erreur lors de la génération")
            self._show_error("Erreur", message)

        # Nettoyer le thread
        if self.generation_thread:
            self.generation_thread.deleteLater()
            self.generation_thread = None

    def _load_generated_reports(self):
        """Charger la liste des rapports générés"""
        try:
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
                return

            self.reports_list.clear()
            self.generated_reports = []

            # Lister tous les fichiers dans le dossier reports
            for filename in os.listdir(reports_dir):
                if filename.endswith(('.pdf', '.xlsx', '.html')):
                    filepath = os.path.join(reports_dir, filename)
                    file_info = {
                        'name': filename,
                        'path': filepath,
                        'size': os.path.getsize(filepath),
                        'modified': datetime.fromtimestamp(os.path.getmtime(filepath))
                    }

                    self.generated_reports.append(file_info)

                    # Créer l'item de liste
                    item_text = f"{filename} ({file_info['size']} bytes) - {file_info['modified'].strftime('%d/%m/%Y %H:%M')}"
                    item = QListWidgetItem(item_text)
                    item.setData(Qt.UserRole, file_info)
                    self.reports_list.addItem(item)

            # Trier par date de modification (plus récent en premier)
            self.generated_reports.sort(key=lambda x: x['modified'], reverse=True)

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des rapports: {e}")

    def _open_report(self, item: QListWidgetItem):
        """Ouvrir un rapport"""
        file_info = item.data(Qt.UserRole)
        if file_info:
            self._open_file(file_info['path'])

    def _open_selected_report(self):
        """Ouvrir le rapport sélectionné"""
        current_item = self.reports_list.currentItem()
        if current_item:
            self._open_report(current_item)

    def _open_file(self, filepath: str):
        """Ouvrir un fichier avec l'application par défaut"""
        try:
            import subprocess
            import platform

            if platform.system() == 'Windows':
                os.startfile(filepath)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.call(['open', filepath])
            else:  # Linux
                subprocess.call(['xdg-open', filepath])

        except Exception as e:
            self.logger.error(f"Erreur lors de l'ouverture du fichier: {e}")
            self._show_error("Erreur", f"Impossible d'ouvrir le fichier: {e}")

    def _open_reports_folder(self):
        """Ouvrir le dossier des rapports"""
        reports_dir = os.path.abspath("reports")
        self._open_file(reports_dir)

    def _delete_selected_report(self):
        """Supprimer le rapport sélectionné"""
        current_item = self.reports_list.currentItem()
        if not current_item:
            return

        file_info = current_item.data(Qt.UserRole)
        if not file_info:
            return

        reply = QMessageBox.question(
            self,
            "Confirmer la suppression",
            f"Êtes-vous sûr de vouloir supprimer le rapport '{file_info['name']}'?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                os.remove(file_info['path'])
                self._load_generated_reports()
                self._show_success("Succès", "Rapport supprimé")
            except Exception as e:
                self.logger.error(f"Erreur lors de la suppression: {e}")
                self._show_error("Erreur", f"Impossible de supprimer le fichier: {e}")

    def _show_success(self, title: str, message: str):
        """Afficher un message de succès"""
        if FLUENT_AVAILABLE:
            InfoBar.success(title, message, parent=self)
        else:
            QMessageBox.information(self, title, message)

    def _show_warning(self, title: str, message: str):
        """Afficher un message d'avertissement"""
        if FLUENT_AVAILABLE:
            InfoBar.warning(title, message, parent=self)
        else:
            QMessageBox.warning(self, title, message)

    def _show_error(self, title: str, message: str):
        """Afficher un message d'erreur"""
        if FLUENT_AVAILABLE:
            InfoBar.error(title, message, parent=self)
        else:
            QMessageBox.critical(self, title, message)
