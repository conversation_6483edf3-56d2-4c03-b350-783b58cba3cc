#!/usr/bin/env python3
"""
Test Simple des Méthodes - GSlim
Vérifie que les méthodes ajoutées fonctionnent
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_article_methods():
    """Tester les méthodes du contrôleur d'articles"""
    print("🧪 Test des méthodes d'articles...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.article_controller import ArticleController
        
        db_manager = DatabaseManager()
        db_manager.connect()
        
        controller = ArticleController(db_manager)
        
        # Test get_stock_statistics
        if hasattr(controller, 'get_stock_statistics'):
            stats = controller.get_stock_statistics()
            print(f"✅ get_stock_statistics: {stats}")
        else:
            print("❌ get_stock_statistics manquante")
            return False
        
        # Test get_all
        if hasattr(controller, 'get_all'):
            articles = controller.get_all()
            print(f"✅ get_all: {len(articles)} articles")
        else:
            print("❌ get_all manquante")
            return False
        
        db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


def test_supplier_methods():
    """Tester les méthodes du contrôleur de fournisseurs"""
    print("\n🧪 Test des méthodes de fournisseurs...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.supplier_controller import SupplierController
        
        db_manager = DatabaseManager()
        db_manager.connect()
        
        controller = SupplierController(db_manager)
        
        # Test count
        if hasattr(controller, 'count'):
            count = controller.count()
            print(f"✅ count: {count} fournisseurs")
        else:
            print("❌ count manquante")
            return False
        
        # Test get_supplier_by_id
        if hasattr(controller, 'get_supplier_by_id'):
            supplier = controller.get_supplier_by_id(1)
            if supplier:
                print(f"✅ get_supplier_by_id: {supplier['nom']}")
            else:
                print("✅ get_supplier_by_id: aucun fournisseur avec ID 1")
        else:
            print("❌ get_supplier_by_id manquante")
            return False
        
        db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


def main():
    """Fonction principale"""
    print("🧪 TEST SIMPLE DES MÉTHODES AJOUTÉES")
    print("="*50)
    
    tests = [
        ("Méthodes d'articles", test_article_methods),
        ("Méthodes de fournisseurs", test_supplier_methods)
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                success_count += 1
                print(f"✅ {test_name} - RÉUSSI")
            else:
                print(f"❌ {test_name} - ÉCHEC")
        except Exception as e:
            print(f"❌ {test_name} - ERREUR: {e}")
    
    print(f"\n📊 RÉSULTATS")
    print("="*20)
    print(f"Tests réussis: {success_count}/{len(tests)}")
    
    if success_count == len(tests):
        print("\n🎉 TOUTES LES MÉTHODES FONCTIONNENT !")
        print("✅ get_stock_statistics disponible")
        print("✅ get_all disponible")
        print("✅ count disponible")
        print("✅ get_supplier_by_id disponible")
        
        print("\n🚀 Votre application devrait maintenant fonctionner sans erreurs !")
        print("   python main.py")
        print("   Connectez-vous avec: admin / admin123")
        
        return True
    else:
        print("\n⚠️  Certaines méthodes ont des problèmes")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
