"""
Dialogue de gestion des catégories - Liste complète
Conforme au cahier des charges
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QHeaderView, QAbstractItemView, QMessageBox, QLabel
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        Dialog, TableWidget, PushButton, TitleLabel, FluentIcon
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger
from .category_dialog import CategoryDialog


class CategoriesManagementDialog(QDialog):
    """
    Dialogue de gestion des catégories selon les spécifications
    Permet d'ajouter, modifier et supprimer des catégories
    """

    def __init__(self, parent=None, app_instance=None):
        super().__init__(parent)
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        
        # Variables d'instance
        self.categories_data = []
        
        # Initialiser l'interface
        self._init_ui()
        
        # Charger les données
        self.refresh_data()
        
        self.logger.info("Dialogue de gestion des catégories ouvert")

    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Configuration de la fenêtre
        self.setWindowTitle("Gestion des Catégories")
        self.setModal(True)
        self.resize(600, 400)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # En-tête
        self._create_header(main_layout)
        
        # Tableau des catégories
        self._create_categories_table(main_layout)
        
        # Barre d'actions
        self._create_action_bar(main_layout)
        
        # Boutons de fermeture
        self._create_close_buttons(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du dialogue"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Catégories")
        else:
            title = QLabel("Gestion des Catégories")
            title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        
        subtitle = QLabel("Gérez les catégories de produits")
        subtitle.setFont(QFont("Segoe UI", 10))
        
        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_categories_table(self, layout):
        """Créer le tableau des catégories"""
        if FLUENT_AVAILABLE:
            self.categories_table = TableWidget()
        else:
            self.categories_table = QTableWidget()
        
        # Configuration du tableau
        columns = ["ID", "Nom", "Description", "Date de création"]
        
        self.categories_table.setColumnCount(len(columns))
        self.categories_table.setHorizontalHeaderLabels(columns)
        
        # Configuration de l'affichage
        self.categories_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.categories_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.categories_table.setAlternatingRowColors(True)
        
        # Ajustement des colonnes
        header = self.categories_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Description
        
        # Double-clic pour modifier
        self.categories_table.doubleClicked.connect(self._on_category_double_click)
        
        layout.addWidget(self.categories_table, 1)

    def _create_action_bar(self, layout):
        """Créer la barre d'actions"""
        actions_layout = QHBoxLayout()
        
        # Boutons d'action
        if FLUENT_AVAILABLE:
            self.add_btn = PushButton("Ajouter")
            self.add_btn.setIcon(FluentIcon.ADD)
            
            self.edit_btn = PushButton("Modifier")
            self.edit_btn.setIcon(FluentIcon.EDIT)
            
            self.delete_btn = PushButton("Supprimer")
            self.delete_btn.setIcon(FluentIcon.DELETE)
        else:
            self.add_btn = QPushButton("Ajouter")
            self.edit_btn = QPushButton("Modifier")
            self.delete_btn = QPushButton("Supprimer")
        
        # État initial des boutons
        self.edit_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
        
        # Connecter les signaux
        self.add_btn.clicked.connect(self._on_add_category)
        self.edit_btn.clicked.connect(self._on_edit_category)
        self.delete_btn.clicked.connect(self._on_delete_category)
        
        # Gestion de la sélection
        self.categories_table.itemSelectionChanged.connect(self._on_selection_changed)
        
        # Assemblage
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)

    def _create_close_buttons(self, layout):
        """Créer les boutons de fermeture"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            close_btn = PushButton("Fermer")
            close_btn.setIcon(FluentIcon.CLOSE)
        else:
            close_btn = QPushButton("Fermer")
        
        close_btn.clicked.connect(self.accept)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)
        
        layout.addLayout(buttons_layout)

    def _update_table_display(self):
        """Mettre à jour l'affichage du tableau"""
        self.categories_table.setRowCount(len(self.categories_data))
        
        for row, category in enumerate(self.categories_data):
            # Colonnes
            self.categories_table.setItem(row, 0, QTableWidgetItem(str(category.get('id', ''))))
            self.categories_table.setItem(row, 1, QTableWidgetItem(category.get('name', '')))
            self.categories_table.setItem(row, 2, QTableWidgetItem(category.get('description', '')))
            
            # Date de création formatée
            created_at = category.get('created_at', '')
            if created_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%d/%m/%Y %H:%M')
                except:
                    formatted_date = created_at
            else:
                formatted_date = ''
            
            self.categories_table.setItem(row, 3, QTableWidgetItem(formatted_date))

    def _on_selection_changed(self):
        """Gérer le changement de sélection"""
        has_selection = len(self.categories_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _on_category_double_click(self):
        """Gérer le double-clic sur une catégorie"""
        self._on_edit_category()

    def _on_add_category(self):
        """Ajouter une nouvelle catégorie"""
        try:
            dialog = CategoryDialog(self)
            if dialog.exec_() == dialog.Accepted:
                category_data = dialog.get_category_data()
                
                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                category_id = db_manager.create_category(
                    category_data['name'], 
                    category_data['description']
                )
                
                if category_id:
                    self.refresh_data()
                    QMessageBox.information(self, "Succès", "Catégorie ajoutée avec succès")
                else:
                    QMessageBox.warning(self, "Erreur", "Erreur lors de la sauvegarde de la catégorie")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def _on_edit_category(self):
        """Modifier la catégorie sélectionnée"""
        try:
            selected_row = self.categories_table.currentRow()
            if selected_row < 0:
                return
            
            category = self.categories_data[selected_row]
            
            dialog = CategoryDialog(self, category)
            if dialog.exec_() == dialog.Accepted:
                category_data = dialog.get_category_data()
                
                # Mettre à jour en base de données
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.update_category(category['id'], category_data)
                
                if success:
                    self.refresh_data()
                    QMessageBox.information(self, "Succès", "Catégorie modifiée avec succès")
                else:
                    QMessageBox.warning(self, "Erreur", "Erreur lors de la mise à jour de la catégorie")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la modification: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification: {e}")

    def _on_delete_category(self):
        """Supprimer la catégorie sélectionnée"""
        try:
            selected_row = self.categories_table.currentRow()
            if selected_row < 0:
                return
            
            category = self.categories_data[selected_row]
            
            # Dialogue de confirmation
            reply = QMessageBox.question(
                self,
                "Confirmer la suppression",
                f"Êtes-vous sûr de vouloir supprimer la catégorie '{category.get('name', '')}'?\n\n"
                "Les produits de cette catégorie passeront en 'Aucune catégorie'.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # Supprimer de la base de données
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.delete_category(category['id'])
                
                if success:
                    self.refresh_data()
                    QMessageBox.information(self, "Succès", "Catégorie supprimée avec succès")
                else:
                    QMessageBox.warning(self, "Erreur", "Erreur lors de la suppression de la catégorie")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {e}")

    def refresh_data(self):
        """Actualiser les données depuis la base de données"""
        try:
            db_manager = self.app_instance.get_database_manager()
            self.categories_data = db_manager.get_categories()
            self._update_table_display()
            
            self.logger.info(f"{len(self.categories_data)} catégories chargées")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur de chargement: {e}")
