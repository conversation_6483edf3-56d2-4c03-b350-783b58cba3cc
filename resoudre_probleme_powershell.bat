@echo off
echo.
echo ========================================
echo   🔧 RÉSOLUTION PROBLÈME POWERSHELL
echo ========================================
echo.
echo Le problème que vous rencontrez est lié aux politiques
echo d'exécution de PowerShell sur Windows.
echo.
echo 💡 SOLUTIONS DISPONIBLES:
echo.
echo 1️⃣  SOLUTION SIMPLE (Recommandée)
echo    Utilisez le fichier .bat au lieu de PowerShell:
echo    📁 Double-cliquez sur: activer_venv.bat
echo.
echo 2️⃣  SOLUTION POWERSHELL
echo    Modifiez la politique d'exécution:
echo.
echo    a) Ouvrez PowerShell en tant qu'administrateur
echo    b) Exécutez cette commande:
echo       Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
echo    c) Confirmez avec 'Y'
echo    d) Puis utilisez: venv\Scripts\Activate.ps1
echo.
echo 3️⃣  SOLUTION ALTERNATIVE
echo    Activez manuellement dans le terminal:
echo.
echo    Dans Command Prompt (cmd):
echo    venv\Scripts\activate.bat
echo.
echo    Dans PowerShell:
echo    venv\Scripts\Activate.ps1
echo.
echo ========================================
echo.
echo 🚀 ACTIVATION AUTOMATIQUE (Solution 1)
echo.
set /p choice="Voulez-vous activer l'environnement maintenant? (O/N): "
if /i "%choice%"=="O" (
    echo.
    echo 🔄 Activation en cours...
    call venv\Scripts\activate.bat
    echo.
    echo ✅ Environnement virtuel activé !
    echo 📋 Vous pouvez maintenant lancer: python main.py
    echo.
    cmd /k
) else (
    echo.
    echo 📋 Utilisez une des solutions ci-dessus quand vous serez prêt.
)
echo.
pause
