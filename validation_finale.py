#!/usr/bin/env python3
"""
Script de validation finale pour GSlim
Vérifie que toutes les erreurs ont été corrigées
"""

import sys
import os
import traceback
from pathlib import Path

def print_header(title):
    """Afficher un en-tête formaté"""
    print(f"\n{'='*60}")
    print(f"✅ {title}")
    print(f"{'='*60}")

def print_step(step):
    """Afficher une étape"""
    print(f"\n📋 {step}")
    print("-" * 40)

def test_imports():
    """Tester tous les imports critiques"""
    print_step("Test des imports critiques")
    
    # Ajouter src au path
    if 'src' not in sys.path:
        sys.path.insert(0, 'src')
    
    imports_tests = [
        ("PyQt5", "from PyQt5.QtWidgets import QApplication"),
        ("PyQt5 Core", "from PyQt5.QtCore import Qt"),
        ("PyQt5 GUI", "from PyQt5.QtGui import QFont"),
        ("Configuration", "from config.settings import config"),
        ("Database Manager", "from database.manager import DatabaseManager"),
        ("Logger", "from utils.logger import setup_logger"),
        ("Theme Manager", "from styles.themes import theme_manager"),
        ("Login Window", "from views.login_window import LoginWindow"),
        ("Main Window", "from views.main_window import MainWindow"),
        ("Application", "from src.app import GSlimApp"),
    ]
    
    success_count = 0
    for name, import_statement in imports_tests:
        try:
            exec(import_statement)
            print(f"✅ {name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {name}: {e}")
    
    print(f"\n📊 Résultat: {success_count}/{len(imports_tests)} imports réussis")
    return success_count == len(imports_tests)

def test_database():
    """Tester la base de données"""
    print_step("Test de la base de données")
    
    try:
        # Ajouter src au path
        if 'src' not in sys.path:
            sys.path.insert(0, 'src')
        
        from database.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        # Test de connexion
        db_manager.connect()
        
        # Test d'une requête simple
        cursor = db_manager.cursor
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"✅ Base de données connectée")
        print(f"✅ {len(tables)} tables trouvées")
        
        # Vérifier les tables principales
        table_names = [table[0] for table in tables]
        required_tables = ['users', 'articles', 'categories', 'suppliers', 'stock_movements']
        
        for table in required_tables:
            if table in table_names:
                print(f"✅ Table {table} présente")
            else:
                print(f"⚠️ Table {table} manquante")
        
        db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def test_application_init():
    """Tester l'initialisation de l'application"""
    print_step("Test d'initialisation de l'application")
    
    try:
        # Ajouter src au path
        if 'src' not in sys.path:
            sys.path.insert(0, 'src')
        
        from src.app import GSlimApp
        
        # Test d'initialisation (sans interface graphique)
        app = GSlimApp()
        
        print("✅ Application initialisée")
        print(f"✅ Database Manager: {app.db_manager is not None}")
        print(f"✅ Logger: {app.logger is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur initialisation: {e}")
        print(f"📋 Détails: {traceback.format_exc()}")
        return False

def test_modules():
    """Tester les modules principaux"""
    print_step("Test des modules principaux")
    
    try:
        # Ajouter src au path
        if 'src' not in sys.path:
            sys.path.insert(0, 'src')
        
        # Test des contrôleurs
        from controllers.article_controller import ArticleController
        print("✅ Article Controller")
        
        from controllers.supplier_controller import SupplierController
        print("✅ Supplier Controller")
        
        # Test des modèles
        from models.article import Article
        print("✅ Article Model")
        
        from models.supplier import Supplier
        print("✅ Supplier Model")
        
        # Test des vues
        from views.dashboard_window import DashboardWindow
        print("✅ Dashboard Window")
        
        from views.articles_window import ArticlesWindow
        print("✅ Articles Window")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur modules: {e}")
        return False

def check_files():
    """Vérifier la présence des fichiers critiques"""
    print_step("Vérification des fichiers")
    
    critical_files = [
        "main.py",
        "src/app.py",
        "src/config/settings.py",
        "src/database/manager.py",
        "src/views/login_window.py",
        "src/views/main_window.py",
        "src/utils/logger.py",
        "src/styles/themes.py",
        "data/gslim.db",
        "requirements.txt"
    ]
    
    missing_files = []
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} manquant")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def main():
    """Fonction principale de validation"""
    print_header("VALIDATION FINALE DE GSLIM")
    
    tests = [
        ("Fichiers critiques", check_files),
        ("Imports", test_imports),
        ("Base de données", test_database),
        ("Modules", test_modules),
        ("Application", test_application_init),
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        try:
            if test_function():
                success_count += 1
                print(f"✅ {test_name} - SUCCÈS")
            else:
                print(f"❌ {test_name} - ÉCHEC")
        except Exception as e:
            print(f"❌ {test_name} - ERREUR: {e}")
    
    # Résumé final
    print_header("RÉSUMÉ DE LA VALIDATION")
    
    if success_count == total_tests:
        print("🎉 VALIDATION COMPLÈTE RÉUSSIE !")
        print("✅ Toutes les erreurs ont été corrigées")
        print("✅ L'application est entièrement fonctionnelle")
        print("\n🚀 INSTRUCTIONS DE LANCEMENT:")
        print("   1. Ouvrez un terminal dans le répertoire du projet")
        print("   2. Exécutez: python main.py")
        print("   3. Connectez-vous avec:")
        print("      - Utilisateur: admin")
        print("      - Mot de passe: admin123")
        print("\n🎊 FÉLICITATIONS ! VOTRE APPLICATION EST PARFAITE !")
        
        return True
    else:
        print(f"⚠️ {total_tests - success_count} test(s) échoué(s)")
        print("💡 Consultez les messages d'erreur ci-dessus")
        
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Validation interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur critique: {e}")
        print(f"📋 Détails: {traceback.format_exc()}")
        sys.exit(1)
