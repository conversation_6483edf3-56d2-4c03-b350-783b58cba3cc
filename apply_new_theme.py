#!/usr/bin/env python3
"""
Script pour appliquer le nouveau thème professionnel à GSlim
Remplace le thème sombre actuel par un design moderne et élégant
"""

import sys
import os
import shutil
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def backup_current_theme():
    """Sauvegarder le thème actuel"""
    print("📦 Sauvegarde du thème actuel...")
    
    backup_dir = Path("backup_theme")
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "src/styles/themes.py",
        "src/app.py",
        "src/views/main_window.py"
    ]
    
    for file_path in files_to_backup:
        if os.path.exists(file_path):
            backup_path = backup_dir / Path(file_path).name
            shutil.copy2(file_path, backup_path)
            print(f"✅ Sauvegardé: {file_path}")
    
    print(f"📁 Sauvegardes dans: {backup_dir}")

def update_app_theme():
    """Mettre à jour l'application pour utiliser le nouveau thème"""
    print("\n🎨 Application du nouveau thème...")
    
    app_file = "src/app.py"
    if not os.path.exists(app_file):
        print(f"❌ Fichier {app_file} non trouvé")
        return False
    
    # Lire le contenu actuel
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter l'import du nouveau thème
    if "from styles.professional_theme import ProfessionalTheme" not in content:
        # Trouver la ligne d'import des styles
        lines = content.split('\n')
        insert_index = -1
        
        for i, line in enumerate(lines):
            if "from styles" in line or "import" in line:
                insert_index = i + 1
                break
        
        if insert_index > 0:
            lines.insert(insert_index, "from styles.professional_theme import ProfessionalTheme")
            content = '\n'.join(lines)
            print("✅ Import ProfessionalTheme ajouté")
    
    # Remplacer l'application du thème
    replacements = [
        ("theme_manager.get_current_theme()", "ProfessionalTheme.get_professional_stylesheet()"),
        ("ModernTheme.get_modern_stylesheet()", "ProfessionalTheme.get_professional_stylesheet()"),
        ("setStyleSheet(theme_manager", "setStyleSheet(ProfessionalTheme.get_professional_stylesheet())  # theme_manager")
    ]
    
    for old, new in replacements:
        if old in content:
            content = content.replace(old, new)
            print(f"✅ Remplacé: {old}")
    
    # Écrire le fichier mis à jour
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def create_theme_demo():
    """Créer une démonstration du nouveau thème"""
    print("\n🚀 Création de la démonstration du thème...")
    
    demo_content = '''#!/usr/bin/env python3
"""
Démonstration du nouveau thème professionnel GSlim
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QGridLayout
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

from styles.professional_theme import ProfessionalTheme


class ThemeDemo(QMainWindow):
    """Démonstration du thème professionnel"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("GSlim - Nouveau Thème Professionnel")
        self.setGeometry(100, 100, 1200, 800)
        
        # Appliquer le nouveau thème
        self.setStyleSheet(ProfessionalTheme.get_professional_stylesheet())
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurer l'interface"""
        central_widget = QWidget()
        central_widget.setProperty("class", "main-container")
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # Conteneur avec le nouveau design
        content_wrapper = QWidget()
        content_wrapper.setProperty("class", "content-wrapper")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(32, 32, 32, 32)
        content_layout.setSpacing(24)
        
        # Header élégant
        self.create_header(content_layout)
        
        # Cartes de démonstration
        self.create_demo_cards(content_layout)
        
        # Boutons de démonstration
        self.create_demo_buttons(content_layout)
        
        content_wrapper.setLayout(content_layout)
        main_layout.addWidget(content_wrapper)
        central_widget.setLayout(main_layout)
    
    def create_header(self, layout):
        """Créer l'en-tête"""
        header = QWidget()
        header.setProperty("class", "header")
        
        header_layout = QVBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(8)
        
        title = QLabel("Tableau de Bord GSlim")
        title.setProperty("class", "header-title")
        header_layout.addWidget(title)
        
        subtitle = QLabel("Interface moderne et professionnelle")
        subtitle.setProperty("class", "header-subtitle")
        header_layout.addWidget(subtitle)
        
        header.setLayout(header_layout)
        layout.addWidget(header)
    
    def create_demo_cards(self, layout):
        """Créer les cartes de démonstration"""
        cards_layout = QGridLayout()
        cards_layout.setSpacing(16)
        
        # Données de démonstration
        card_data = [
            ("Total Articles", "1,234", "stat-card-primary"),
            ("Ventes du Mois", "€45,678", "stat-card-success"),
            ("Commandes", "89", "stat-card-info"),
            ("Alertes", "3", "stat-card-warning")
        ]
        
        for i, (title, value, card_class) in enumerate(card_data):
            card = self.create_stat_card(title, value, card_class)
            cards_layout.addWidget(card, 0, i)
        
        layout.addLayout(cards_layout)
    
    def create_stat_card(self, title, value, card_class):
        """Créer une carte de statistique"""
        card = QWidget()
        card.setProperty("class", card_class)
        card.setFixedHeight(160)
        
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(24, 20, 24, 20)
        card_layout.setSpacing(12)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setProperty("class", "stat-value")
        card_layout.addWidget(value_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setProperty("class", "stat-label")
        card_layout.addWidget(title_label)
        
        card_layout.addStretch()
        card.setLayout(card_layout)
        
        return card
    
    def create_demo_buttons(self, layout):
        """Créer les boutons de démonstration"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(16)
        
        # Boutons avec différents styles
        primary_btn = QPushButton("Action Principale")
        primary_btn.clicked.connect(lambda: self.show_message("Bouton principal cliqué!"))
        buttons_layout.addWidget(primary_btn)
        
        secondary_btn = QPushButton("Action Secondaire")
        secondary_btn.setProperty("class", "secondary")
        secondary_btn.clicked.connect(lambda: self.show_message("Bouton secondaire cliqué!"))
        buttons_layout.addWidget(secondary_btn)
        
        success_btn = QPushButton("Succès")
        success_btn.setProperty("class", "success")
        success_btn.clicked.connect(lambda: self.show_message("Action de succès!"))
        buttons_layout.addWidget(success_btn)
        
        warning_btn = QPushButton("Attention")
        warning_btn.setProperty("class", "warning")
        warning_btn.clicked.connect(lambda: self.show_message("Attention requise!"))
        buttons_layout.addWidget(warning_btn)
        
        danger_btn = QPushButton("Danger")
        danger_btn.setProperty("class", "danger")
        danger_btn.clicked.connect(lambda: self.show_message("Action dangereuse!"))
        buttons_layout.addWidget(danger_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
    
    def show_message(self, message):
        """Afficher un message"""
        print(f"💬 {message}")


def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("GSlim Theme Demo")
    app.setApplicationVersion("1.0.0")
    
    # Créer et afficher la fenêtre
    window = ThemeDemo()
    window.show()
    
    print("🎨 Nouveau thème professionnel appliqué!")
    print("✨ Interface moderne avec dégradés et design élégant")
    
    # Démarrer la boucle d'événements
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
'''
    
    with open("theme_demo.py", 'w', encoding='utf-8') as f:
        f.write(demo_content)
    
    print("✅ Démonstration créée: theme_demo.py")

def create_integration_instructions():
    """Créer les instructions d'intégration"""
    print("\n📋 Création des instructions...")
    
    instructions = '''# 🎨 Nouveau Thème Professionnel - GSlim

## ✨ Améliorations Apportées

### Problèmes Résolus
- ❌ **Avant**: Thème sombre difficile à lire
- ❌ **Avant**: Interface peu attrayante
- ❌ **Avant**: Manque de contraste et de hiérarchie

### Solutions Implémentées
- ✅ **Après**: Dégradés élégants et modernes
- ✅ **Après**: Couleurs harmonieuses et professionnelles
- ✅ **Après**: Excellent contraste et lisibilité
- ✅ **Après**: Design moderne avec ombres et effets

## 🚀 Utilisation

### 1. Tester le Nouveau Thème
```bash
python theme_demo.py
```

### 2. Appliquer à l'Application Principale
```python
# Dans votre code
from styles.professional_theme import ProfessionalTheme

# Appliquer le thème
self.setStyleSheet(ProfessionalTheme.get_professional_stylesheet())
```

### 3. Classes CSS Disponibles

#### Conteneurs
- `main-container` - Conteneur principal avec dégradé
- `content-wrapper` - Wrapper de contenu avec transparence
- `dashboard-main` - Carte principale du dashboard
- `header` - En-tête avec dégradé

#### Cartes de Statistiques
- `stat-card-primary` - Carte principale (bleu/violet)
- `stat-card-success` - Carte de succès (vert)
- `stat-card-warning` - Carte d'avertissement (orange)
- `stat-card-error` - Carte d'erreur (rouge)
- `stat-card-info` - Carte d'information (bleu)

#### Boutons
- Bouton par défaut - Style principal
- `secondary` - Bouton secondaire
- `success` - Bouton de succès
- `warning` - Bouton d'avertissement
- `danger` - Bouton de danger

#### Texte
- `header-title` - Titre d'en-tête
- `header-subtitle` - Sous-titre d'en-tête
- `title` - Titre principal
- `subtitle` - Sous-titre
- `body` - Texte de corps
- `stat-value` - Valeur de statistique
- `stat-label` - Label de statistique

## 🎯 Caractéristiques du Design

### Couleurs
- **Dégradés modernes**: Transitions fluides entre couleurs
- **Palette harmonieuse**: Couleurs professionnelles et élégantes
- **Excellent contraste**: Lisibilité optimale

### Effets Visuels
- **Ombres subtiles**: Profondeur et hiérarchie
- **Bordures arrondies**: Design moderne et doux
- **Transparences**: Effet de verre moderne
- **Animations**: Transitions fluides au survol

### Typographie
- **Hiérarchie claire**: Tailles et poids appropriés
- **Espacement optimisé**: Lisibilité améliorée
- **Police moderne**: Segoe UI, Inter, Roboto

## 🔄 Migration

### Étapes Simples
1. Sauvegarder l'ancien thème (fait automatiquement)
2. Importer le nouveau thème
3. Appliquer le stylesheet
4. Tester l'interface

### Restauration
En cas de problème, restaurez depuis `backup_theme/`

## 💡 Conseils

### Pour Optimiser l'Apparence
1. Utilisez les classes CSS appropriées
2. Respectez la hiérarchie des couleurs
3. Testez sur différentes résolutions
4. Maintenez la cohérence visuelle

### Personnalisation
- Modifiez les dégradés dans `professional_theme.py`
- Ajustez les couleurs selon vos préférences
- Adaptez les ombres et rayons selon vos besoins
'''
    
    with open("NOUVEAU_THEME.md", 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ Instructions créées: NOUVEAU_THEME.md")

def run_theme_update():
    """Exécuter la mise à jour du thème"""
    print("🎨 Application du Nouveau Thème Professionnel - GSlim")
    print("="*60)
    
    steps = [
        ("Sauvegarde du thème actuel", backup_current_theme),
        ("Application du nouveau thème", update_app_theme),
        ("Création de la démonstration", create_theme_demo),
        ("Création des instructions", create_integration_instructions)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            result = step_func()
            if result is not False:
                success_count += 1
                print(f"✅ {step_name} terminé")
            else:
                print(f"❌ {step_name} échoué")
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DE LA MISE À JOUR")
    print("="*60)
    print(f"Étapes réussies: {success_count}/{len(steps)}")
    
    if success_count == len(steps):
        print("\n🎉 Nouveau thème appliqué avec succès!")
        print("\n📋 Prochaines étapes:")
        print("1. Tester: python theme_demo.py")
        print("2. Consulter: NOUVEAU_THEME.md")
        print("3. Redémarrer votre application GSlim")
        print("\n✨ Votre interface sera maintenant moderne et professionnelle!")
    else:
        print("\n⚠️  Application partielle. Vérifiez les erreurs ci-dessus.")

def main():
    """Fonction principale"""
    try:
        run_theme_update()
        return 0
    except KeyboardInterrupt:
        print("\n⏹️  Mise à jour interrompue par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
