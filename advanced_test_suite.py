#!/usr/bin/env python3
"""
Suite de Tests Avancée - GSlim
Tests complets pour toutes les fonctionnalités développées
"""

import sys
import os
import unittest
import time
import sqlite3
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtCore import QTimer
from PyQt5.QtTest import QTest

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class TestThemeSystem(unittest.TestCase):
    """Tests du système de thèmes"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_theme_manager_singleton(self):
        """Tester le pattern singleton du gestionnaire de thèmes"""
        from styles.theme_manager import get_theme_manager
        
        manager1 = get_theme_manager()
        manager2 = get_theme_manager()
        
        self.assertIs(manager1, manager2, "Le gestionnaire de thèmes doit être un singleton")
    
    def test_all_themes_available(self):
        """Tester que tous les thèmes sont disponibles"""
        from styles.theme_manager import get_theme_manager, ThemeType
        
        manager = get_theme_manager()
        available_themes = manager.get_available_themes()
        
        expected_themes = [
            ThemeType.MODERN,
            ThemeType.PROFESSIONAL,
            ThemeType.FLUENT,
            ThemeType.CYBERPUNK,
            ThemeType.CLASSIC
        ]
        
        for theme in expected_themes:
            self.assertIn(theme, available_themes, f"Thème {theme} manquant")
    
    def test_theme_switching(self):
        """Tester le changement de thèmes"""
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        
        manager = get_theme_manager()
        
        # Tester le changement vers chaque thème
        for theme_type in [ThemeType.MODERN, ThemeType.PROFESSIONAL, ThemeType.CYBERPUNK]:
            manager.set_theme(theme_type, ThemeMode.DARK)
            current_theme = manager.get_current_theme()
            
            self.assertEqual(current_theme['type'], theme_type)
            self.assertEqual(current_theme['mode'], ThemeMode.DARK)
    
    def test_theme_persistence(self):
        """Tester la sauvegarde des préférences de thème"""
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        
        manager = get_theme_manager()
        
        # Définir un thème
        manager.set_theme(ThemeType.CYBERPUNK, ThemeMode.DARK)
        
        # Sauvegarder
        manager.save_preferences()
        
        # Créer un nouveau gestionnaire
        manager2 = get_theme_manager()
        manager2.load_preferences()
        
        current_theme = manager2.get_current_theme()
        self.assertEqual(current_theme['type'], ThemeType.CYBERPUNK)


class TestEnhancedWidgets(unittest.TestCase):
    """Tests des widgets avancés"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_enhanced_card_creation(self):
        """Tester la création d'une carte améliorée"""
        from widgets.enhanced_widgets import EnhancedCard
        
        card = EnhancedCard("Test Card", None)
        
        self.assertIsNotNone(card)
        self.assertEqual(card.title, "Test Card")
        self.assertIsInstance(card, QWidget)
    
    def test_enhanced_card_with_content(self):
        """Tester une carte avec contenu"""
        from widgets.enhanced_widgets import EnhancedCard
        from PyQt5.QtWidgets import QLabel
        
        content = QLabel("Test Content")
        card = EnhancedCard("Test Card", content)
        
        self.assertIsNotNone(card)
        self.assertEqual(card.title, "Test Card")
    
    def test_enhanced_table_creation(self):
        """Tester la création d'une table améliorée"""
        from widgets.enhanced_widgets import EnhancedTable
        
        headers = ["ID", "Nom", "Prix"]
        table = EnhancedTable(headers)
        
        self.assertIsNotNone(table)
        self.assertEqual(table.columnCount(), len(headers))
    
    def test_enhanced_form_creation(self):
        """Tester la création d'un formulaire amélioré"""
        from widgets.enhanced_widgets import EnhancedForm
        
        fields = [
            {"name": "nom", "label": "Nom", "type": "text", "required": True},
            {"name": "prix", "label": "Prix", "type": "number", "required": True}
        ]
        
        form = EnhancedForm(fields)
        
        self.assertIsNotNone(form)
        self.assertEqual(len(form.fields), len(fields))
    
    def test_status_indicator(self):
        """Tester l'indicateur de statut"""
        from widgets.enhanced_widgets import StatusIndicator
        
        indicator = StatusIndicator("success", "Opération réussie")
        
        self.assertIsNotNone(indicator)
        self.assertEqual(indicator.status, "success")
        self.assertEqual(indicator.message, "Opération réussie")


class TestDatabaseIntegration(unittest.TestCase):
    """Tests d'intégration de la base de données"""
    
    def setUp(self):
        """Configuration des tests"""
        self.test_db_path = ":memory:"  # Base de données en mémoire pour les tests
    
    def test_database_manager_creation(self):
        """Tester la création du gestionnaire de base de données"""
        from database.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        self.assertIsNotNone(db_manager)
        self.assertTrue(hasattr(db_manager, 'cursor'))
    
    def test_database_connection(self):
        """Tester la connexion à la base de données"""
        from database.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        connection = db_manager.connect()
        
        self.assertIsNotNone(connection)
        
        # Nettoyer
        db_manager.disconnect()
    
    def test_article_controller_crud(self):
        """Tester les opérations CRUD du contrôleur d'articles"""
        from controllers.article_controller import ArticleController
        from database.manager import DatabaseManager
        
        # Mock du gestionnaire de base de données
        mock_db = Mock()
        mock_connection = sqlite3.connect(":memory:")
        mock_db.connection = mock_connection
        mock_db.cursor = mock_connection.cursor()
        
        # Créer la table articles pour le test
        cursor = mock_connection.cursor()
        cursor.execute("""
            CREATE TABLE articles (
                id INTEGER PRIMARY KEY,
                nom TEXT NOT NULL,
                description TEXT,
                prix_unitaire REAL NOT NULL,
                quantite_stock INTEGER NOT NULL,
                seuil_alerte INTEGER DEFAULT 0,
                categorie_id INTEGER,
                fournisseur_id INTEGER,
                date_creation TEXT,
                date_modification TEXT
            )
        """)
        mock_connection.commit()
        
        controller = ArticleController(mock_db)
        
        # Test de création d'article
        article_data = {
            'nom': 'Test Article',
            'description': 'Description test',
            'prix_unitaire': 10.50,
            'quantite_stock': 100,
            'seuil_alerte': 10
        }
        
        article_id = controller.create_article(article_data)
        self.assertIsNotNone(article_id)
        
        # Test de récupération
        article = controller.get_article_by_id(article_id)
        self.assertIsNotNone(article)
        self.assertEqual(article['nom'], 'Test Article')
        
        # Test de mise à jour
        updated_data = article_data.copy()
        updated_data['nom'] = 'Article Modifié'
        
        success = controller.update_article(article_id, updated_data)
        self.assertTrue(success)
        
        # Test de suppression
        success = controller.delete_article(article_id)
        self.assertTrue(success)


class TestIntegratedDashboard(unittest.TestCase):
    """Tests du dashboard intégré"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_dashboard_creation(self):
        """Tester la création du dashboard"""
        from views.integrated_dashboard import IntegratedDashboard
        
        # Mock de l'instance d'application
        mock_app = Mock()
        mock_app.get_database_manager.return_value = Mock()
        
        dashboard = IntegratedDashboard(mock_app)
        
        self.assertIsNotNone(dashboard)
        self.assertIsInstance(dashboard, QWidget)
    
    def test_dashboard_tabs(self):
        """Tester les onglets du dashboard"""
        from views.integrated_dashboard import IntegratedDashboard
        
        mock_app = Mock()
        mock_app.get_database_manager.return_value = Mock()
        
        dashboard = IntegratedDashboard(mock_app)
        
        # Vérifier que les onglets sont créés
        self.assertTrue(hasattr(dashboard, 'tab_widget'))
        self.assertGreater(dashboard.tab_widget.count(), 0)


class TestEnhancedArticlesWindow(unittest.TestCase):
    """Tests de la fenêtre articles améliorée"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_articles_window_creation(self):
        """Tester la création de la fenêtre articles"""
        from views.enhanced_articles_window import EnhancedArticlesWindow
        
        # Mock de l'instance d'application
        mock_app = Mock()
        mock_db = Mock()
        mock_db.connection = None
        mock_app.get_database_manager.return_value = mock_db
        
        with patch('controllers.article_controller.ArticleController'):
            articles_window = EnhancedArticlesWindow(mock_app)
            
            self.assertIsNotNone(articles_window)
            self.assertIsInstance(articles_window, QWidget)


class TestPerformance(unittest.TestCase):
    """Tests de performance"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_theme_switching_performance(self):
        """Tester la performance du changement de thèmes"""
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        
        manager = get_theme_manager()
        
        start_time = time.time()
        
        # Changer de thème plusieurs fois
        for _ in range(10):
            manager.set_theme(ThemeType.MODERN, ThemeMode.DARK)
            manager.set_theme(ThemeType.CYBERPUNK, ThemeMode.DARK)
            manager.set_theme(ThemeType.PROFESSIONAL, ThemeMode.LIGHT)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Le changement de thème doit être rapide (moins de 1 seconde pour 30 changements)
        self.assertLess(duration, 1.0, "Le changement de thème est trop lent")
    
    def test_widget_creation_performance(self):
        """Tester la performance de création des widgets"""
        from widgets.enhanced_widgets import EnhancedCard
        
        start_time = time.time()
        
        # Créer plusieurs widgets
        cards = []
        for i in range(100):
            card = EnhancedCard(f"Card {i}", None)
            cards.append(card)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # La création de 100 cartes doit être rapide (moins de 2 secondes)
        self.assertLess(duration, 2.0, "La création des widgets est trop lente")


class TestErrorHandling(unittest.TestCase):
    """Tests de gestion d'erreurs"""
    
    def test_error_handler_installation(self):
        """Tester l'installation du gestionnaire d'erreurs"""
        from utils.error_handler import install_error_handler, error_handler
        
        install_error_handler()
        
        # Vérifier que le gestionnaire est installé
        self.assertEqual(sys.excepthook, error_handler.handle_exception)
    
    def test_database_error_handling(self):
        """Tester la gestion d'erreurs de base de données"""
        from controllers.article_controller import ArticleController
        
        # Mock d'un gestionnaire de base de données défaillant
        mock_db = Mock()
        mock_db.connection = None
        mock_db.cursor = None
        
        controller = ArticleController(mock_db)
        
        # Les méthodes doivent gérer gracieusement les erreurs
        articles = controller.get_all_articles()
        self.assertEqual(articles, [])
        
        article = controller.get_article_by_id(1)
        self.assertIsNone(article)


def run_advanced_tests():
    """Exécuter tous les tests avancés"""
    print("🧪 SUITE DE TESTS AVANCÉE - GSLIM")
    print("="*50)
    print("🎯 Tests complets de toutes les fonctionnalités...")
    print()
    
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter toutes les classes de tests
    test_classes = [
        TestThemeSystem,
        TestEnhancedWidgets,
        TestDatabaseIntegration,
        TestIntegratedDashboard,
        TestEnhancedArticlesWindow,
        TestPerformance,
        TestErrorHandling
    ]
    
    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Exécuter les tests avec un runner détaillé
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    print("🚀 Démarrage des tests...")
    print("-" * 50)
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # Résumé détaillé
    print("\n" + "="*50)
    print("📊 RÉSUMÉ DÉTAILLÉ DES TESTS")
    print("="*50)
    print(f"⏱️  Durée totale: {end_time - start_time:.2f} secondes")
    print(f"🧪 Tests exécutés: {result.testsRun}")
    print(f"✅ Réussites: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"❌ Échecs: {len(result.failures)}")
    print(f"💥 Erreurs: {len(result.errors)}")
    
    if result.failures:
        print(f"\n🔍 DÉTAILS DES ÉCHECS:")
        for test, traceback in result.failures:
            print(f"  ❌ {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
    
    if result.errors:
        print(f"\n🔍 DÉTAILS DES ERREURS:")
        for test, traceback in result.errors:
            print(f"  💥 {test}: {traceback.split('\\n')[-2]}")
    
    # Calcul du taux de réussite
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    
    print(f"\n📈 Taux de réussite: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("\n🎉 TOUS LES TESTS AVANCÉS RÉUSSIS !")
        print("✨ Votre application GSlim est complètement validée !")
        return True
    elif success_rate >= 80:
        print("\n⚠️  TESTS MAJORITAIREMENT RÉUSSIS")
        print("La plupart des fonctionnalités sont opérationnelles.")
        return True
    else:
        print("\n❌ PLUSIEURS TESTS ONT ÉCHOUÉ")
        print("Des améliorations sont nécessaires.")
        return False


def main():
    """Fonction principale"""
    try:
        success = run_advanced_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrompus par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\n💥 Erreur critique dans les tests: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
