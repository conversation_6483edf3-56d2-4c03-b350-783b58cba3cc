"""
Contrôleur complet pour la gestion des articles - Version sans pandas
"""

import sqlite3
from datetime import datetime
from typing import List, Dict, Optional, Any
from utils.logger import setup_logger


class ArticleController:
    """Contrôleur complet pour la gestion des articles"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if not self.db_manager.connection:
            self.db_manager.connect()
    
    def get_all_articles(self):
        """Récupérer tous les articles"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT a.*, s.nom as supplier_name, c.name as category_name
                FROM articles a
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                LEFT JOIN categories c ON a.categorie_id = c.id
                ORDER BY a.nom
            """)
            
            articles = cursor.fetchall()
            if articles:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in articles]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles: {e}")
            return []
    
    def get_all(self):
        """Alias pour get_all_articles (compatibilité)"""
        return self.get_all_articles()
    
    def get_stock_statistics(self):
        """Obtenir les statistiques de stock complètes"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {
                    'total_articles': 0,
                    'low_stock_count': 0,
                    'total_value': 0.0,
                    'out_of_stock': 0,
                    'average_price': 0.0,
                    'total_quantity': 0
                }
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {
                    'total_articles': 0,
                    'low_stock_count': 0,
                    'total_value': 0.0,
                    'out_of_stock': 0,
                    'average_price': 0.0,
                    'total_quantity': 0
                }
            
            # Total des articles
            cursor.execute("SELECT COUNT(*) FROM articles")
            total_articles = cursor.fetchone()[0]
            
            # Articles en stock faible
            cursor.execute("SELECT COUNT(*) FROM articles WHERE quantite_stock <= seuil_alerte")
            low_stock_count = cursor.fetchone()[0]
            
            # Valeur totale du stock
            cursor.execute("SELECT SUM(prix_unitaire * quantite_stock) FROM articles")
            result = cursor.fetchone()[0]
            total_value = result if result else 0.0
            
            # Articles en rupture
            cursor.execute("SELECT COUNT(*) FROM articles WHERE quantite_stock = 0")
            out_of_stock = cursor.fetchone()[0]
            
            # Prix moyen
            cursor.execute("SELECT AVG(prix_unitaire) FROM articles")
            result = cursor.fetchone()[0]
            average_price = result if result else 0.0
            
            # Quantité totale
            cursor.execute("SELECT SUM(quantite_stock) FROM articles")
            result = cursor.fetchone()[0]
            total_quantity = result if result else 0
            
            return {
                'total_articles': total_articles,
                'low_stock_count': low_stock_count,
                'total_value': float(total_value),
                'out_of_stock': out_of_stock,
                'average_price': float(average_price),
                'total_quantity': total_quantity
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques de stock: {e}")
            return {
                'total_articles': 0,
                'low_stock_count': 0,
                'total_value': 0.0,
                'out_of_stock': 0,
                'average_price': 0.0,
                'total_quantity': 0
            }
    
    def get_article_by_id(self, article_id):
        """Récupérer un article par son ID"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return None
            
            cursor = self.db_manager.cursor
            if not cursor:
                return None
            
            cursor.execute("""
                SELECT a.*, s.nom as supplier_name, c.name as category_name
                FROM articles a
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                LEFT JOIN categories c ON a.categorie_id = c.id
                WHERE a.id = ?
            """, (article_id,))
            
            result = cursor.fetchone()
            if result:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de l'article {article_id}: {e}")
            return None
    
    def add_article(self, article_data):
        """Ajouter un nouvel article"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return False
            
            cursor = self.db_manager.cursor
            if not cursor:
                return False
            
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO articles (nom, description, prix_unitaire, quantite_stock, 
                                    seuil_alerte, categorie_id, fournisseur_id, 
                                    date_creation, date_modification)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                article_data.get('nom'),
                article_data.get('description', ''),
                article_data.get('prix_unitaire', 0.0),
                article_data.get('quantite_stock', 0),
                article_data.get('seuil_alerte', 0),
                article_data.get('categorie_id'),
                article_data.get('fournisseur_id'),
                now,
                now
            ))
            
            self.db_manager.connection.commit()
            return cursor.lastrowid
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout de l'article: {e}")
            return False
    
    def update_article(self, article_id, article_data):
        """Mettre à jour un article"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return False
            
            cursor = self.db_manager.cursor
            if not cursor:
                return False
            
            now = datetime.now().isoformat()
            
            cursor.execute("""
                UPDATE articles 
                SET nom = ?, description = ?, prix_unitaire = ?, quantite_stock = ?,
                    seuil_alerte = ?, categorie_id = ?, fournisseur_id = ?,
                    date_modification = ?
                WHERE id = ?
            """, (
                article_data.get('nom'),
                article_data.get('description'),
                article_data.get('prix_unitaire'),
                article_data.get('quantite_stock'),
                article_data.get('seuil_alerte'),
                article_data.get('categorie_id'),
                article_data.get('fournisseur_id'),
                now,
                article_id
            ))
            
            self.db_manager.connection.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de l'article: {e}")
            return False
    
    def delete_article(self, article_id):
        """Supprimer un article"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return False
            
            cursor = self.db_manager.cursor
            if not cursor:
                return False
            
            cursor.execute("DELETE FROM articles WHERE id = ?", (article_id,))
            self.db_manager.connection.commit()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de l'article: {e}")
            return False
    
    def search_articles(self, search_term):
        """Rechercher des articles"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT a.*, s.nom as supplier_name, c.name as category_name
                FROM articles a
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                LEFT JOIN categories c ON a.categorie_id = c.id
                WHERE a.nom LIKE ? OR a.description LIKE ?
                ORDER BY a.nom
            """, (f"%{search_term}%", f"%{search_term}%"))
            
            articles = cursor.fetchall()
            if articles:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in articles]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la recherche d'articles: {e}")
            return []
    
    def get_low_stock_articles(self, limit=10):
        """Obtenir les articles en stock faible"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT a.*, s.nom as supplier_name, c.name as category_name
                FROM articles a
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                LEFT JOIN categories c ON a.categorie_id = c.id
                WHERE a.quantite_stock <= a.seuil_alerte 
                ORDER BY a.quantite_stock ASC 
                LIMIT ?
            """, (limit,))
            
            articles = cursor.fetchall()
            if articles:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in articles]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles en stock faible: {e}")
            return []
