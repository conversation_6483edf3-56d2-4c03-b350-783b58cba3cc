"""
Dialogue de vente avancé avec calculs automatiques
Conforme au cahier des charges avec gestion du stock
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QPushButton, QLabel, 
    QMessageBox, QFrame, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QDateEdit
)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        Dialog, LineEdit, ComboBox, SpinBox, DoubleSpinBox, PushButton, 
        TitleLabel, BodyLabel, CardWidget, FluentIcon, TableWidget,
        DateEdit
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class SaleDialog(QDialog):
    """
    Dialogue de vente avancé selon les spécifications
    Fonctionnalités: Sélection produits, calculs automatiques, gestion stock
    """

    def __init__(self, parent=None, app_instance=None, sale_data=None):
        super().__init__(parent)
        self.app_instance = app_instance
        self.sale_data = sale_data or {}
        self.logger = setup_logger(__name__)
        
        # Mode édition ou création
        self.is_edit_mode = bool(sale_data)
        
        # Variables d'instance
        self.products_data = []
        self.clients_data = []
        self.sale_items = []
        
        # Initialiser l'interface
        self._init_ui()
        
        # Charger les données
        self._load_data()
        
        # Pré-remplir si mode édition
        if self.is_edit_mode:
            self._populate_fields()
        
        self.logger.info(f"Dialogue vente ouvert ({'édition' if self.is_edit_mode else 'création'})")

    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Configuration de la fenêtre
        title = "Modifier la vente" if self.is_edit_mode else "Nouvelle vente"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(800, 700)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # En-tête
        self._create_header(main_layout)
        
        # Informations de vente
        self._create_sale_info(main_layout)
        
        # Produits de la vente
        self._create_products_section(main_layout)
        
        # Calculs et totaux
        self._create_totals_section(main_layout)
        
        # Boutons d'action
        self._create_buttons(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du dialogue"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Modifier la vente" if self.is_edit_mode else "Nouvelle vente")
            subtitle = BodyLabel("Gérez les produits et calculez automatiquement les totaux")
        else:
            title = QLabel("Modifier la vente" if self.is_edit_mode else "Nouvelle vente")
            title.setFont(QFont("Segoe UI", 16, QFont.Bold))
            subtitle = QLabel("Gérez les produits et calculez automatiquement les totaux")
            subtitle.setFont(QFont("Segoe UI", 10))
        
        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_sale_info(self, layout):
        """Créer la section d'informations de vente"""
        if FLUENT_AVAILABLE:
            info_card = CardWidget()
        else:
            info_card = QFrame()
            info_card.setFrameStyle(QFrame.Box)
        
        form_layout = QFormLayout(info_card)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)
        
        # Date de vente
        if FLUENT_AVAILABLE:
            self.date_input = DateEdit()
        else:
            self.date_input = QDateEdit()
        
        self.date_input.setDate(QDate.currentDate())
        form_layout.addRow("Date de vente:", self.date_input)
        
        # Client
        if FLUENT_AVAILABLE:
            self.client_combo = ComboBox()
        else:
            self.client_combo = QComboBox()
        
        self.client_combo.addItem("Vente sans client", None)
        form_layout.addRow("Client:", self.client_combo)
        
        # Statut
        if FLUENT_AVAILABLE:
            self.status_combo = ComboBox()
        else:
            self.status_combo = QComboBox()
        
        statuses = [
            ("Brouillon", "draft"),
            ("Confirmée", "confirmed"),
            ("Annulée", "cancelled")
        ]
        
        for label, value in statuses:
            self.status_combo.addItem(label, value)
        
        form_layout.addRow("Statut:", self.status_combo)
        
        # Notes
        if FLUENT_AVAILABLE:
            self.notes_input = LineEdit()
        else:
            self.notes_input = QLineEdit()
        
        self.notes_input.setPlaceholderText("Notes sur la vente")
        form_layout.addRow("Notes:", self.notes_input)
        
        layout.addWidget(info_card)

    def _create_products_section(self, layout):
        """Créer la section de gestion des produits"""
        # En-tête de section
        section_label = QLabel("Produits de la vente")
        section_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        layout.addWidget(section_label)
        
        # Barre d'ajout de produit
        add_layout = QHBoxLayout()
        
        # Sélection de produit
        if FLUENT_AVAILABLE:
            self.product_combo = ComboBox()
        else:
            self.product_combo = QComboBox()
        
        self.product_combo.setMinimumWidth(200)
        add_layout.addWidget(QLabel("Produit:"))
        add_layout.addWidget(self.product_combo)
        
        # Quantité
        if FLUENT_AVAILABLE:
            self.quantity_input = SpinBox()
        else:
            self.quantity_input = QSpinBox()
        
        self.quantity_input.setMinimum(1)
        self.quantity_input.setMaximum(1000)
        self.quantity_input.setValue(1)
        add_layout.addWidget(QLabel("Qté:"))
        add_layout.addWidget(self.quantity_input)
        
        # Bouton d'ajout
        if FLUENT_AVAILABLE:
            add_btn = PushButton("Ajouter")
            add_btn.setIcon(FluentIcon.ADD)
        else:
            add_btn = QPushButton("Ajouter")
        
        add_btn.clicked.connect(self._add_product_to_sale)
        add_layout.addWidget(add_btn)
        add_layout.addStretch()
        
        layout.addLayout(add_layout)
        
        # Tableau des produits
        if FLUENT_AVAILABLE:
            self.products_table = TableWidget()
        else:
            self.products_table = QTableWidget()
        
        columns = ["Produit", "Prix unitaire", "Quantité", "Total", "Actions"]
        self.products_table.setColumnCount(len(columns))
        self.products_table.setHorizontalHeaderLabels(columns)
        
        # Configuration du tableau
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        
        # Ajustement des colonnes
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Produit
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Prix
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Quantité
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Total
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Actions
        
        layout.addWidget(self.products_table, 1)

    def _create_totals_section(self, layout):
        """Créer la section des totaux"""
        if FLUENT_AVAILABLE:
            totals_card = CardWidget()
        else:
            totals_card = QFrame()
            totals_card.setFrameStyle(QFrame.Box)
        
        totals_layout = QFormLayout(totals_card)
        totals_layout.setContentsMargins(20, 20, 20, 20)
        
        # Sous-total
        self.subtotal_label = QLabel("0.00 €")
        self.subtotal_label.setFont(QFont("Segoe UI", 11))
        totals_layout.addRow("Sous-total:", self.subtotal_label)
        
        # Remise
        if FLUENT_AVAILABLE:
            self.discount_input = DoubleSpinBox()
        else:
            self.discount_input = QDoubleSpinBox()
        
        self.discount_input.setMinimum(0)
        self.discount_input.setMaximum(100)
        self.discount_input.setSuffix(" %")
        self.discount_input.valueChanged.connect(self._calculate_totals)
        totals_layout.addRow("Remise:", self.discount_input)
        
        # TVA
        if FLUENT_AVAILABLE:
            self.tax_input = DoubleSpinBox()
        else:
            self.tax_input = QDoubleSpinBox()
        
        self.tax_input.setMinimum(0)
        self.tax_input.setMaximum(30)
        self.tax_input.setValue(20)  # TVA par défaut 20%
        self.tax_input.setSuffix(" %")
        self.tax_input.valueChanged.connect(self._calculate_totals)
        totals_layout.addRow("TVA:", self.tax_input)
        
        # Total final
        self.total_label = QLabel("0.00 €")
        self.total_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.total_label.setStyleSheet("color: #0078d4;")
        totals_layout.addRow("TOTAL:", self.total_label)
        
        layout.addWidget(totals_card)

    def _create_buttons(self, layout):
        """Créer les boutons d'action"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.save_btn = PushButton("Enregistrer")
            self.save_btn.setIcon(FluentIcon.SAVE)
            
            self.cancel_btn = PushButton("Annuler")
            self.cancel_btn.setIcon(FluentIcon.CANCEL)
        else:
            self.save_btn = QPushButton("Enregistrer")
            self.cancel_btn = QPushButton("Annuler")
        
        # Connecter les signaux
        self.save_btn.clicked.connect(self._on_save)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)

    def _load_data(self):
        """Charger les données nécessaires"""
        try:
            db_manager = self.app_instance.get_database_manager()
            
            # Charger les produits
            self.products_data = db_manager.get_products()
            self.product_combo.clear()
            self.product_combo.addItem("Sélectionner un produit", None)
            
            for product in self.products_data:
                if product['stock_quantity'] > 0:  # Seulement les produits en stock
                    label = f"{product['name']} - {product['price']}€ (Stock: {product['stock_quantity']})"
                    self.product_combo.addItem(label, product)
            
            # Charger les clients
            self.clients_data = db_manager.get_clients()
            for client in self.clients_data:
                label = f"{client['name']} ({client['loyalty_points']} pts)"
                self.client_combo.addItem(label, client)
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des données: {e}")

    def _add_product_to_sale(self):
        """Ajouter un produit à la vente"""
        try:
            product = self.product_combo.currentData()
            if not product:
                QMessageBox.warning(self, "Attention", "Veuillez sélectionner un produit.")
                return
            
            quantity = self.quantity_input.value()
            
            # Vérifier le stock
            if quantity > product['stock_quantity']:
                QMessageBox.warning(
                    self, 
                    "Stock insuffisant", 
                    f"Stock disponible: {product['stock_quantity']}"
                )
                return
            
            # Vérifier si le produit est déjà dans la vente
            for item in self.sale_items:
                if item['product_id'] == product['id']:
                    # Mettre à jour la quantité
                    new_quantity = item['quantity'] + quantity
                    if new_quantity > product['stock_quantity']:
                        QMessageBox.warning(
                            self, 
                            "Stock insuffisant", 
                            f"Quantité totale dépasserait le stock disponible"
                        )
                        return
                    item['quantity'] = new_quantity
                    item['total_price'] = item['quantity'] * item['unit_price']
                    self._update_products_table()
                    self._calculate_totals()
                    return
            
            # Ajouter le nouveau produit
            item = {
                'product_id': product['id'],
                'product_name': product['name'],
                'unit_price': product['price'],
                'quantity': quantity,
                'total_price': product['price'] * quantity
            }
            
            self.sale_items.append(item)
            self._update_products_table()
            self._calculate_totals()
            
            # Réinitialiser la sélection
            self.product_combo.setCurrentIndex(0)
            self.quantity_input.setValue(1)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout du produit: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout: {e}")

    def _update_products_table(self):
        """Mettre à jour le tableau des produits"""
        self.products_table.setRowCount(len(self.sale_items))
        
        for row, item in enumerate(self.sale_items):
            # Produit
            self.products_table.setItem(row, 0, QTableWidgetItem(item['product_name']))
            
            # Prix unitaire
            self.products_table.setItem(row, 1, QTableWidgetItem(f"{item['unit_price']:.2f} €"))
            
            # Quantité
            self.products_table.setItem(row, 2, QTableWidgetItem(str(item['quantity'])))
            
            # Total
            self.products_table.setItem(row, 3, QTableWidgetItem(f"{item['total_price']:.2f} €"))
            
            # Bouton de suppression
            if FLUENT_AVAILABLE:
                remove_btn = PushButton("Supprimer")
                remove_btn.setIcon(FluentIcon.DELETE)
            else:
                remove_btn = QPushButton("Supprimer")
            
            remove_btn.clicked.connect(lambda checked, r=row: self._remove_product(r))
            self.products_table.setCellWidget(row, 4, remove_btn)

    def _remove_product(self, row):
        """Supprimer un produit de la vente"""
        if 0 <= row < len(self.sale_items):
            del self.sale_items[row]
            self._update_products_table()
            self._calculate_totals()

    def _calculate_totals(self):
        """Calculer les totaux automatiquement"""
        try:
            # Sous-total
            subtotal = sum(item['total_price'] for item in self.sale_items)
            self.subtotal_label.setText(f"{subtotal:.2f} €")
            
            # Remise
            discount_percent = self.discount_input.value()
            discount_amount = subtotal * (discount_percent / 100)
            
            # Montant après remise
            amount_after_discount = subtotal - discount_amount
            
            # TVA
            tax_percent = self.tax_input.value()
            tax_amount = amount_after_discount * (tax_percent / 100)
            
            # Total final
            final_amount = amount_after_discount + tax_amount
            self.total_label.setText(f"{final_amount:.2f} €")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des totaux: {e}")

    def _populate_fields(self):
        """Pré-remplir les champs en mode édition"""
        if not self.sale_data:
            return
        
        # TODO: Implémenter le pré-remplissage pour l'édition
        pass

    def _on_save(self):
        """Gérer la sauvegarde"""
        try:
            # Validation
            if not self._validate_form():
                return
            
            # Accepter le dialogue
            self.accept()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _validate_form(self):
        """Valider le formulaire"""
        if not self.sale_items:
            QMessageBox.warning(self, "Validation", "Veuillez ajouter au moins un produit à la vente.")
            return False
        
        return True

    def get_sale_data(self):
        """Récupérer les données de la vente"""
        # Calculer les totaux
        subtotal = sum(item['total_price'] for item in self.sale_items)
        discount_percent = self.discount_input.value()
        discount_amount = subtotal * (discount_percent / 100)
        amount_after_discount = subtotal - discount_amount
        tax_percent = self.tax_input.value()
        tax_amount = amount_after_discount * (tax_percent / 100)
        final_amount = amount_after_discount + tax_amount
        
        return {
            'client_id': self.client_combo.currentData()['id'] if self.client_combo.currentData() else None,
            'sale_date': self.date_input.date().toString('yyyy-MM-dd'),
            'total_amount': subtotal,
            'discount': discount_amount,
            'tax_amount': tax_amount,
            'final_amount': final_amount,
            'status': self.status_combo.currentData(),
            'notes': self.notes_input.text().strip(),
            'items': self.sale_items.copy()
        }
