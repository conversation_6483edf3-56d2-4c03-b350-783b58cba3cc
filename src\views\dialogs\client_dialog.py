"""
Dialogue de client conforme au cahier des charges
Gestion complète avec points de fidélité
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QTextEdit, QSpinBox, QPushButton, QLabel, QMessageBox, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIntValidator

try:
    from qfluentwidgets import (
        Dialog, LineEdit, TextEdit, SpinBox, PushButton, TitleLabel, 
        BodyLabel, CardWidget, FluentIcon
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class ClientDialog(QDialog):
    """
    Dialogue de client selon les spécifications du cahier des charges
    Champs: Nom (requis), Contact (email, tel), Points de fidélité
    """

    def __init__(self, parent=None, client_data=None):
        super().__init__(parent)
        self.client_data = client_data or {}
        self.logger = setup_logger(__name__)
        
        # Mode édition ou création
        self.is_edit_mode = bool(client_data)
        
        # Initialiser l'interface
        self._init_ui()
        
        # Pré-remplir si mode édition
        if self.is_edit_mode:
            self._populate_fields()
        
        self.logger.info(f"Dialogue client ouvert ({'édition' if self.is_edit_mode else 'création'})")

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        # Configuration de la fenêtre
        title = "Modifier le client" if self.is_edit_mode else "Ajouter un client"
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(500, 600)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # En-tête
        self._create_header(main_layout)
        
        # Formulaire principal
        self._create_form(main_layout)
        
        # Boutons d'action
        self._create_buttons(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du dialogue"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Modifier le client" if self.is_edit_mode else "Nouveau client")
            subtitle = BodyLabel("Modifiez les informations du client" if self.is_edit_mode else "Saisissez les informations du nouveau client")
        else:
            title = QLabel("Modifier le client" if self.is_edit_mode else "Nouveau client")
            title.setFont(QFont("Segoe UI", 16, QFont.Bold))
            subtitle = QLabel("Modifiez les informations du client" if self.is_edit_mode else "Saisissez les informations du nouveau client")
            subtitle.setFont(QFont("Segoe UI", 10))
        
        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_form(self, layout):
        """Créer le formulaire selon les spécifications"""
        if FLUENT_AVAILABLE:
            form_card = CardWidget()
        else:
            form_card = QFrame()
            form_card.setFrameStyle(QFrame.Box)
        
        form_layout = QFormLayout(form_card)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)
        
        # Nom (requis) selon spécifications
        if FLUENT_AVAILABLE:
            self.name_input = LineEdit()
        else:
            self.name_input = QLineEdit()
        
        self.name_input.setPlaceholderText("Nom du client (requis)")
        form_layout.addRow("Nom *:", self.name_input)
        
        # Email
        if FLUENT_AVAILABLE:
            self.email_input = LineEdit()
        else:
            self.email_input = QLineEdit()
        
        self.email_input.setPlaceholderText("<EMAIL>")
        form_layout.addRow("Email:", self.email_input)
        
        # Téléphone
        if FLUENT_AVAILABLE:
            self.phone_input = LineEdit()
        else:
            self.phone_input = QLineEdit()
        
        self.phone_input.setPlaceholderText("Numéro de téléphone")
        form_layout.addRow("Téléphone:", self.phone_input)
        
        # Adresse
        if FLUENT_AVAILABLE:
            self.address_input = TextEdit()
        else:
            self.address_input = QTextEdit()
        
        self.address_input.setPlaceholderText("Adresse complète du client")
        self.address_input.setMaximumHeight(80)
        form_layout.addRow("Adresse:", self.address_input)
        
        # Points de fidélité selon spécifications
        if FLUENT_AVAILABLE:
            self.loyalty_points_input = SpinBox()
        else:
            self.loyalty_points_input = QSpinBox()
        
        self.loyalty_points_input.setMinimum(0)
        self.loyalty_points_input.setMaximum(999999)
        self.loyalty_points_input.setValue(0)
        self.loyalty_points_input.setSuffix(" points")
        form_layout.addRow("Points de fidélité:", self.loyalty_points_input)
        
        # Notes
        if FLUENT_AVAILABLE:
            self.notes_input = TextEdit()
        else:
            self.notes_input = QTextEdit()
        
        self.notes_input.setPlaceholderText("Notes sur le client")
        self.notes_input.setMaximumHeight(80)
        form_layout.addRow("Notes:", self.notes_input)
        
        layout.addWidget(form_card)

    def _create_buttons(self, layout):
        """Créer les boutons d'action"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.save_btn = PushButton("Enregistrer")
            self.save_btn.setIcon(FluentIcon.SAVE)
            
            self.cancel_btn = PushButton("Annuler")
            self.cancel_btn.setIcon(FluentIcon.CANCEL)
        else:
            self.save_btn = QPushButton("Enregistrer")
            self.cancel_btn = QPushButton("Annuler")
        
        # Connecter les signaux
        self.save_btn.clicked.connect(self._on_save)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)

    def _populate_fields(self):
        """Pré-remplir les champs en mode édition"""
        if not self.client_data:
            return
        
        self.name_input.setText(self.client_data.get('name', ''))
        self.email_input.setText(self.client_data.get('email', ''))
        self.phone_input.setText(self.client_data.get('phone', ''))
        self.loyalty_points_input.setValue(int(self.client_data.get('loyalty_points', 0)))
        
        # Adresse
        if hasattr(self.address_input, 'setPlainText'):
            self.address_input.setPlainText(self.client_data.get('address', ''))
        else:
            self.address_input.setText(self.client_data.get('address', ''))
        
        # Notes
        if hasattr(self.notes_input, 'setPlainText'):
            self.notes_input.setPlainText(self.client_data.get('notes', ''))
        else:
            self.notes_input.setText(self.client_data.get('notes', ''))

    def _on_save(self):
        """Gérer la sauvegarde avec validation selon spécifications"""
        try:
            # Validation selon spécifications
            if not self._validate_form():
                return
            
            # Accepter le dialogue
            self.accept()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _validate_form(self):
        """Valider le formulaire selon les spécifications"""
        # Nom requis
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "Validation", "Le nom du client est requis.")
            self.name_input.setFocus()
            return False
        
        # Validation email si fourni
        email = self.email_input.text().strip()
        if email and '@' not in email:
            QMessageBox.warning(self, "Validation", "L'adresse email n'est pas valide.")
            self.email_input.setFocus()
            return False
        
        return True

    def get_client_data(self):
        """Récupérer les données du client depuis le formulaire"""
        address = ""
        if hasattr(self.address_input, 'toPlainText'):
            address = self.address_input.toPlainText()
        else:
            address = self.address_input.text()
        
        notes = ""
        if hasattr(self.notes_input, 'toPlainText'):
            notes = self.notes_input.toPlainText()
        else:
            notes = self.notes_input.text()
        
        return {
            'id': self.client_data.get('id') if self.is_edit_mode else None,
            'name': self.name_input.text().strip(),
            'email': self.email_input.text().strip(),
            'phone': self.phone_input.text().strip(),
            'address': address.strip(),
            'loyalty_points': self.loyalty_points_input.value(),
            'notes': notes.strip()
        }
