"""
Composants UI modernes avec animations et effets visuels
"""

from PyQt5.QtWidgets import (
    QWidget, QLabel, QPushButton, QFrame, QVBoxLayout, QHBoxLayout,
    QGraphicsDropShadowEffect, QGraphicsOpacityEffect, QProgressBar
)
from PyQt5.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve, QTimer, QRect, 
    pyqtSignal, QParallelAnimationGroup, QSequentialAnimationGroup
)
from PyQt5.QtGui import QPainter, QColor, QBrush, QPen, QFont, QPixmap

try:
    from qfluentwidgets import CardWidget as FluentCard, PushButton as FluentButton
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class AnimatedCard(QFrame):
    """
    Carte moderne avec animations de hover et effets d'ombre
    """
    
    clicked = pyqtSignal()
    
    def __init__(self, title: str = "", subtitle: str = "", parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # Configuration de base
        self.setFixedHeight(120)
        self.setFrameStyle(QFrame.Box)
        self.setCursor(Qt.PointingHandCursor)
        
        # Variables d'animation
        self.hover_animation = None
        self.shadow_effect = None
        self.is_hovered = False
        
        # Initialiser l'interface
        self._init_ui(title, subtitle)
        self._setup_animations()
        
    def _init_ui(self, title: str, subtitle: str):
        """Initialiser l'interface de la carte"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(8)
        
        # Titre
        self.title_label = QLabel(title)
        self.title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
        self.title_label.setStyleSheet("color: #323130;")
        
        # Sous-titre
        self.subtitle_label = QLabel(subtitle)
        self.subtitle_label.setFont(QFont("Segoe UI", 10))
        self.subtitle_label.setStyleSheet("color: #605e5c;")
        self.subtitle_label.setWordWrap(True)
        
        layout.addWidget(self.title_label)
        layout.addWidget(self.subtitle_label)
        layout.addStretch()
        
        # Style de base
        self.setStyleSheet("""
            AnimatedCard {
                background-color: white;
                border: 1px solid #e1dfdd;
                border-radius: 8px;
            }
        """)
    
    def _setup_animations(self):
        """Configurer les animations"""
        # Effet d'ombre
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(10)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        self.shadow_effect.setOffset(0, 2)
        self.setGraphicsEffect(self.shadow_effect)
        
        # Animation de hover
        self.hover_animation = QPropertyAnimation(self.shadow_effect, b"blurRadius")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def enterEvent(self, event):
        """Animation d'entrée de la souris"""
        if self.hover_animation and not self.is_hovered:
            self.is_hovered = True
            self.hover_animation.setStartValue(10)
            self.hover_animation.setEndValue(20)
            self.hover_animation.start()
            
            # Changer la couleur de l'ombre
            self.shadow_effect.setColor(QColor(0, 120, 212, 60))
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation de sortie de la souris"""
        if self.hover_animation and self.is_hovered:
            self.is_hovered = False
            self.hover_animation.setStartValue(20)
            self.hover_animation.setEndValue(10)
            self.hover_animation.start()
            
            # Restaurer la couleur de l'ombre
            self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Gestion du clic"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)
    
    def update_content(self, title: str, subtitle: str):
        """Mettre à jour le contenu de la carte"""
        self.title_label.setText(title)
        self.subtitle_label.setText(subtitle)


class ModernProgressBar(QProgressBar):
    """
    Barre de progression moderne avec animations fluides
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # Configuration de base
        self.setTextVisible(False)
        self.setFixedHeight(8)
        
        # Animation de progression
        self.progress_animation = QPropertyAnimation(self, b"value")
        self.progress_animation.setDuration(500)
        self.progress_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Style moderne
        self.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 4px;
                background-color: #f3f2f1;
            }
            QProgressBar::chunk {
                border-radius: 4px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0078d4, stop:1 #00bcf2);
            }
        """)
    
    def animate_to_value(self, target_value: int):
        """Animer vers une valeur cible"""
        self.progress_animation.setStartValue(self.value())
        self.progress_animation.setEndValue(target_value)
        self.progress_animation.start()


class FloatingActionButton(QPushButton):
    """
    Bouton d'action flottant avec animations
    """
    
    def __init__(self, icon_text: str = "+", parent=None):
        super().__init__(icon_text, parent)
        self.logger = setup_logger(__name__)
        
        # Configuration de base
        self.setFixedSize(56, 56)
        self.setCursor(Qt.PointingHandCursor)
        
        # Animations
        self.scale_animation = QPropertyAnimation(self, b"geometry")
        self.scale_animation.setDuration(150)
        self.scale_animation.setEasingCurve(QEasingCurve.OutBack)
        
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        # Style moderne
        self.setStyleSheet("""
            FloatingActionButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 28px;
                font-size: 24px;
                font-weight: bold;
            }
            FloatingActionButton:hover {
                background-color: #106ebe;
            }
        """)
        
        # Effet d'ombre
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 120, 212, 100))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
    
    def enterEvent(self, event):
        """Animation d'entrée"""
        current_rect = self.geometry()
        target_rect = QRect(
            current_rect.x() - 2,
            current_rect.y() - 2,
            current_rect.width() + 4,
            current_rect.height() + 4
        )
        
        self.scale_animation.setStartValue(current_rect)
        self.scale_animation.setEndValue(target_rect)
        self.scale_animation.start()
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation de sortie"""
        current_rect = self.geometry()
        target_rect = QRect(
            current_rect.x() + 2,
            current_rect.y() + 2,
            current_rect.width() - 4,
            current_rect.height() - 4
        )
        
        self.scale_animation.setStartValue(current_rect)
        self.scale_animation.setEndValue(target_rect)
        self.scale_animation.start()
        
        super().leaveEvent(event)


class NotificationToast(QFrame):
    """
    Toast de notification moderne avec auto-disparition
    """
    
    def __init__(self, message: str, notification_type: str = "info", parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # Configuration
        self.setFixedHeight(60)
        self.setFrameStyle(QFrame.Box)
        
        # Couleurs selon le type
        colors = {
            "info": ("#0078d4", "#ffffff"),
            "success": ("#107c10", "#ffffff"),
            "warning": ("#ff8c00", "#ffffff"),
            "error": ("#d13438", "#ffffff")
        }
        
        bg_color, text_color = colors.get(notification_type, colors["info"])
        
        # Interface
        layout = QHBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Icône
        icon_label = QLabel("ℹ️" if notification_type == "info" else
                           "✅" if notification_type == "success" else
                           "⚠️" if notification_type == "warning" else "❌")
        icon_label.setFont(QFont("Segoe UI", 16))
        
        # Message
        message_label = QLabel(message)
        message_label.setFont(QFont("Segoe UI", 11))
        message_label.setWordWrap(True)
        
        layout.addWidget(icon_label)
        layout.addWidget(message_label, 1)
        
        # Style
        self.setStyleSheet(f"""
            NotificationToast {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 8px;
            }}
        """)
        
        # Effet d'ombre
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 50))
        shadow.setOffset(0, 4)
        self.setGraphicsEffect(shadow)
        
        # Animations
        self._setup_animations()
        
        # Auto-disparition après 4 secondes
        QTimer.singleShot(4000, self.hide_animated)
    
    def _setup_animations(self):
        """Configurer les animations"""
        # Animation d'apparition
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.show_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.show_animation.setDuration(300)
        self.show_animation.setStartValue(0.0)
        self.show_animation.setEndValue(1.0)
        self.show_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Animation de disparition
        self.hide_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.hide_animation.setDuration(300)
        self.hide_animation.setStartValue(1.0)
        self.hide_animation.setEndValue(0.0)
        self.hide_animation.setEasingCurve(QEasingCurve.InCubic)
        self.hide_animation.finished.connect(self.deleteLater)
    
    def show_animated(self):
        """Afficher avec animation"""
        self.show()
        self.show_animation.start()
    
    def hide_animated(self):
        """Masquer avec animation"""
        self.hide_animation.start()


class ModernSearchBox(QFrame):
    """
    Boîte de recherche moderne avec suggestions
    """
    
    search_changed = pyqtSignal(str)
    
    def __init__(self, placeholder: str = "Rechercher...", parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        
        # Configuration
        self.setFixedHeight(40)
        self.setFrameStyle(QFrame.NoFrame)
        
        # Interface
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(8)
        
        # Icône de recherche
        search_icon = QLabel("🔍")
        search_icon.setFont(QFont("Segoe UI", 12))
        
        # Champ de recherche
        from PyQt5.QtWidgets import QLineEdit
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText(placeholder)
        self.search_input.setFrame(False)
        self.search_input.textChanged.connect(self.search_changed.emit)
        
        layout.addWidget(search_icon)
        layout.addWidget(self.search_input, 1)
        
        # Style moderne
        self.setStyleSheet("""
            ModernSearchBox {
                background-color: #f9f9f9;
                border: 2px solid #e1dfdd;
                border-radius: 20px;
            }
            ModernSearchBox:focus-within {
                border-color: #0078d4;
                background-color: white;
            }
            QLineEdit {
                background: transparent;
                border: none;
                color: #323130;
                font-size: 14px;
            }
        """)
        
        # Animation de focus
        self.focus_animation = QPropertyAnimation(self, b"geometry")
        self.focus_animation.setDuration(200)
        self.focus_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def focusInEvent(self, event):
        """Animation lors du focus"""
        current_rect = self.geometry()
        target_rect = QRect(
            current_rect.x() - 5,
            current_rect.y(),
            current_rect.width() + 10,
            current_rect.height()
        )
        
        self.focus_animation.setStartValue(current_rect)
        self.focus_animation.setEndValue(target_rect)
        self.focus_animation.start()
        
        super().focusInEvent(event)
    
    def focusOutEvent(self, event):
        """Animation lors de la perte de focus"""
        current_rect = self.geometry()
        target_rect = QRect(
            current_rect.x() + 5,
            current_rect.y(),
            current_rect.width() - 10,
            current_rect.height()
        )
        
        self.focus_animation.setStartValue(current_rect)
        self.focus_animation.setEndValue(target_rect)
        self.focus_animation.start()
        
        super().focusOutEvent(event)


class StatsCard(AnimatedCard):
    """
    Carte de statistiques avec animations de valeurs
    """
    
    def __init__(self, title: str, value: str, trend: str = "", parent=None):
        super().__init__(title, "", parent)
        
        # Reconfigurer le layout pour les stats
        layout = self.layout()
        
        # Supprimer le subtitle existant
        self.subtitle_label.deleteLater()
        
        # Valeur principale
        self.value_label = QLabel(value)
        self.value_label.setFont(QFont("Segoe UI", 24, QFont.Bold))
        self.value_label.setStyleSheet("color: #0078d4;")
        
        # Tendance
        self.trend_label = QLabel(trend)
        self.trend_label.setFont(QFont("Segoe UI", 10))
        self.trend_label.setStyleSheet("color: #107c10;" if "↗" in trend else "color: #d13438;" if "↘" in trend else "color: #605e5c;")
        
        layout.insertWidget(1, self.value_label)
        layout.insertWidget(2, self.trend_label)
    
    def update_value(self, new_value: str, new_trend: str = ""):
        """Mettre à jour la valeur avec animation"""
        # Animation de changement de valeur
        self.value_label.setText(new_value)
        self.trend_label.setText(new_trend)
        
        # Effet de pulsation
        effect = QGraphicsOpacityEffect()
        self.value_label.setGraphicsEffect(effect)
        
        pulse = QPropertyAnimation(effect, b"opacity")
        pulse.setDuration(300)
        pulse.setStartValue(0.5)
        pulse.setEndValue(1.0)
        pulse.setEasingCurve(QEasingCurve.OutCubic)
        pulse.start()
