"""
Dialogue de gestion des points de fidélité
Conforme au cahier des charges
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QSpinBox, QPushButton, QLabel, QMessageBox, QFrame, QRadioButton,
    QButtonGroup
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        Dialog, LineEdit, SpinBox, PushButton, TitleLabel, BodyLabel, 
        CardWidget, FluentIcon, RadioButton
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class LoyaltyPointsDialog(QDialog):
    """
    Dialogue de gestion des points de fidélité
    Permet d'ajouter ou retirer des points avec raison
    """

    def __init__(self, parent=None, client_data=None):
        super().__init__(parent)
        self.client_data = client_data or {}
        self.logger = setup_logger(__name__)
        
        # Initialiser l'interface
        self._init_ui()
        
        self.logger.info(f"Dialogue points de fidélité ouvert pour: {self.client_data.get('name', 'Client inconnu')}")

    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Configuration de la fenêtre
        self.setWindowTitle("Gestion des Points de Fidélité")
        self.setModal(True)
        self.setFixedSize(450, 400)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # En-tête
        self._create_header(main_layout)
        
        # Informations client
        self._create_client_info(main_layout)
        
        # Formulaire de gestion des points
        self._create_points_form(main_layout)
        
        # Boutons d'action
        self._create_buttons(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du dialogue"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Points de Fidélité")
            subtitle = BodyLabel("Ajoutez ou retirez des points de fidélité")
        else:
            title = QLabel("Gestion des Points de Fidélité")
            title.setFont(QFont("Segoe UI", 16, QFont.Bold))
            subtitle = QLabel("Ajoutez ou retirez des points de fidélité")
            subtitle.setFont(QFont("Segoe UI", 10))
        
        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_client_info(self, layout):
        """Créer la section d'informations client"""
        if FLUENT_AVAILABLE:
            info_card = CardWidget()
        else:
            info_card = QFrame()
            info_card.setFrameStyle(QFrame.Box)
        
        info_layout = QVBoxLayout(info_card)
        info_layout.setContentsMargins(15, 15, 15, 15)
        
        # Nom du client
        client_name = QLabel(f"Client: {self.client_data.get('name', 'Inconnu')}")
        client_name.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        # Points actuels
        current_points = self.client_data.get('loyalty_points', 0)
        points_label = QLabel(f"Points actuels: {current_points} points")
        points_label.setFont(QFont("Segoe UI", 11))
        points_label.setStyleSheet("color: #0078d4;")
        
        info_layout.addWidget(client_name)
        info_layout.addWidget(points_label)
        
        layout.addWidget(info_card)

    def _create_points_form(self, layout):
        """Créer le formulaire de gestion des points"""
        if FLUENT_AVAILABLE:
            form_card = CardWidget()
        else:
            form_card = QFrame()
            form_card.setFrameStyle(QFrame.Box)
        
        form_layout = QFormLayout(form_card)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)
        
        # Type d'opération
        operation_layout = QVBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.add_radio = RadioButton("Ajouter des points")
            self.remove_radio = RadioButton("Retirer des points")
        else:
            self.add_radio = QRadioButton("Ajouter des points")
            self.remove_radio = QRadioButton("Retirer des points")
        
        self.add_radio.setChecked(True)  # Par défaut
        
        # Groupe de boutons radio
        self.operation_group = QButtonGroup()
        self.operation_group.addButton(self.add_radio, 1)
        self.operation_group.addButton(self.remove_radio, 2)
        
        operation_layout.addWidget(self.add_radio)
        operation_layout.addWidget(self.remove_radio)
        
        form_layout.addRow("Opération:", operation_layout)
        
        # Nombre de points
        if FLUENT_AVAILABLE:
            self.points_input = SpinBox()
        else:
            self.points_input = QSpinBox()
        
        self.points_input.setMinimum(1)
        self.points_input.setMaximum(10000)
        self.points_input.setValue(10)
        self.points_input.setSuffix(" points")
        form_layout.addRow("Nombre de points:", self.points_input)
        
        # Raison
        if FLUENT_AVAILABLE:
            self.reason_input = LineEdit()
        else:
            self.reason_input = QLineEdit()
        
        self.reason_input.setPlaceholderText("Raison de la modification (optionnel)")
        form_layout.addRow("Raison:", self.reason_input)
        
        layout.addWidget(form_card)

    def _create_buttons(self, layout):
        """Créer les boutons d'action"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.apply_btn = PushButton("Appliquer")
            self.apply_btn.setIcon(FluentIcon.ACCEPT)
            
            self.cancel_btn = PushButton("Annuler")
            self.cancel_btn.setIcon(FluentIcon.CANCEL)
        else:
            self.apply_btn = QPushButton("Appliquer")
            self.cancel_btn = QPushButton("Annuler")
        
        # Connecter les signaux
        self.apply_btn.clicked.connect(self._on_apply)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.apply_btn)
        
        layout.addLayout(buttons_layout)

    def _on_apply(self):
        """Appliquer la modification des points"""
        try:
            # Validation
            if not self._validate_form():
                return
            
            # Accepter le dialogue
            self.accept()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'application: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'application: {e}")

    def _validate_form(self):
        """Valider le formulaire"""
        points = self.points_input.value()
        
        if points <= 0:
            QMessageBox.warning(self, "Validation", "Le nombre de points doit être positif.")
            self.points_input.setFocus()
            return False
        
        # Vérifier qu'on ne retire pas plus de points que disponible
        if self.remove_radio.isChecked():
            current_points = self.client_data.get('loyalty_points', 0)
            if points > current_points:
                QMessageBox.warning(
                    self, 
                    "Validation", 
                    f"Impossible de retirer {points} points.\n"
                    f"Le client n'a que {current_points} points."
                )
                self.points_input.setFocus()
                return False
        
        return True

    def get_points_change(self):
        """Récupérer la modification des points"""
        points = self.points_input.value()
        
        # Si on retire des points, retourner une valeur négative
        if self.remove_radio.isChecked():
            points = -points
        
        reason = self.reason_input.text().strip()
        if not reason:
            if self.add_radio.isChecked():
                reason = "Ajout manuel de points"
            else:
                reason = "Retrait manuel de points"
        
        return {
            'points_change': points,
            'reason': reason
        }
