# Script PowerShell pour activer l'environnement virtuel
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🔧 ACTIVATION ENVIRONNEMENT VIRTUEL" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Vérifier si le dossier venv existe
if (-not (Test-Path "venv")) {
    Write-Host "❌ Le dossier venv n'existe pas" -ForegroundColor Red
    Write-Host "💡 Créez d'abord un environnement virtuel avec:" -ForegroundColor Yellow
    Write-Host "   python -m venv venv" -ForegroundColor White
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

# Vérifier si le script d'activation existe
if (-not (Test-Path "venv\Scripts\Activate.ps1")) {
    Write-Host "❌ Script d'activation PowerShell non trouvé" -ForegroundColor Red
    Write-Host "💡 L'environnement virtuel semble corrompu" -ForegroundColor Yellow
    Read-Host "Appuyez sur Entrée pour continuer"
    exit 1
}

Write-Host "🔄 Activation de l'environnement virtuel..." -ForegroundColor Yellow

try {
    # Activer l'environnement virtuel
    & "venv\Scripts\Activate.ps1"
    
    Write-Host ""
    Write-Host "✅ Environnement virtuel activé !" -ForegroundColor Green
    Write-Host "📋 Vous êtes maintenant dans l'environnement virtuel" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🚀 Commandes disponibles:" -ForegroundColor Cyan
    Write-Host "   python main.py          - Lancer GSlim" -ForegroundColor White
    Write-Host "   pip list                - Voir les packages installés" -ForegroundColor White
    Write-Host "   deactivate              - Désactiver l'environnement" -ForegroundColor White
    Write-Host ""
}
catch {
    Write-Host "❌ Erreur lors de l'activation: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Solutions possibles:" -ForegroundColor Yellow
    Write-Host "   1. Exécuter en tant qu'administrateur" -ForegroundColor White
    Write-Host "   2. Modifier la politique d'exécution:" -ForegroundColor White
    Write-Host "      Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Gray
    Write-Host "   3. Utiliser le fichier .bat à la place" -ForegroundColor White
    Write-Host ""
    Read-Host "Appuyez sur Entrée pour continuer"
}
