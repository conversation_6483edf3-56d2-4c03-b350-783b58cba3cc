"""
Sélecteur de Thèmes Intégré pour GSlim
Interface pour choisir et prévisualiser tous les thèmes disponibles
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QButtonGroup, QRadioButton, QGroupBox, QComboBox,
    QDialog, QDialogButtonBox, QTextEdit, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette, QPixmap, QPainter

from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode


class ThemePreviewCard(QFrame):
    """Carte de prévisualisation d'un thème"""
    
    theme_selected = pyqtSignal(str, str)  # theme_type, theme_mode
    
    def __init__(self, theme_type: ThemeType, theme_mode: ThemeMode, parent=None):
        super().__init__(parent)
        self.theme_type = theme_type
        self.theme_mode = theme_mode
        self.theme_manager = get_theme_manager()
        self.is_selected = False
        
        self.setup_ui()
        self.setup_preview()
    
    def setup_ui(self):
        """Configurer l'interface de la carte"""
        self.setFixedSize(280, 200)
        self.setCursor(Qt.PointingHandCursor)
        self.setFrameStyle(QFrame.Box)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # En-tête avec nom et icône
        header_layout = QHBoxLayout()
        
        theme_info = self.theme_manager.get_theme_info(self.theme_type)
        
        # Icône du thème
        icon_label = QLabel(theme_info.get("icon", "🎨"))
        icon_label.setStyleSheet("font-size: 24px;")
        header_layout.addWidget(icon_label)
        
        # Nom du thème
        name_label = QLabel(theme_info.get("name", self.theme_type.value.title()))
        name_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        header_layout.addWidget(name_label)
        
        header_layout.addStretch()
        
        # Mode (clair/sombre)
        mode_label = QLabel("🌙" if self.theme_mode == ThemeMode.DARK else "☀️")
        mode_label.setStyleSheet("font-size: 16px;")
        header_layout.addWidget(mode_label)
        
        layout.addLayout(header_layout)
        
        # Description
        desc_label = QLabel(theme_info.get("description", ""))
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(desc_label)
        
        # Zone de prévisualisation
        self.preview_area = QFrame()
        self.preview_area.setMinimumHeight(80)
        self.preview_area.setFrameStyle(QFrame.StyledPanel)
        layout.addWidget(self.preview_area)
        
        # Fonctionnalités
        features = theme_info.get("features", [])
        if features:
            features_text = " • ".join(features[:3])  # Limiter à 3 fonctionnalités
            features_label = QLabel(features_text)
            features_label.setStyleSheet("color: #888; font-size: 10px;")
            features_label.setWordWrap(True)
            layout.addWidget(features_label)
        
        # Vérifier la disponibilité
        if not self.theme_manager.is_theme_available(self.theme_type):
            unavailable_label = QLabel("⚠️ Nécessite: " + theme_info.get("requires", ""))
            unavailable_label.setStyleSheet("color: #ff6b6b; font-size: 10px;")
            layout.addWidget(unavailable_label)
            self.setEnabled(False)
        
        self.setLayout(layout)
    
    def setup_preview(self):
        """Configurer la prévisualisation du thème"""
        # Obtenir les couleurs du thème
        colors = self.theme_manager.get_theme_preview_colors(self.theme_type, self.theme_mode)
        
        # Appliquer un style de prévisualisation
        preview_style = f"""
        QFrame {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {colors.get('background', '#ffffff')},
                stop:1 {colors.get('primary', '#0078d4')});
            border-radius: 8px;
        }}
        """
        self.preview_area.setStyleSheet(preview_style)
        
        # Ajouter des éléments de prévisualisation
        preview_layout = QVBoxLayout(self.preview_area)
        preview_layout.setContentsMargins(8, 8, 8, 8)
        
        # Bouton de prévisualisation
        preview_btn = QPushButton("Aperçu")
        preview_btn.setMaximumWidth(80)
        preview_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {colors.get('primary', '#0078d4')};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 10px;
            }}
        """)
        preview_layout.addWidget(preview_btn)
        
        # Texte de prévisualisation
        preview_text = QLabel("Exemple de texte")
        preview_text.setStyleSheet(f"color: {colors.get('text', '#000000')}; font-size: 10px;")
        preview_layout.addWidget(preview_text)
    
    def mousePressEvent(self, event):
        """Gérer la sélection du thème"""
        if event.button() == Qt.LeftButton and self.isEnabled():
            self.theme_selected.emit(self.theme_type.value, self.theme_mode.value)
            self.set_selected(True)
        super().mousePressEvent(event)
    
    def set_selected(self, selected: bool):
        """Définir l'état de sélection"""
        self.is_selected = selected
        if selected:
            self.setStyleSheet("""
                QFrame {
                    border: 2px solid #0078d4;
                    background-color: rgba(0, 120, 212, 0.1);
                }
            """)
        else:
            self.setStyleSheet("""
                QFrame {
                    border: 1px solid #cccccc;
                    background-color: transparent;
                }
                QFrame:hover {
                    border: 1px solid #0078d4;
                    background-color: rgba(0, 120, 212, 0.05);
                }
            """)


class ThemeSelector(QWidget):
    """Sélecteur de thèmes principal"""
    
    theme_applied = pyqtSignal(str, str)  # theme_type, theme_mode
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.theme_cards = []
        self.current_selection = None
        
        self.setup_ui()
        self.load_current_theme()
    
    def setup_ui(self):
        """Configurer l'interface du sélecteur"""
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Titre
        title_label = QLabel("🎨 Sélecteur de Thèmes")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel("Choisissez le thème et le mode qui vous conviennent le mieux")
        desc_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(desc_label)
        
        # Zone de sélection de mode
        mode_group = QGroupBox("Mode d'affichage")
        mode_layout = QHBoxLayout()
        
        self.mode_group = QButtonGroup()
        
        self.dark_radio = QRadioButton("🌙 Sombre")
        self.light_radio = QRadioButton("☀️ Clair")
        
        self.mode_group.addButton(self.dark_radio, 0)
        self.mode_group.addButton(self.light_radio, 1)
        
        mode_layout.addWidget(self.dark_radio)
        mode_layout.addWidget(self.light_radio)
        mode_layout.addStretch()
        
        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)
        
        # Zone de cartes de thèmes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        themes_widget = QWidget()
        self.themes_layout = QGridLayout(themes_widget)
        self.themes_layout.setSpacing(16)
        
        self.create_theme_cards()
        
        scroll_area.setWidget(themes_widget)
        layout.addWidget(scroll_area)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("👁️ Prévisualiser")
        self.preview_btn.clicked.connect(self.preview_theme)
        self.preview_btn.setEnabled(False)
        buttons_layout.addWidget(self.preview_btn)
        
        self.apply_btn = QPushButton("✅ Appliquer")
        self.apply_btn.clicked.connect(self.apply_theme)
        self.apply_btn.setEnabled(False)
        buttons_layout.addWidget(self.apply_btn)
        
        buttons_layout.addStretch()
        
        self.reset_btn = QPushButton("🔄 Réinitialiser")
        self.reset_btn.clicked.connect(self.reset_theme)
        buttons_layout.addWidget(self.reset_btn)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # Connecter les signaux
        self.mode_group.buttonClicked.connect(self.on_mode_changed)
    
    def create_theme_cards(self):
        """Créer les cartes de thèmes"""
        themes = [ThemeType.CLASSIC, ThemeType.MODERN, ThemeType.PROFESSIONAL, 
                 ThemeType.FLUENT, ThemeType.CYBERPUNK]
        
        row = 0
        col = 0
        
        for theme_type in themes:
            # Mode sombre
            dark_card = ThemePreviewCard(theme_type, ThemeMode.DARK)
            dark_card.theme_selected.connect(self.on_theme_selected)
            self.themes_layout.addWidget(dark_card, row, col)
            self.theme_cards.append(dark_card)
            
            col += 1
            
            # Mode clair
            light_card = ThemePreviewCard(theme_type, ThemeMode.LIGHT)
            light_card.theme_selected.connect(self.on_theme_selected)
            self.themes_layout.addWidget(light_card, row, col)
            self.theme_cards.append(light_card)
            
            col += 1
            
            # Nouvelle ligne après 2 thèmes (4 cartes)
            if col >= 4:
                col = 0
                row += 1
    
    def load_current_theme(self):
        """Charger le thème actuel"""
        current_theme, current_mode = self.theme_manager.get_current_theme()
        
        # Sélectionner le mode approprié
        if current_mode == ThemeMode.DARK:
            self.dark_radio.setChecked(True)
        else:
            self.light_radio.setChecked(True)
        
        # Sélectionner la carte appropriée
        for card in self.theme_cards:
            if card.theme_type == current_theme and card.theme_mode == current_mode:
                card.set_selected(True)
                self.current_selection = card
                break
    
    def on_mode_changed(self):
        """Gérer le changement de mode"""
        # Désélectionner toutes les cartes
        for card in self.theme_cards:
            card.set_selected(False)
        
        self.current_selection = None
        self.preview_btn.setEnabled(False)
        self.apply_btn.setEnabled(False)
    
    def on_theme_selected(self, theme_type: str, theme_mode: str):
        """Gérer la sélection d'un thème"""
        # Désélectionner les autres cartes
        for card in self.theme_cards:
            if not (card.theme_type.value == theme_type and card.theme_mode.value == theme_mode):
                card.set_selected(False)
        
        # Trouver la carte sélectionnée
        for card in self.theme_cards:
            if card.theme_type.value == theme_type and card.theme_mode.value == theme_mode:
                self.current_selection = card
                break
        
        # Mettre à jour le mode radio
        if theme_mode == "dark":
            self.dark_radio.setChecked(True)
        else:
            self.light_radio.setChecked(True)
        
        # Activer les boutons
        self.preview_btn.setEnabled(True)
        self.apply_btn.setEnabled(True)
    
    def preview_theme(self):
        """Prévisualiser le thème sélectionné"""
        if self.current_selection:
            # Appliquer temporairement le thème
            self.theme_manager.set_theme(
                self.current_selection.theme_type,
                self.current_selection.theme_mode
            )
            
            # Programmer un retour au thème précédent après 5 secondes
            QTimer.singleShot(5000, self.load_current_theme)
    
    def apply_theme(self):
        """Appliquer le thème sélectionné"""
        if self.current_selection:
            success = self.theme_manager.set_theme(
                self.current_selection.theme_type,
                self.current_selection.theme_mode
            )
            
            if success:
                self.theme_applied.emit(
                    self.current_selection.theme_type.value,
                    self.current_selection.theme_mode.value
                )
    
    def reset_theme(self):
        """Réinitialiser au thème par défaut"""
        self.theme_manager.reset_to_default()
        self.load_current_theme()


class ThemeSelectorDialog(QDialog):
    """Dialog pour la sélection de thèmes"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Sélecteur de Thèmes - GSlim")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout()
        
        # Sélecteur de thèmes
        self.theme_selector = ThemeSelector()
        self.theme_selector.theme_applied.connect(self.accept)
        layout.addWidget(self.theme_selector)
        
        # Boutons de dialog
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)


def show_theme_selector(parent=None):
    """Afficher le sélecteur de thèmes"""
    dialog = ThemeSelectorDialog(parent)
    return dialog.exec_() == QDialog.Accepted
