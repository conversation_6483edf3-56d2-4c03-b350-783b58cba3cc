#!/usr/bin/env python3
"""
Test Rapide d'Intégration - GSlim
Vérification simple que tous les thèmes sont intégrés
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_integration():
    """Test basique d'intégration"""
    print("🧪 Test Rapide d'Intégration GSlim")
    print("="*40)
    
    # Test 1: Vérifier les fichiers
    print("\n📁 Vérification des fichiers...")
    files_to_check = [
        "src/styles/theme_manager.py",
        "src/styles/futuristic_theme.py",
        "src/widgets/theme_selector.py",
        "src/views/cyberpunk_dashboard.py"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - MANQUANT")
    
    # Test 2: Import du gestionnaire de thèmes
    print("\n🎨 Test du gestionnaire de thèmes...")
    try:
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        theme_manager = get_theme_manager()
        print("   ✅ Gestionnaire de thèmes importé")
        
        # Test des thèmes disponibles
        themes = theme_manager.get_available_themes()
        print(f"   ✅ {len(themes)} thèmes disponibles:")
        for theme_type, info in themes.items():
            print(f"      {info['icon']} {info['name']}")
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 3: Test du thème cyberpunk
    print("\n🚀 Test du thème cyberpunk...")
    try:
        from styles.futuristic_theme import FuturisticTheme
        stylesheet = FuturisticTheme.get_complete_futuristic_theme()
        if len(stylesheet) > 1000:
            print(f"   ✅ Thème cyberpunk: {len(stylesheet)} caractères")
        else:
            print("   ❌ Thème cyberpunk trop court")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    # Test 4: Test des composants
    print("\n🧩 Test des composants...")
    try:
        from widgets.cyberpunk_components import CyberpunkStatCard
        print("   ✅ Composants cyberpunk importés")
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
    
    print("\n🎉 Test terminé !")
    print("\n🚀 Pour tester l'interface complète:")
    print("   python demo_launcher.py")

if __name__ == "__main__":
    test_basic_integration()
