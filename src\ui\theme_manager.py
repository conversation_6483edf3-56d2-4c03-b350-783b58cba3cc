"""
Gestionnaire de thèmes avancé avec animations et styles modernes
Support pour thèmes personnalisés et transitions fluides
"""

import json
import os
from typing import Dict, Any, Optional
from PyQt5.QtCore import QObject, pyqtSignal, QPropertyAnimation, QEasingCurve, QTimer
from PyQt5.QtWidgets import QWidget, QGraphicsOpacityEffect
from PyQt5.QtGui import QPalette, QColor

try:
    from qfluentwidgets import Theme, setTheme, qconfig, ConfigItem, OptionsConfigItem
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class ModernThemeManager(QObject):
    """
    Gestionnaire de thèmes moderne avec animations et personnalisation
    """
    
    theme_changed = pyqtSignal(str)  # Signal émis lors du changement de thème
    animation_finished = pyqtSignal()  # Signal émis à la fin d'une animation
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__)
        self.current_theme = "light"
        self.custom_themes = {}
        self.animations = []
        
        # Charger les thèmes personnalisés
        self._load_custom_themes()
        
        self.logger.info("Gestionnaire de thèmes moderne initialisé")

    def _load_custom_themes(self):
        """Charger les thèmes personnalisés depuis les fichiers"""
        themes_dir = os.path.join("src", "ui", "themes")
        if not os.path.exists(themes_dir):
            os.makedirs(themes_dir)
            self._create_default_themes()
        
        try:
            for filename in os.listdir(themes_dir):
                if filename.endswith('.json'):
                    theme_name = filename[:-5]  # Enlever .json
                    with open(os.path.join(themes_dir, filename), 'r', encoding='utf-8') as f:
                        self.custom_themes[theme_name] = json.load(f)
            
            self.logger.info(f"{len(self.custom_themes)} thèmes personnalisés chargés")
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des thèmes: {e}")

    def _create_default_themes(self):
        """Créer les thèmes par défaut"""
        themes_dir = os.path.join("src", "ui", "themes")
        
        # Thème sombre moderne
        dark_modern = {
            "name": "Dark Modern",
            "type": "dark",
            "colors": {
                "primary": "#0078d4",
                "secondary": "#106ebe",
                "accent": "#00bcf2",
                "background": "#1e1e1e",
                "surface": "#2d2d30",
                "card": "#3c3c3c",
                "text_primary": "#ffffff",
                "text_secondary": "#cccccc",
                "border": "#484848",
                "success": "#107c10",
                "warning": "#ff8c00",
                "error": "#d13438",
                "info": "#0078d4"
            },
            "effects": {
                "blur_radius": 10,
                "shadow_opacity": 0.3,
                "border_radius": 8,
                "animation_duration": 200
            }
        }
        
        # Thème clair moderne
        light_modern = {
            "name": "Light Modern",
            "type": "light",
            "colors": {
                "primary": "#0078d4",
                "secondary": "#106ebe",
                "accent": "#00bcf2",
                "background": "#ffffff",
                "surface": "#f9f9f9",
                "card": "#ffffff",
                "text_primary": "#323130",
                "text_secondary": "#605e5c",
                "border": "#e1dfdd",
                "success": "#107c10",
                "warning": "#ff8c00",
                "error": "#d13438",
                "info": "#0078d4"
            },
            "effects": {
                "blur_radius": 10,
                "shadow_opacity": 0.1,
                "border_radius": 8,
                "animation_duration": 200
            }
        }
        
        # Thème bleu professionnel
        blue_professional = {
            "name": "Blue Professional",
            "type": "light",
            "colors": {
                "primary": "#003f7f",
                "secondary": "#0052a3",
                "accent": "#0078d4",
                "background": "#f8f9fa",
                "surface": "#ffffff",
                "card": "#ffffff",
                "text_primary": "#212529",
                "text_secondary": "#6c757d",
                "border": "#dee2e6",
                "success": "#28a745",
                "warning": "#ffc107",
                "error": "#dc3545",
                "info": "#17a2b8"
            },
            "effects": {
                "blur_radius": 12,
                "shadow_opacity": 0.15,
                "border_radius": 6,
                "animation_duration": 250
            }
        }
        
        # Sauvegarder les thèmes
        themes = {
            "dark_modern": dark_modern,
            "light_modern": light_modern,
            "blue_professional": blue_professional
        }
        
        for theme_name, theme_data in themes.items():
            with open(os.path.join(themes_dir, f"{theme_name}.json"), 'w', encoding='utf-8') as f:
                json.dump(theme_data, f, indent=2, ensure_ascii=False)

    def get_available_themes(self) -> Dict[str, str]:
        """Obtenir la liste des thèmes disponibles"""
        themes = {
            "light": "Clair (Système)",
            "dark": "Sombre (Système)",
            "auto": "Automatique (Système)"
        }
        
        # Ajouter les thèmes personnalisés
        for theme_name, theme_data in self.custom_themes.items():
            themes[theme_name] = theme_data.get("name", theme_name)
        
        return themes

    def apply_theme(self, theme_name: str, widget: Optional[QWidget] = None, animate: bool = True):
        """Appliquer un thème avec animation optionnelle"""
        try:
            if animate and widget:
                self._animate_theme_change(widget, lambda: self._apply_theme_internal(theme_name))
            else:
                self._apply_theme_internal(theme_name)
            
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)
            
            self.logger.info(f"Thème appliqué: {theme_name}")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'application du thème: {e}")

    def _apply_theme_internal(self, theme_name: str):
        """Application interne du thème"""
        if theme_name in ["light", "dark", "auto"]:
            # Thèmes système Fluent
            if FLUENT_AVAILABLE:
                if theme_name == "light":
                    setTheme(Theme.LIGHT)
                elif theme_name == "dark":
                    setTheme(Theme.DARK)
                else:  # auto
                    setTheme(Theme.AUTO)
        
        elif theme_name in self.custom_themes:
            # Thème personnalisé
            theme_data = self.custom_themes[theme_name]
            self._apply_custom_theme(theme_data)

    def _apply_custom_theme(self, theme_data: Dict[str, Any]):
        """Appliquer un thème personnalisé"""
        try:
            colors = theme_data.get("colors", {})
            effects = theme_data.get("effects", {})
            
            # Créer le CSS personnalisé
            css = self._generate_custom_css(colors, effects)
            
            # Appliquer le CSS à l'application
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                app.setStyleSheet(css)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'application du thème personnalisé: {e}")

    def _generate_custom_css(self, colors: Dict[str, str], effects: Dict[str, Any]) -> str:
        """Générer le CSS personnalisé à partir des couleurs et effets"""
        border_radius = effects.get("border_radius", 8)
        animation_duration = effects.get("animation_duration", 200)
        
        css = f"""
        /* Styles globaux */
        QWidget {{
            background-color: {colors.get('background', '#ffffff')};
            color: {colors.get('text_primary', '#000000')};
            font-family: 'Segoe UI', Arial, sans-serif;
        }}
        
        /* Cartes et surfaces */
        QFrame[class="card"], CardWidget {{
            background-color: {colors.get('card', '#ffffff')};
            border: 1px solid {colors.get('border', '#e0e0e0')};
            border-radius: {border_radius}px;
            padding: 16px;
        }}
        
        /* Boutons */
        QPushButton, PushButton {{
            background-color: {colors.get('primary', '#0078d4')};
            color: white;
            border: none;
            border-radius: {border_radius}px;
            padding: 8px 16px;
            font-weight: 600;
            transition: all {animation_duration}ms ease;
        }}
        
        QPushButton:hover, PushButton:hover {{
            background-color: {colors.get('secondary', '#106ebe')};
            transform: translateY(-1px);
        }}
        
        QPushButton:pressed, PushButton:pressed {{
            background-color: {colors.get('secondary', '#106ebe')};
            transform: translateY(0px);
        }}
        
        /* Champs de saisie */
        QLineEdit, LineEdit, QTextEdit, TextEdit {{
            background-color: {colors.get('surface', '#f9f9f9')};
            border: 2px solid {colors.get('border', '#e0e0e0')};
            border-radius: {border_radius}px;
            padding: 8px 12px;
            color: {colors.get('text_primary', '#000000')};
        }}
        
        QLineEdit:focus, LineEdit:focus, QTextEdit:focus, TextEdit:focus {{
            border-color: {colors.get('primary', '#0078d4')};
            background-color: {colors.get('card', '#ffffff')};
        }}
        
        /* Tableaux */
        QTableWidget, TableWidget {{
            background-color: {colors.get('card', '#ffffff')};
            alternate-background-color: {colors.get('surface', '#f9f9f9')};
            gridline-color: {colors.get('border', '#e0e0e0')};
            border: 1px solid {colors.get('border', '#e0e0e0')};
            border-radius: {border_radius}px;
        }}
        
        QHeaderView::section {{
            background-color: {colors.get('surface', '#f9f9f9')};
            color: {colors.get('text_primary', '#000000')};
            border: none;
            border-bottom: 2px solid {colors.get('primary', '#0078d4')};
            padding: 12px 8px;
            font-weight: 600;
        }}
        
        /* ComboBox */
        QComboBox, ComboBox {{
            background-color: {colors.get('surface', '#f9f9f9')};
            border: 2px solid {colors.get('border', '#e0e0e0')};
            border-radius: {border_radius}px;
            padding: 8px 12px;
            color: {colors.get('text_primary', '#000000')};
        }}
        
        /* Labels */
        QLabel {{
            color: {colors.get('text_primary', '#000000')};
        }}
        
        QLabel[class="secondary"] {{
            color: {colors.get('text_secondary', '#666666')};
        }}
        
        /* Messages de statut */
        QLabel[class="success"] {{
            color: {colors.get('success', '#107c10')};
            font-weight: 600;
        }}
        
        QLabel[class="warning"] {{
            color: {colors.get('warning', '#ff8c00')};
            font-weight: 600;
        }}
        
        QLabel[class="error"] {{
            color: {colors.get('error', '#d13438')};
            font-weight: 600;
        }}
        
        /* Animations */
        * {{
            transition: all {animation_duration}ms ease;
        }}
        """
        
        return css

    def _animate_theme_change(self, widget: QWidget, apply_function):
        """Animer le changement de thème"""
        try:
            # Effet de fondu
            effect = QGraphicsOpacityEffect()
            widget.setGraphicsEffect(effect)
            
            # Animation de fondu sortant
            self.fade_out = QPropertyAnimation(effect, b"opacity")
            self.fade_out.setDuration(150)
            self.fade_out.setStartValue(1.0)
            self.fade_out.setEndValue(0.3)
            self.fade_out.setEasingCurve(QEasingCurve.OutCubic)
            
            # Animation de fondu entrant
            self.fade_in = QPropertyAnimation(effect, b"opacity")
            self.fade_in.setDuration(150)
            self.fade_in.setStartValue(0.3)
            self.fade_in.setEndValue(1.0)
            self.fade_in.setEasingCurve(QEasingCurve.InCubic)
            
            # Connecter les animations
            self.fade_out.finished.connect(lambda: self._on_fade_out_finished(apply_function))
            self.fade_in.finished.connect(self._on_animation_finished)
            
            # Démarrer l'animation
            self.fade_out.start()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'animation: {e}")
            # Fallback sans animation
            apply_function()

    def _on_fade_out_finished(self, apply_function):
        """Callback à la fin du fondu sortant"""
        try:
            # Appliquer le nouveau thème
            apply_function()
            
            # Démarrer le fondu entrant
            QTimer.singleShot(50, self.fade_in.start)
            
        except Exception as e:
            self.logger.error(f"Erreur lors du callback d'animation: {e}")

    def _on_animation_finished(self):
        """Callback à la fin de l'animation complète"""
        self.animation_finished.emit()

    def get_current_theme_colors(self) -> Dict[str, str]:
        """Obtenir les couleurs du thème actuel"""
        if self.current_theme in self.custom_themes:
            return self.custom_themes[self.current_theme].get("colors", {})
        else:
            # Couleurs par défaut pour les thèmes système
            if self.current_theme == "dark":
                return {
                    "primary": "#0078d4",
                    "background": "#1e1e1e",
                    "surface": "#2d2d30",
                    "text_primary": "#ffffff"
                }
            else:
                return {
                    "primary": "#0078d4",
                    "background": "#ffffff",
                    "surface": "#f9f9f9",
                    "text_primary": "#323130"
                }

    def create_gradient_background(self, colors: list, direction: str = "vertical") -> str:
        """Créer un arrière-plan dégradé"""
        if direction == "vertical":
            gradient = f"qlineargradient(x1:0, y1:0, x2:0, y2:1"
        else:  # horizontal
            gradient = f"qlineargradient(x1:0, y1:0, x2:1, y2:0"
        
        for i, color in enumerate(colors):
            stop = i / (len(colors) - 1) if len(colors) > 1 else 0
            gradient += f", stop:{stop} {color}"
        
        gradient += ")"
        return gradient

    def get_theme_info(self, theme_name: str) -> Dict[str, Any]:
        """Obtenir les informations d'un thème"""
        if theme_name in self.custom_themes:
            return self.custom_themes[theme_name]
        else:
            return {
                "name": theme_name.title(),
                "type": "system",
                "colors": self.get_current_theme_colors()
            }


# Instance globale du gestionnaire de thèmes
theme_manager = ModernThemeManager()
