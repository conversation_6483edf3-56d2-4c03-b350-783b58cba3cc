"""
GSlim Cyberpunk Dashboard - Interface Futuriste
Design sci-fi avec néons, hologrammes et animations cyberpunk
"""

import sys
import random
import math
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QGraphicsDropShadowEffect, QGraphicsOpacityEffect,
    QSizePolicy, QSpacerItem
)
from PyQt5.QtCore import (
    Qt, QTimer, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect,
    QParallelAnimationGroup, QSequentialAnimationGroup, QVariantAnimation
)
from PyQt5.QtGui import (
    QFont, QPainter, QColor, QLinearGradient, QRadialGradient, QPen, QBrush,
    QPainterPath, QPixmap, QPalette
)

try:
    from qfluentwidgets import FluentIcon
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from styles.futuristic_theme import FuturisticTheme


class CyberpunkStatCard(QFrame):
    """Carte de statistique cyberpunk avec effets néon"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title: str, value: str, subtitle: str = "", 
                 card_type: str = "primary", icon: str = "⚡", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.card_type = card_type
        self.icon = icon
        self.glow_intensity = 0.5
        
        self.setup_ui()
        self.setup_animations()
        self.setup_effects()
    
    def setup_ui(self):
        """Configurer l'interface cyberpunk"""
        self.setProperty("class", f"neon-{self.card_type}")
        self.setFixedSize(320, 200)
        self.setCursor(Qt.PointingHandCursor)
        
        # Layout principal
        layout = QVBoxLayout()
        layout.setContentsMargins(25, 20, 25, 20)
        layout.setSpacing(15)
        
        # Header avec icône holographique
        header_layout = QHBoxLayout()
        
        # Icône néon
        icon_label = QLabel(self.icon)
        icon_label.setProperty("class", "cyber-caption")
        icon_label.setStyleSheet(f"""
            font-size: 32px;
            color: {FuturisticTheme.NEON_CYAN};
            text-shadow: 0 0 15px {FuturisticTheme.NEON_CYAN};
        """)
        header_layout.addWidget(icon_label)
        
        header_layout.addStretch()
        
        # Indicateur de statut
        status_dot = QLabel("●")
        status_dot.setStyleSheet(f"""
            color: {FuturisticTheme.NEON_GREEN};
            font-size: 16px;
            text-shadow: 0 0 10px {FuturisticTheme.NEON_GREEN};
        """)
        header_layout.addWidget(status_dot)
        
        layout.addLayout(header_layout)
        
        # Valeur principale avec effet hologramme
        self.value_label = QLabel(self.value)
        self.value_label.setProperty("class", "neon-value")
        layout.addWidget(self.value_label)
        
        # Titre avec effet cyber
        title_label = QLabel(self.title)
        title_label.setProperty("class", "neon-label")
        layout.addWidget(title_label)
        
        # Sous-titre (optionnel)
        if self.subtitle:
            subtitle_label = QLabel(self.subtitle)
            subtitle_label.setProperty("class", "cyber-caption")
            layout.addWidget(subtitle_label)
        
        # Ligne d'énergie décorative
        energy_line = QFrame()
        energy_line.setFrameShape(QFrame.HLine)
        energy_line.setStyleSheet(f"""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 transparent,
                stop:0.2 {FuturisticTheme.NEON_CYAN},
                stop:0.8 {FuturisticTheme.NEON_CYAN},
                stop:1 transparent);
            height: 2px;
            border: none;
        """)
        layout.addWidget(energy_line)
        
        self.setLayout(layout)
    
    def setup_animations(self):
        """Configurer les animations cyberpunk"""
        # Animation de lévitation
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(400)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Animation de pulsation néon
        self.glow_animation = QVariantAnimation()
        self.glow_animation.setDuration(2000)
        self.glow_animation.setStartValue(0.3)
        self.glow_animation.setEndValue(1.0)
        self.glow_animation.setLoopCount(-1)
        self.glow_animation.valueChanged.connect(self.update_glow)
        self.glow_animation.start()
        
        # Animation de rotation de l'icône
        self.icon_rotation = QVariantAnimation()
        self.icon_rotation.setDuration(10000)
        self.icon_rotation.setStartValue(0)
        self.icon_rotation.setEndValue(360)
        self.icon_rotation.setLoopCount(-1)
        
        # Animation de changement de valeur
        self.value_animation = QSequentialAnimationGroup()
    
    def setup_effects(self):
        """Configurer les effets visuels"""
        # Effet d'ombre néon
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(30)
        self.shadow_effect.setColor(QColor(FuturisticTheme.NEON_CYAN))
        self.shadow_effect.setOffset(0, 0)
        self.setGraphicsEffect(self.shadow_effect)
    
    def update_glow(self, value):
        """Mettre à jour l'intensité du néon"""
        self.glow_intensity = value
        color = QColor(FuturisticTheme.NEON_CYAN)
        color.setAlphaF(value)
        self.shadow_effect.setColor(color)
    
    def enterEvent(self, event):
        """Animation d'entrée cyberpunk"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() - 8,
            current_rect.width(),
            current_rect.height()
        )
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(new_rect)
        self.hover_animation.start()
        
        # Changer la couleur du néon
        self.shadow_effect.setColor(QColor(FuturisticTheme.NEON_PINK))
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation de sortie cyberpunk"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() + 8,
            current_rect.width(),
            current_rect.height()
        )
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(new_rect)
        self.hover_animation.start()
        
        # Restaurer la couleur du néon
        self.shadow_effect.setColor(QColor(FuturisticTheme.NEON_CYAN))
        
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Effet de clic cyberpunk"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
            # Flash néon
            self.shadow_effect.setColor(QColor(FuturisticTheme.NEON_GREEN))
            QTimer.singleShot(150, lambda: self.shadow_effect.setColor(QColor(FuturisticTheme.NEON_CYAN)))
        
        super().mousePressEvent(event)
    
    def update_value(self, new_value: str, animate: bool = True):
        """Mettre à jour la valeur avec effet holographique"""
        if animate:
            self.animate_value_change(new_value)
        else:
            self.value = new_value
            self.value_label.setText(new_value)
    
    def animate_value_change(self, new_value: str):
        """Animation de changement de valeur"""
        # Effet de scintillement
        opacity_effect = QGraphicsOpacityEffect()
        self.value_label.setGraphicsEffect(opacity_effect)
        
        fade_out = QPropertyAnimation(opacity_effect, b"opacity")
        fade_out.setDuration(200)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.2)
        
        fade_in = QPropertyAnimation(opacity_effect, b"opacity")
        fade_in.setDuration(200)
        fade_in.setStartValue(0.2)
        fade_in.setEndValue(1.0)
        
        self.value_animation = QSequentialAnimationGroup()
        self.value_animation.addAnimation(fade_out)
        self.value_animation.addAnimation(fade_in)
        
        fade_out.finished.connect(lambda: self.value_label.setText(new_value))
        self.value_animation.start()


class CyberpunkButton(QPushButton):
    """Bouton cyberpunk avec effets néon"""
    
    def __init__(self, text: str, button_type: str = "cyber-primary", icon: str = "", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.icon_text = icon
        self.is_loading = False
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface du bouton"""
        self.setProperty("class", self.button_type)
        self.setMinimumHeight(45)
        self.setMinimumWidth(120)
        
        if self.icon_text:
            self.setText(f"{self.icon_text} {self.text()}")
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de pulsation pour le loading
        self.pulse_animation = QVariantAnimation()
        self.pulse_animation.setDuration(1000)
        self.pulse_animation.setStartValue(0.7)
        self.pulse_animation.setEndValue(1.0)
        self.pulse_animation.setLoopCount(-1)
        self.pulse_animation.valueChanged.connect(self.update_pulse)
        
        # Animation de clic
        self.click_animation = QPropertyAnimation(self, b"geometry")
        self.click_animation.setDuration(100)
        self.click_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def update_pulse(self, value):
        """Mettre à jour la pulsation"""
        if self.is_loading:
            self.setStyleSheet(f"""
                QPushButton {{
                    opacity: {value};
                }}
            """)
    
    def set_loading(self, loading: bool):
        """Activer l'état de chargement cyberpunk"""
        self.is_loading = loading
        
        if loading:
            self.setEnabled(False)
            original_text = self.text()
            self.setText("⚡ PROCESSING...")
            self.pulse_animation.start()
            
            # Restaurer après 3 secondes
            QTimer.singleShot(3000, lambda: self.restore_button(original_text))
        else:
            self.setEnabled(True)
            self.pulse_animation.stop()
            self.setStyleSheet("")
    
    def restore_button(self, original_text: str):
        """Restaurer l'état normal"""
        self.setText(original_text)
        self.set_loading(False)


class CyberpunkDashboard(QMainWindow):
    """Tableau de bord cyberpunk futuriste"""
    
    def __init__(self):
        super().__init__()
        self.setup_window()
        self.setup_ui()
        self.setup_background_effects()
        self.setup_data_timer()
        
        # Appliquer le thème futuriste
        self.setStyleSheet(FuturisticTheme.get_complete_futuristic_theme())
    
    def setup_window(self):
        """Configurer la fenêtre cyberpunk"""
        self.setWindowTitle("🚀 GSlim v1.0.0 - CYBERPUNK INTERFACE")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Palette de couleurs cyberpunk
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(FuturisticTheme.DARK_VOID))
        palette.setColor(QPalette.WindowText, QColor(FuturisticTheme.HOLOGRAM_WHITE))
        self.setPalette(palette)
    
    def setup_ui(self):
        """Configurer l'interface cyberpunk"""
        central_widget = QWidget()
        central_widget.setProperty("class", "holo-container")
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(30, 25, 30, 25)
        main_layout.setSpacing(25)
        
        # Header cyberpunk
        self.create_cyber_header(main_layout)
        
        # Grille de cartes néon
        self.create_neon_stats_grid(main_layout)
        
        # Panel de contrôle
        self.create_control_panel(main_layout)
        
        # Zone de données en temps réel
        self.create_realtime_data_zone(main_layout)
        
        central_widget.setLayout(main_layout)
    
    def create_cyber_header(self, layout):
        """Créer l'en-tête cyberpunk"""
        header_frame = QFrame()
        header_frame.setProperty("class", "cyber-card")
        header_frame.setFixedHeight(120)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # Titre principal avec effet hologramme
        title_layout = QVBoxLayout()
        
        main_title = QLabel("GSLIM CYBERPUNK")
        main_title.setProperty("class", "cyber-title")
        title_layout.addWidget(main_title)
        
        subtitle = QLabel("QUANTUM INVENTORY SYSTEM")
        subtitle.setProperty("class", "cyber-subtitle")
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Contrôles système
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)
        
        # Bouton mode quantique
        quantum_btn = CyberpunkButton("⚛️ QUANTUM", "cyber-primary")
        quantum_btn.clicked.connect(self.toggle_quantum_mode)
        controls_layout.addWidget(quantum_btn)
        
        # Bouton neural link
        neural_btn = CyberpunkButton("🧠 NEURAL", "cyber-success")
        neural_btn.clicked.connect(self.activate_neural_link)
        controls_layout.addWidget(neural_btn)
        
        # Bouton déconnexion
        logout_btn = CyberpunkButton("⚡ LOGOUT", "cyber-danger")
        logout_btn.clicked.connect(self.cyber_logout)
        controls_layout.addWidget(logout_btn)
        
        header_layout.addLayout(controls_layout)
        
        # Status système
        status_layout = QVBoxLayout()
        
        status_label = QLabel("SYSTEM STATUS")
        status_label.setProperty("class", "cyber-caption")
        status_layout.addWidget(status_label)
        
        online_label = QLabel("🟢 ONLINE")
        online_label.setStyleSheet(f"""
            color: {FuturisticTheme.NEON_GREEN};
            font-weight: bold;
            text-shadow: 0 0 10px {FuturisticTheme.NEON_GREEN};
        """)
        status_layout.addWidget(online_label)
        
        header_layout.addLayout(status_layout)
        
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
    
    def create_neon_stats_grid(self, layout):
        """Créer la grille de statistiques néon"""
        stats_layout = QGridLayout()
        stats_layout.setSpacing(20)
        
        # Données cyberpunk
        stats_data = [
            ("QUANTUM ITEMS", "1,337", "⚛️ PARTICLES", "primary"),
            ("NEURAL SALES", "42,069", "🧠 SYNAPSES", "success"),
            ("CRYPTO REVENUE", "₿ 13.37", "💎 BLOCKCHAIN", "info"),
            ("CYBER ALERTS", "7", "⚠️ ANOMALIES", "warning"),
            ("MATRIX USERS", "256", "👥 CONNECTED", "primary"),
            ("DATA STREAMS", "∞", "📡 FLOWING", "success")
        ]
        
        self.stat_cards = []
        for i, (title, value, subtitle, card_type) in enumerate(stats_data):
            # Icônes cyberpunk
            icons = ["⚛️", "🧠", "💎", "⚠️", "👥", "📡"]
            icon = icons[i % len(icons)]
            
            card = CyberpunkStatCard(title, value, subtitle, card_type, icon)
            card.clicked.connect(lambda t=title: self.on_cyber_card_clicked(t))
            self.stat_cards.append(card)
            
            row = i // 3
            col = i % 3
            stats_layout.addWidget(card, row, col)
        
        layout.addLayout(stats_layout)
    
    def create_control_panel(self, layout):
        """Créer le panel de contrôle cyberpunk"""
        control_frame = QFrame()
        control_frame.setProperty("class", "cyber-card")
        control_frame.setFixedHeight(100)
        
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(25, 15, 25, 15)
        control_layout.setSpacing(20)
        
        # Boutons de contrôle
        controls = [
            ("🔄 SYNC DATA", "cyber-primary", self.sync_quantum_data),
            ("📊 HOLO REPORT", "cyber-success", self.generate_holo_report),
            ("⚡ BOOST SYSTEM", "cyber-warning", self.boost_system),
            ("🛡️ FIREWALL", "cyber-danger", self.activate_firewall)
        ]
        
        for text, btn_type, callback in controls:
            btn = CyberpunkButton(text, btn_type)
            btn.clicked.connect(callback)
            control_layout.addWidget(btn)
        
        control_layout.addStretch()
        
        # Indicateur d'énergie
        energy_layout = QVBoxLayout()
        
        energy_label = QLabel("ENERGY LEVEL")
        energy_label.setProperty("class", "cyber-caption")
        energy_layout.addWidget(energy_label)
        
        energy_bar = QFrame()
        energy_bar.setFixedHeight(8)
        energy_bar.setStyleSheet(f"""
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 {FuturisticTheme.NEON_GREEN},
                stop:0.7 {FuturisticTheme.NEON_YELLOW},
                stop:1 {FuturisticTheme.NEON_ORANGE});
            border-radius: 4px;
            box-shadow: 0 0 10px {FuturisticTheme.NEON_GREEN};
        """)
        energy_layout.addWidget(energy_bar)
        
        control_layout.addLayout(energy_layout)
        
        control_frame.setLayout(control_layout)
        layout.addWidget(control_frame)
    
    def create_realtime_data_zone(self, layout):
        """Créer la zone de données en temps réel"""
        data_frame = QFrame()
        data_frame.setProperty("class", "cyber-card")
        data_frame.setMinimumHeight(250)
        
        data_layout = QVBoxLayout()
        data_layout.setContentsMargins(25, 20, 25, 20)
        
        # Titre de la zone
        data_title = QLabel("REAL-TIME QUANTUM DATA STREAM")
        data_title.setProperty("class", "cyber-subtitle")
        data_layout.addWidget(data_title)
        
        # Simulation de flux de données
        self.data_display = QLabel("🌊 INITIALIZING QUANTUM STREAM...")
        self.data_display.setProperty("class", "cyber-body")
        self.data_display.setWordWrap(True)
        data_layout.addWidget(self.data_display)
        
        data_frame.setLayout(data_layout)
        layout.addWidget(data_frame)
    
    def setup_background_effects(self):
        """Configurer les effets d'arrière-plan"""
        # Timer pour les effets de scintillement
        self.flicker_timer = QTimer()
        self.flicker_timer.timeout.connect(self.create_flicker_effect)
        self.flicker_timer.start(3000)  # Toutes les 3 secondes
    
    def setup_data_timer(self):
        """Configurer le timer de données"""
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self.update_quantum_data)
        self.data_timer.start(2000)  # Toutes les 2 secondes
    
    def create_flicker_effect(self):
        """Créer un effet de scintillement aléatoire"""
        if self.stat_cards:
            card = random.choice(self.stat_cards)
            original_color = card.shadow_effect.color()
            
            # Flash coloré aléatoire
            colors = [
                FuturisticTheme.NEON_CYAN,
                FuturisticTheme.NEON_PINK,
                FuturisticTheme.NEON_GREEN,
                FuturisticTheme.NEON_PURPLE
            ]
            flash_color = QColor(random.choice(colors))
            
            card.shadow_effect.setColor(flash_color)
            QTimer.singleShot(200, lambda: card.shadow_effect.setColor(original_color))
    
    def update_quantum_data(self):
        """Mettre à jour les données quantiques"""
        # Simuler des données en temps réel
        quantum_data = [
            "⚛️ Quantum flux: STABLE",
            "🌊 Data stream: 1.21 GW",
            "🔮 Neural patterns: SYNCHRONIZED",
            "⚡ Energy levels: OPTIMAL",
            "🛡️ Security status: ENCRYPTED",
            "🚀 System performance: 99.7%"
        ]
        
        current_data = random.choice(quantum_data)
        timestamp = QTimer().remainingTime()
        
        self.data_display.setText(f"[{timestamp:04d}] {current_data}")
        
        # Mettre à jour une carte aléatoirement
        if self.stat_cards and random.random() < 0.3:  # 30% de chance
            card = random.choice(self.stat_cards)
            if "₿" in card.value:
                new_value = f"₿ {random.uniform(10, 20):.2f}"
            elif "∞" in card.value:
                new_value = "∞"
            else:
                current_val = int(card.value.replace(",", "")) if card.value.replace(",", "").isdigit() else 1000
                new_value = f"{current_val + random.randint(-50, 100):,}"
            
            card.update_value(new_value, animate=True)
    
    def toggle_quantum_mode(self):
        """Basculer le mode quantique"""
        self.show_cyber_message("🌀 QUANTUM MODE", "Entering quantum superposition...")
    
    def activate_neural_link(self):
        """Activer le lien neural"""
        self.show_cyber_message("🧠 NEURAL LINK", "Establishing neural connection...")
    
    def sync_quantum_data(self):
        """Synchroniser les données quantiques"""
        btn = self.sender()
        if isinstance(btn, CyberpunkButton):
            btn.set_loading(True)
        self.show_cyber_message("🔄 SYNC", "Quantum data synchronized")
    
    def generate_holo_report(self):
        """Générer un rapport holographique"""
        self.show_cyber_message("📊 HOLO REPORT", "Generating holographic report...")
    
    def boost_system(self):
        """Booster le système"""
        self.show_cyber_message("⚡ BOOST", "System performance enhanced!")
    
    def activate_firewall(self):
        """Activer le firewall"""
        self.show_cyber_message("🛡️ FIREWALL", "Quantum firewall activated")
    
    def cyber_logout(self):
        """Déconnexion cyberpunk"""
        self.show_cyber_message("⚡ LOGOUT", "Disconnecting from the matrix...")
        QTimer.singleShot(3000, self.close)
    
    def on_cyber_card_clicked(self, title):
        """Gérer le clic sur une carte cyberpunk"""
        self.show_cyber_message("🎯 ACCESS", f"Accessing {title} module...")
    
    def show_cyber_message(self, title: str, message: str):
        """Afficher un message cyberpunk"""
        print(f"💬 {title}: {message}")
        
        # Effet visuel sur l'affichage de données
        self.data_display.setText(f"[SYSTEM] {title} - {message}")
        
        # Flash de l'interface
        for card in self.stat_cards:
            card.shadow_effect.setColor(QColor(FuturisticTheme.NEON_GREEN))
        
        QTimer.singleShot(500, self.restore_card_colors)
    
    def restore_card_colors(self):
        """Restaurer les couleurs des cartes"""
        for card in self.stat_cards:
            card.shadow_effect.setColor(QColor(FuturisticTheme.NEON_CYAN))


def main():
    """Fonction principale cyberpunk"""
    app = QApplication(sys.argv)
    
    # Configuration cyberpunk
    app.setApplicationName("GSlim Cyberpunk")
    app.setApplicationVersion("2077.1.0")
    
    # Police futuriste
    font = QFont("Consolas", 10)
    font.setStyleHint(QFont.Monospace)
    app.setFont(font)
    
    # Créer et afficher l'interface cyberpunk
    dashboard = CyberpunkDashboard()
    dashboard.show()
    
    print("🚀 CYBERPUNK INTERFACE ACTIVATED")
    print("⚡ Welcome to the future of inventory management!")
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
