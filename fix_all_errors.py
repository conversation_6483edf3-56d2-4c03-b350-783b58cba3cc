#!/usr/bin/env python3
"""
Script de Correction Complète - GSlim
Corrige toutes les erreurs identifiées dans le projet
"""

import sys
import os
import shutil
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def fix_database_manager():
    """Corriger les erreurs du DatabaseManager"""
    print("🔧 Correction du DatabaseManager...")
    
    db_manager_file = "src/database/database_manager.py"
    
    if not os.path.exists(db_manager_file):
        print(f"❌ Fichier {db_manager_file} non trouvé")
        return False
    
    # Lire le contenu actuel
    with open(db_manager_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter la propriété cursor manquante
    cursor_property = '''
    @property
    def cursor(self):
        """Obtenir un curseur de base de données"""
        if self.connection:
            return self.connection.cursor()
        return None
'''
    
    # Ajouter la méthode si elle n'existe pas
    if "@property" not in content or "def cursor" not in content:
        # Trouver la fin de la classe DatabaseManager
        lines = content.split('\n')
        insert_index = -1
        
        for i, line in enumerate(lines):
            if "class DatabaseManager" in line:
                # Trouver la fin de la classe
                for j in range(i + 1, len(lines)):
                    if lines[j].strip() and not lines[j].startswith('    ') and not lines[j].startswith('\t'):
                        insert_index = j
                        break
                break
        
        if insert_index > 0:
            lines.insert(insert_index, cursor_property)
            content = '\n'.join(lines)
            print("✅ Propriété cursor ajoutée")
    
    # Écrire le fichier corrigé
    with open(db_manager_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True


def fix_orders_window():
    """Corriger les erreurs dans OrdersWindow"""
    print("🔧 Correction d'OrdersWindow...")
    
    orders_file = "src/views/orders_window.py"
    
    if not os.path.exists(orders_file):
        print(f"❌ Fichier {orders_file} non trouvé")
        return False
    
    # Lire le contenu actuel
    with open(orders_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Ajouter la méthode _reset_filters manquante
    reset_filters_method = '''
    def _reset_filters(self):
        """Réinitialiser les filtres"""
        try:
            if hasattr(self, 'status_filter'):
                self.status_filter.setCurrentIndex(0)
            if hasattr(self, 'date_filter'):
                self.date_filter.setCurrentIndex(0)
            if hasattr(self, 'search_input'):
                self.search_input.clear()
            
            # Recharger les données
            self.load_orders()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la réinitialisation des filtres: {e}")
'''
    
    # Ajouter la méthode si elle n'existe pas
    if "def _reset_filters" not in content:
        # Trouver la fin de la classe
        lines = content.split('\n')
        insert_index = -1
        
        for i, line in enumerate(lines):
            if "class OrdersWindow" in line:
                # Trouver la fin de la classe
                for j in range(i + 1, len(lines)):
                    if lines[j].strip() and not lines[j].startswith('    ') and not lines[j].startswith('\t'):
                        insert_index = j
                        break
                if insert_index == -1:
                    insert_index = len(lines)
                break
        
        if insert_index > 0:
            lines.insert(insert_index, reset_filters_method)
            content = '\n'.join(lines)
            print("✅ Méthode _reset_filters ajoutée")
    
    # Écrire le fichier corrigé
    with open(orders_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True


def fix_fluent_icons():
    """Corriger les erreurs d'icônes Fluent"""
    print("🔧 Correction des icônes Fluent...")
    
    reports_file = "src/views/reports_window.py"
    
    if not os.path.exists(reports_file):
        print(f"❌ Fichier {reports_file} non trouvé")
        return False
    
    # Lire le contenu actuel
    with open(reports_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Remplacer les icônes problématiques
    icon_replacements = {
        "FluentIcon.PIE": "FluentIcon.CHART",
        "FluentIcon.BAR_CHART": "FluentIcon.CHART",
        "FluentIcon.LINE_CHART": "FluentIcon.CHART",
        "FluentIcon.AREA_CHART": "FluentIcon.CHART"
    }
    
    for old_icon, new_icon in icon_replacements.items():
        if old_icon in content:
            content = content.replace(old_icon, new_icon)
            print(f"✅ Remplacé {old_icon} par {new_icon}")
    
    # Écrire le fichier corrigé
    with open(reports_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True


def create_missing_controllers():
    """Créer les contrôleurs manquants"""
    print("🔧 Création des contrôleurs manquants...")
    
    # Vérifier si le répertoire controllers existe
    controllers_dir = "src/controllers"
    if not os.path.exists(controllers_dir):
        os.makedirs(controllers_dir)
        print("✅ Répertoire controllers créé")
    
    # Créer article_controller.py s'il n'existe pas
    article_controller_file = f"{controllers_dir}/article_controller.py"
    if not os.path.exists(article_controller_file):
        article_controller_content = '''"""
Contrôleur pour la gestion des articles
"""

from utils.logger import setup_logger


class ArticleController:
    """Contrôleur pour la gestion des articles"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
    
    def get_all_articles(self):
        """Récupérer tous les articles"""
        try:
            if not self.db_manager or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("SELECT * FROM articles")
            articles = cursor.fetchall()
            
            # Convertir en liste de dictionnaires
            columns = [desc[0] for desc in cursor.description]
            return [dict(zip(columns, row)) for row in articles]
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles: {e}")
            return []
    
    def create_article(self, article_data):
        """Créer un nouvel article"""
        try:
            if not self.db_manager or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Insérer l'article
            query = """
                INSERT INTO articles (nom, description, prix_unitaire, quantite_stock, seuil_alerte, categorie, fournisseur)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            cursor.execute(query, (
                article_data.get('nom'),
                article_data.get('description'),
                article_data.get('prix_unitaire'),
                article_data.get('quantite_stock'),
                article_data.get('seuil_alerte'),
                article_data.get('categorie'),
                article_data.get('fournisseur')
            ))
            
            self.db_manager.connection.commit()
            return cursor.lastrowid
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de l'article: {e}")
            raise
    
    def update_article(self, article_id, article_data):
        """Mettre à jour un article"""
        try:
            if not self.db_manager or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Mettre à jour l'article
            query = """
                UPDATE articles 
                SET nom=?, description=?, prix_unitaire=?, quantite_stock=?, seuil_alerte=?, categorie=?, fournisseur=?
                WHERE id=?
            """
            
            cursor.execute(query, (
                article_data.get('nom'),
                article_data.get('description'),
                article_data.get('prix_unitaire'),
                article_data.get('quantite_stock'),
                article_data.get('seuil_alerte'),
                article_data.get('categorie'),
                article_data.get('fournisseur'),
                article_id
            ))
            
            self.db_manager.connection.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de l'article: {e}")
            raise
    
    def delete_article(self, article_id):
        """Supprimer un article"""
        try:
            if not self.db_manager or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            cursor.execute("DELETE FROM articles WHERE id=?", (article_id,))
            self.db_manager.connection.commit()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de l'article: {e}")
            raise
'''
        
        with open(article_controller_file, 'w', encoding='utf-8') as f:
            f.write(article_controller_content)
        
        print("✅ ArticleController créé")
    
    return True


def fix_css_properties():
    """Corriger les propriétés CSS non supportées"""
    print("🔧 Correction des propriétés CSS...")
    
    # Fichiers contenant des styles CSS
    style_files = [
        "src/styles/modern_theme.py",
        "src/styles/professional_theme.py",
        "src/styles/fluent_theme.py",
        "src/styles/futuristic_theme.py"
    ]
    
    for file_path in style_files:
        if not os.path.exists(file_path):
            continue
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remplacer les propriétés CSS non supportées par PyQt5
        css_replacements = {
            "box-shadow:": "/* box-shadow:",
            "transform:": "/* transform:",
            "transition:": "/* transition:",
            "backdrop-filter:": "/* backdrop-filter:",
            "filter:": "/* filter:"
        }
        
        modified = False
        for old_prop, new_prop in css_replacements.items():
            if old_prop in content and not new_prop in content:
                content = content.replace(old_prop, new_prop)
                modified = True
        
        if modified:
            # Fermer les commentaires
            content = content.replace("/* box-shadow:", "/* box-shadow:").replace(";", "; */")
            content = content.replace("/* transform:", "/* transform:").replace(";", "; */")
            content = content.replace("/* transition:", "/* transition:").replace(";", "; */")
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Propriétés CSS corrigées dans {file_path}")
    
    return True


def create_error_handler():
    """Créer un gestionnaire d'erreurs global"""
    print("🔧 Création du gestionnaire d'erreurs...")
    
    error_handler_content = '''"""
Gestionnaire d'erreurs global pour GSlim
"""

import sys
import traceback
from PyQt5.QtWidgets import QMessageBox, QApplication
from utils.logger import setup_logger


class ErrorHandler:
    """Gestionnaire d'erreurs global"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """Gérer les exceptions non capturées"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Logger l'erreur
        error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        self.logger.error(f"Exception non capturée: {error_msg}")
        
        # Afficher un message à l'utilisateur
        app = QApplication.instance()
        if app:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("Erreur")
            msg_box.setText("Une erreur inattendue s'est produite.")
            msg_box.setDetailedText(error_msg)
            msg_box.exec_()
    
    def install(self):
        """Installer le gestionnaire d'erreurs"""
        sys.excepthook = self.handle_exception


# Instance globale
error_handler = ErrorHandler()


def install_error_handler():
    """Installer le gestionnaire d'erreurs global"""
    error_handler.install()
'''
    
    error_handler_file = "src/utils/error_handler.py"
    with open(error_handler_file, 'w', encoding='utf-8') as f:
        f.write(error_handler_content)
    
    print("✅ Gestionnaire d'erreurs créé")
    return True


def create_test_runner():
    """Créer un système de tests"""
    print("🔧 Création du système de tests...")
    
    test_runner_content = '''#!/usr/bin/env python3
"""
Système de Tests pour GSlim
Tests automatisés pour valider toutes les fonctionnalités
"""

import sys
import os
import unittest
from PyQt5.QtWidgets import QApplication

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


class TestImports(unittest.TestCase):
    """Tests des imports"""
    
    def test_theme_manager_import(self):
        """Tester l'import du gestionnaire de thèmes"""
        try:
            from styles.theme_manager import get_theme_manager
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Import du gestionnaire de thèmes échoué: {e}")
    
    def test_widgets_import(self):
        """Tester l'import des widgets"""
        try:
            from widgets.enhanced_widgets import EnhancedCard
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Import des widgets échoué: {e}")
    
    def test_views_import(self):
        """Tester l'import des vues"""
        try:
            from views.integrated_dashboard import IntegratedDashboard
            self.assertTrue(True)
        except ImportError as e:
            self.fail(f"Import des vues échoué: {e}")


class TestThemeManager(unittest.TestCase):
    """Tests du gestionnaire de thèmes"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_theme_manager_creation(self):
        """Tester la création du gestionnaire de thèmes"""
        from styles.theme_manager import get_theme_manager
        theme_manager = get_theme_manager()
        self.assertIsNotNone(theme_manager)
    
    def test_available_themes(self):
        """Tester les thèmes disponibles"""
        from styles.theme_manager import get_theme_manager
        theme_manager = get_theme_manager()
        themes = theme_manager.get_available_themes()
        self.assertGreater(len(themes), 0)


class TestWidgets(unittest.TestCase):
    """Tests des widgets"""
    
    def setUp(self):
        """Configuration des tests"""
        self.app = QApplication.instance()
        if self.app is None:
            self.app = QApplication([])
    
    def test_enhanced_card_creation(self):
        """Tester la création d'une carte améliorée"""
        from widgets.enhanced_widgets import EnhancedCard
        card = EnhancedCard("Test", None)
        self.assertIsNotNone(card)
        self.assertEqual(card.title, "Test")


def run_tests():
    """Exécuter tous les tests"""
    print("🧪 SYSTÈME DE TESTS GSLIM")
    print("="*40)
    
    # Créer la suite de tests
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Ajouter les tests
    suite.addTests(loader.loadTestsFromTestCase(TestImports))
    suite.addTests(loader.loadTestsFromTestCase(TestThemeManager))
    suite.addTests(loader.loadTestsFromTestCase(TestWidgets))
    
    # Exécuter les tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Résumé
    print("\\n" + "="*40)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*40)
    print(f"Tests exécutés: {result.testsRun}")
    print(f"Échecs: {len(result.failures)}")
    print(f"Erreurs: {len(result.errors)}")
    
    if result.wasSuccessful():
        print("\\n🎉 TOUS LES TESTS RÉUSSIS !")
        return True
    else:
        print("\\n❌ Certains tests ont échoué")
        return False


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
'''
    
    test_runner_file = "test_runner.py"
    with open(test_runner_file, 'w', encoding='utf-8') as f:
        f.write(test_runner_content)
    
    print("✅ Système de tests créé")
    return True


def run_complete_fix():
    """Exécuter toutes les corrections"""
    print("🔧 CORRECTION COMPLÈTE DES ERREURS - GSLIM")
    print("="*60)
    
    fixes = [
        ("Correction du DatabaseManager", fix_database_manager),
        ("Correction d'OrdersWindow", fix_orders_window),
        ("Correction des icônes Fluent", fix_fluent_icons),
        ("Création des contrôleurs manquants", create_missing_controllers),
        ("Correction des propriétés CSS", fix_css_properties),
        ("Création du gestionnaire d'erreurs", create_error_handler),
        ("Création du système de tests", create_test_runner)
    ]
    
    success_count = 0
    for fix_name, fix_func in fixes:
        print(f"\\n📋 {fix_name}...")
        try:
            result = fix_func()
            if result is not False:
                success_count += 1
                print(f"✅ {fix_name} terminé")
            else:
                print(f"❌ {fix_name} échoué")
        except Exception as e:
            print(f"❌ Erreur dans {fix_name}: {e}")
    
    print("\\n" + "="*60)
    print("📊 RÉSUMÉ DES CORRECTIONS")
    print("="*60)
    print(f"Corrections réussies: {success_count}/{len(fixes)}")
    
    if success_count == len(fixes):
        print("\\n🎉 TOUTES LES CORRECTIONS RÉUSSIES !")
        print("\\n🔧 Corrections appliquées:")
        print("   ✅ DatabaseManager.cursor ajouté")
        print("   ✅ OrdersWindow._reset_filters ajouté")
        print("   ✅ Icônes Fluent corrigées")
        print("   ✅ ArticleController créé")
        print("   ✅ Propriétés CSS commentées")
        print("   ✅ Gestionnaire d'erreurs installé")
        print("   ✅ Système de tests créé")
        
        print("\\n📋 Prochaines étapes:")
        print("1. Tester: python test_runner.py")
        print("2. Lancer: python launch_enhanced.py")
        print("3. Vérifier: Toutes les fonctionnalités")
        
        print("\\n✨ Votre application est maintenant corrigée !")
    else:
        print("\\n⚠️  Corrections partielles. Vérifiez les erreurs ci-dessus.")


def main():
    """Fonction principale"""
    try:
        run_complete_fix()
        return 0
    except KeyboardInterrupt:
        print("\\n⏹️  Corrections interrompues par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\\n💥 Erreur critique: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
