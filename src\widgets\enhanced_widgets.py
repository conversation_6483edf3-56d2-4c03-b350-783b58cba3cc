"""
Widgets Améliorés pour GSlim
Composants modernes et réutilisables pour tous les modules
"""

import sys
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit, QTextEdit,
    QProgressBar, QSlider, QCheckBox, QRadioButton, QGroupBox,
    QScrollArea, QSplitter, QTabWidget, QTreeWidget, QTreeWidgetItem,
    QGraphicsDropShadowEffect, QGraphicsOpacityEffect, QMessageBox
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QTimer,
    QDate, QDateTime, QVariantAnimation, QParallelAnimationGroup
)
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPainter, QLinearGradient

from styles.theme_manager import get_theme_manager, ThemeType

try:
    from qfluentwidgets import (
        CardWidget, PushButton, LineEdit, ComboBox, SpinBox,
        TableWidget, TreeWidget, ProgressBar, InfoBadge, FluentIcon
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False


class EnhancedCard(QFrame):
    """Carte améliorée avec animations et effets"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title: str = "", content_widget: QWidget = None, parent=None):
        super().__init__(parent)
        self.title = title
        self.content_widget = content_widget
        self.is_expanded = True
        
        self.setup_ui()
        self.setup_animations()
        self.apply_theme_style()
    
    def setup_ui(self):
        """Configurer l'interface de la carte"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setCursor(Qt.PointingHandCursor)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)
        
        # En-tête avec titre
        if self.title:
            header_layout = QHBoxLayout()
            
            title_label = QLabel(self.title)
            title_label.setFont(QFont("Segoe UI", 14, QFont.Bold))
            header_layout.addWidget(title_label)
            
            header_layout.addStretch()
            
            # Bouton de réduction/expansion
            self.toggle_btn = QPushButton("−")
            self.toggle_btn.setFixedSize(24, 24)
            self.toggle_btn.clicked.connect(self.toggle_content)
            header_layout.addWidget(self.toggle_btn)
            
            layout.addLayout(header_layout)
        
        # Zone de contenu
        if self.content_widget:
            self.content_area = QFrame()
            content_layout = QVBoxLayout(self.content_area)
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.addWidget(self.content_widget)
            layout.addWidget(self.content_area)
        
        self.setLayout(layout)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de survol
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Animation d'expansion/réduction
        if hasattr(self, 'content_area'):
            self.expand_animation = QPropertyAnimation(self.content_area, b"maximumHeight")
            self.expand_animation.setDuration(300)
            self.expand_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def apply_theme_style(self):
        """Appliquer le style selon le thème actuel"""
        theme_manager = get_theme_manager()
        current_theme, current_mode = theme_manager.get_current_theme()
        
        if current_theme == ThemeType.CYBERPUNK:
            self.setProperty("class", "cyber-card")
        elif current_theme == ThemeType.FLUENT and FLUENT_AVAILABLE:
            self.setProperty("class", "fluent-card")
        else:
            self.setProperty("class", "modern-card")
        
        # Effet d'ombre
        shadow_effect = QGraphicsDropShadowEffect()
        shadow_effect.setBlurRadius(15)
        shadow_effect.setColor(QColor(0, 0, 0, 30))
        shadow_effect.setOffset(0, 2)
        self.setGraphicsEffect(shadow_effect)
    
    def toggle_content(self):
        """Basculer l'affichage du contenu"""
        if not hasattr(self, 'content_area'):
            return
        
        if self.is_expanded:
            # Réduire
            self.expand_animation.setStartValue(self.content_area.height())
            self.expand_animation.setEndValue(0)
            self.toggle_btn.setText("+")
        else:
            # Étendre
            self.expand_animation.setStartValue(0)
            self.expand_animation.setEndValue(200)  # Hauteur par défaut
            self.toggle_btn.setText("−")
        
        self.is_expanded = not self.is_expanded
        self.expand_animation.start()
    
    def enterEvent(self, event):
        """Animation d'entrée de survol"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() - 2,
            current_rect.width(),
            current_rect.height()
        )
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(new_rect)
        self.hover_animation.start()
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation de sortie de survol"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() + 2,
            current_rect.width(),
            current_rect.height()
        )
        
        self.hover_animation.setStartValue(current_rect)
        self.hover_animation.setEndValue(new_rect)
        self.hover_animation.start()
        
        super().leaveEvent(event)


class EnhancedTable(QTableWidget):
    """Table améliorée avec fonctionnalités modernes"""
    
    row_selected = pyqtSignal(int)
    data_changed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_table()
        self.setup_animations()
    
    def setup_table(self):
        """Configurer la table"""
        # Style moderne
        self.setAlternatingRowColors(True)
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)
        self.setSortingEnabled(True)
        
        # En-têtes
        self.horizontalHeader().setStretchLastSection(True)
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)
        self.verticalHeader().setVisible(False)
        
        # Connecter les signaux
        self.itemSelectionChanged.connect(self.on_selection_changed)
        self.itemChanged.connect(lambda: self.data_changed.emit())
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de chargement
        self.loading_animation = QVariantAnimation()
        self.loading_animation.setDuration(1000)
        self.loading_animation.setStartValue(0.3)
        self.loading_animation.setEndValue(1.0)
        self.loading_animation.setLoopCount(-1)
        self.loading_animation.valueChanged.connect(self.update_loading_opacity)
    
    def on_selection_changed(self):
        """Gérer le changement de sélection"""
        current_row = self.currentRow()
        if current_row >= 0:
            self.row_selected.emit(current_row)
    
    def add_data_row(self, data: list, animate: bool = True):
        """Ajouter une ligne avec animation optionnelle"""
        row_count = self.rowCount()
        self.insertRow(row_count)
        
        for col, value in enumerate(data):
            if col < self.columnCount():
                item = QTableWidgetItem(str(value))
                self.setItem(row_count, col, item)
        
        if animate:
            self.animate_new_row(row_count)
    
    def animate_new_row(self, row: int):
        """Animer l'ajout d'une nouvelle ligne"""
        # Animation de fondu
        for col in range(self.columnCount()):
            item = self.item(row, col)
            if item:
                effect = QGraphicsOpacityEffect()
                item.setGraphicsEffect(effect)
                
                fade_animation = QPropertyAnimation(effect, b"opacity")
                fade_animation.setDuration(500)
                fade_animation.setStartValue(0.0)
                fade_animation.setEndValue(1.0)
                fade_animation.start()
    
    def set_loading(self, loading: bool):
        """Activer/désactiver l'état de chargement"""
        if loading:
            self.setEnabled(False)
            self.loading_animation.start()
        else:
            self.setEnabled(True)
            self.loading_animation.stop()
            self.setStyleSheet("")
    
    def update_loading_opacity(self, value):
        """Mettre à jour l'opacité pendant le chargement"""
        self.setStyleSheet(f"QTableWidget {{ opacity: {value}; }}")


class EnhancedForm(QWidget):
    """Formulaire amélioré avec validation et animations"""
    
    form_submitted = pyqtSignal(dict)
    form_validated = pyqtSignal(bool)
    
    def __init__(self, fields_config: list, parent=None):
        super().__init__(parent)
        self.fields_config = fields_config
        self.fields = {}
        self.validators = {}
        
        self.setup_form()
        self.setup_validation()
    
    def setup_form(self):
        """Configurer le formulaire"""
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        for field_config in self.fields_config:
            field_layout = self.create_field(field_config)
            layout.addLayout(field_layout)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 Sauvegarder")
        self.save_btn.setProperty("class", "primary")
        self.save_btn.clicked.connect(self.submit_form)
        buttons_layout.addWidget(self.save_btn)
        
        self.cancel_btn = QPushButton("❌ Annuler")
        self.cancel_btn.setProperty("class", "secondary")
        self.cancel_btn.clicked.connect(self.clear_form)
        buttons_layout.addWidget(self.cancel_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
    
    def create_field(self, config: dict):
        """Créer un champ de formulaire"""
        field_layout = QVBoxLayout()
        
        # Label
        label = QLabel(config.get("label", ""))
        label.setFont(QFont("Segoe UI", 10, QFont.Bold))
        field_layout.addWidget(label)
        
        # Widget de saisie
        field_type = config.get("type", "text")
        field_name = config.get("name", "")
        
        if field_type == "text":
            widget = QLineEdit()
            widget.setPlaceholderText(config.get("placeholder", ""))
        elif field_type == "number":
            widget = QSpinBox()
            widget.setRange(config.get("min", 0), config.get("max", 999999))
        elif field_type == "decimal":
            widget = QDoubleSpinBox()
            widget.setRange(config.get("min", 0.0), config.get("max", 999999.99))
            widget.setDecimals(2)
        elif field_type == "combo":
            widget = QComboBox()
            widget.addItems(config.get("options", []))
        elif field_type == "date":
            widget = QDateEdit()
            widget.setDate(QDate.currentDate())
            widget.setCalendarPopup(True)
        elif field_type == "textarea":
            widget = QTextEdit()
            widget.setMaximumHeight(100)
        else:
            widget = QLineEdit()
        
        # Validation
        if config.get("required", False):
            label.setText(label.text() + " *")
            self.validators[field_name] = lambda w: bool(self.get_field_value(w))
        
        self.fields[field_name] = widget
        field_layout.addWidget(widget)
        
        return field_layout
    
    def setup_validation(self):
        """Configurer la validation en temps réel"""
        for field_name, widget in self.fields.items():
            if isinstance(widget, QLineEdit):
                widget.textChanged.connect(self.validate_form)
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                widget.valueChanged.connect(self.validate_form)
            elif isinstance(widget, QComboBox):
                widget.currentTextChanged.connect(self.validate_form)
    
    def validate_form(self):
        """Valider le formulaire"""
        is_valid = True
        
        for field_name, validator in self.validators.items():
            widget = self.fields.get(field_name)
            if widget and not validator(widget):
                is_valid = False
                widget.setStyleSheet("border: 2px solid #ff6b6b;")
            else:
                widget.setStyleSheet("")
        
        self.save_btn.setEnabled(is_valid)
        self.form_validated.emit(is_valid)
    
    def get_field_value(self, widget):
        """Obtenir la valeur d'un widget"""
        if isinstance(widget, QLineEdit):
            return widget.text()
        elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
            return widget.value()
        elif isinstance(widget, QComboBox):
            return widget.currentText()
        elif isinstance(widget, QDateEdit):
            return widget.date().toPyDate()
        elif isinstance(widget, QTextEdit):
            return widget.toPlainText()
        return None
    
    def submit_form(self):
        """Soumettre le formulaire"""
        if not self.save_btn.isEnabled():
            return
        
        data = {}
        for field_name, widget in self.fields.items():
            data[field_name] = self.get_field_value(widget)
        
        self.form_submitted.emit(data)
    
    def clear_form(self):
        """Vider le formulaire"""
        for widget in self.fields.values():
            if isinstance(widget, QLineEdit):
                widget.clear()
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                widget.setValue(0)
            elif isinstance(widget, QComboBox):
                widget.setCurrentIndex(0)
            elif isinstance(widget, QDateEdit):
                widget.setDate(QDate.currentDate())
            elif isinstance(widget, QTextEdit):
                widget.clear()
    
    def load_data(self, data: dict):
        """Charger des données dans le formulaire"""
        for field_name, value in data.items():
            widget = self.fields.get(field_name)
            if not widget:
                continue
            
            if isinstance(widget, QLineEdit):
                widget.setText(str(value))
            elif isinstance(widget, (QSpinBox, QDoubleSpinBox)):
                widget.setValue(value)
            elif isinstance(widget, QComboBox):
                index = widget.findText(str(value))
                if index >= 0:
                    widget.setCurrentIndex(index)
            elif isinstance(widget, QDateEdit):
                if isinstance(value, str):
                    widget.setDate(QDate.fromString(value, "yyyy-MM-dd"))
                else:
                    widget.setDate(value)
            elif isinstance(widget, QTextEdit):
                widget.setPlainText(str(value))


class EnhancedProgressBar(QProgressBar):
    """Barre de progression améliorée avec animations"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_progress_bar()
        self.setup_animations()
    
    def setup_progress_bar(self):
        """Configurer la barre de progression"""
        self.setTextVisible(True)
        self.setRange(0, 100)
        self.setValue(0)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de progression
        self.progress_animation = QPropertyAnimation(self, b"value")
        self.progress_animation.setDuration(1000)
        self.progress_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Animation de pulsation
        self.pulse_animation = QVariantAnimation()
        self.pulse_animation.setDuration(1500)
        self.pulse_animation.setStartValue(0.7)
        self.pulse_animation.setEndValue(1.0)
        self.pulse_animation.setLoopCount(-1)
        self.pulse_animation.valueChanged.connect(self.update_pulse)
    
    def animate_to_value(self, value: int):
        """Animer vers une valeur"""
        self.progress_animation.setStartValue(self.value())
        self.progress_animation.setEndValue(value)
        self.progress_animation.start()
    
    def set_pulsing(self, pulsing: bool):
        """Activer/désactiver la pulsation"""
        if pulsing:
            self.pulse_animation.start()
        else:
            self.pulse_animation.stop()
            self.setStyleSheet("")
    
    def update_pulse(self, value):
        """Mettre à jour la pulsation"""
        self.setStyleSheet(f"""
            QProgressBar::chunk {{
                opacity: {value};
            }}
        """)


class StatusIndicator(QWidget):
    """Indicateur de statut avec couleurs et animations"""
    
    def __init__(self, status: str = "unknown", parent=None):
        super().__init__(parent)
        self.status = status
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface"""
        self.setFixedSize(12, 12)
        self.update_status_style()
    
    def setup_animations(self):
        """Configurer les animations"""
        self.blink_animation = QVariantAnimation()
        self.blink_animation.setDuration(1000)
        self.blink_animation.setStartValue(0.3)
        self.blink_animation.setEndValue(1.0)
        self.blink_animation.setLoopCount(-1)
        self.blink_animation.valueChanged.connect(self.update_opacity)
    
    def set_status(self, status: str, animate: bool = True):
        """Définir le statut"""
        self.status = status
        self.update_status_style()
        
        if animate and status in ["warning", "error"]:
            self.blink_animation.start()
        else:
            self.blink_animation.stop()
    
    def update_status_style(self):
        """Mettre à jour le style selon le statut"""
        colors = {
            "success": "#4CAF50",
            "warning": "#FF9800", 
            "error": "#F44336",
            "info": "#2196F3",
            "unknown": "#9E9E9E"
        }
        
        color = colors.get(self.status, colors["unknown"])
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {color};
                border-radius: 6px;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }}
        """)
    
    def update_opacity(self, value):
        """Mettre à jour l'opacité pour le clignotement"""
        self.setWindowOpacity(value)
    
    def paintEvent(self, event):
        """Dessiner l'indicateur"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Dessiner le cercle
        painter.setBrush(self.palette().window())
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(self.rect())
        
        super().paintEvent(event)


class ModuleCard(EnhancedCard):
    """Carte de module avec icône et statistiques"""

    module_clicked = pyqtSignal(str)

    def __init__(self, module_name: str, icon: str, stats: dict, parent=None):
        self.module_name = module_name
        self.icon = icon
        self.stats = stats

        # Créer le widget de contenu
        content_widget = self.create_content_widget()

        super().__init__(module_name, content_widget, parent)

        # Connecter le clic
        self.clicked.connect(lambda: self.module_clicked.emit(self.module_name))

    def create_content_widget(self):
        """Créer le widget de contenu du module"""
        widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # En-tête avec icône
        header_layout = QHBoxLayout()

        icon_label = QLabel(self.icon)
        icon_label.setFont(QFont("Segoe UI", 32))
        icon_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(icon_label)

        header_layout.addStretch()

        # Indicateur de statut
        self.status_indicator = StatusIndicator("success")
        header_layout.addWidget(self.status_indicator)

        layout.addLayout(header_layout)

        # Statistiques
        stats_layout = QGridLayout()
        stats_layout.setSpacing(5)

        row = 0
        for key, value in self.stats.items():
            # Label
            label = QLabel(f"{key}:")
            label.setFont(QFont("Segoe UI", 9))
            stats_layout.addWidget(label, row, 0)

            # Valeur
            value_label = QLabel(str(value))
            value_label.setFont(QFont("Segoe UI", 9, QFont.Bold))
            value_label.setAlignment(Qt.AlignRight)
            stats_layout.addWidget(value_label, row, 1)

            row += 1

        layout.addLayout(stats_layout)

        widget.setLayout(layout)
        return widget

    def update_stats(self, new_stats: dict):
        """Mettre à jour les statistiques"""
        self.stats = new_stats
        # Recréer le contenu avec les nouvelles stats
        new_content = self.create_content_widget()

        # Remplacer le contenu existant
        if hasattr(self, 'content_widget') and self.content_widget:
            self.content_widget.setParent(None)

        self.content_widget = new_content
        if hasattr(self, 'content_area'):
            layout = self.content_area.layout()
            if layout:
                layout.addWidget(new_content)


class QuickActionButton(QPushButton):
    """Bouton d'action rapide avec animation"""

    def __init__(self, text: str, icon: str, action_type: str = "primary", parent=None):
        super().__init__(f"{icon} {text}", parent)
        self.action_type = action_type
        self.setup_button()
        self.setup_animations()

    def setup_button(self):
        """Configurer le bouton"""
        self.setProperty("class", self.action_type)
        self.setMinimumHeight(40)
        self.setFont(QFont("Segoe UI", 10, QFont.Bold))

    def setup_animations(self):
        """Configurer les animations"""
        # Animation de clic
        self.click_animation = QPropertyAnimation(self, b"geometry")
        self.click_animation.setDuration(100)
        self.click_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Animation de survol
        self.hover_animation = QVariantAnimation()
        self.hover_animation.setDuration(200)
        self.hover_animation.valueChanged.connect(self.update_hover_effect)

    def mousePressEvent(self, event):
        """Animation de clic"""
        if event.button() == Qt.LeftButton:
            current_rect = self.geometry()
            pressed_rect = QRect(
                current_rect.x() + 1,
                current_rect.y() + 1,
                current_rect.width() - 2,
                current_rect.height() - 2
            )

            self.click_animation.setStartValue(current_rect)
            self.click_animation.setEndValue(pressed_rect)
            self.click_animation.start()

        super().mousePressEvent(event)

    def enterEvent(self, event):
        """Animation d'entrée de survol"""
        self.hover_animation.setStartValue(1.0)
        self.hover_animation.setEndValue(1.1)
        self.hover_animation.start()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Animation de sortie de survol"""
        self.hover_animation.setStartValue(1.1)
        self.hover_animation.setEndValue(1.0)
        self.hover_animation.start()
        super().leaveEvent(event)

    def update_hover_effect(self, value):
        """Mettre à jour l'effet de survol"""
        self.setStyleSheet(f"""
            QPushButton {{
                transform: scale({value});
            }}
        """)
