"""
Contrôleur pour la gestion des fournisseurs
"""

from utils.logger import setup_logger


class SupplierController:
    """Contrôleur pour la gestion des fournisseurs"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
    
    def get_all_suppliers(self):
        """Récupérer tous les fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("SELECT * FROM suppliers ORDER BY nom")
            suppliers = cursor.fetchall()
            
            # Convertir en liste de dictionnaires
            if suppliers:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in suppliers]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des fournisseurs: {e}")
            return []
    
    def create_supplier(self, supplier_data):
        """Créer un nouveau fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            query = """
                INSERT INTO suppliers (nom, contact, telephone, email, adresse)
                VALUES (?, ?, ?, ?, ?)
            """
            
            cursor.execute(query, (
                supplier_data.get('nom'),
                supplier_data.get('contact'),
                supplier_data.get('telephone'),
                supplier_data.get('email'),
                supplier_data.get('adresse')
            ))
            
            self.db_manager.connection.commit()
            return cursor.lastrowid
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création du fournisseur: {e}")
            raise
    
    def get_statistics(self):
        """Récupérer les statistiques des fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'total_suppliers': 0}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'total_suppliers': 0}
            
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            total = cursor.fetchone()[0] if cursor.fetchone() else 0
            
            return {'total_suppliers': total}
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {'total_suppliers': 0}


    def count(self):
        """Compter le nombre total de fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return 0
            
            cursor = self.db_manager.cursor
            if not cursor:
                return 0
            
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            result = cursor.fetchone()
            return result[0] if result else 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors du comptage des fournisseurs: {e}")
            return 0
    
    def get_supplier_by_id(self, supplier_id):
        """Récupérer un fournisseur par son ID"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return None
            
            cursor = self.db_manager.cursor
            if not cursor:
                return None
            
            cursor.execute("SELECT * FROM suppliers WHERE id = ?", (supplier_id,))
            result = cursor.fetchone()
            
            if result:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du fournisseur {supplier_id}: {e}")
            return None
    
    def update_supplier(self, supplier_id, supplier_data):
        """Mettre à jour un fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            query = """
                UPDATE suppliers 
                SET nom = ?, contact = ?, telephone = ?, email = ?, adresse = ?
                WHERE id = ?
            """
            
            cursor.execute(query, (
                supplier_data.get('nom'),
                supplier_data.get('contact'),
                supplier_data.get('telephone'),
                supplier_data.get('email'),
                supplier_data.get('adresse'),
                supplier_id
            ))
            
            self.db_manager.connection.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du fournisseur: {e}")
            return False
    
    def delete_supplier(self, supplier_id):
        """Supprimer un fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Vérifier s'il y a des articles liés
            cursor.execute("SELECT COUNT(*) FROM articles WHERE fournisseur_id = ?", (supplier_id,))
            article_count = cursor.fetchone()[0]
            
            if article_count > 0:
                raise Exception(f"Impossible de supprimer: {article_count} articles liés à ce fournisseur")
            
            cursor.execute("DELETE FROM suppliers WHERE id = ?", (supplier_id,))
            self.db_manager.connection.commit()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression du fournisseur: {e}")
            raise
