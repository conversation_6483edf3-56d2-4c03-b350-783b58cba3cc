# Guide de Design - GSlim

## 🎨 Palette de Couleurs

### Couleurs Primaires
- **Primary 50**: `#eff6ff` - Arrière-plans très clairs
- **Primary 100**: `#dbeafe` - Arrière-plans clairs
- **Primary 200**: `#bfdbfe` - Bordures claires
- **Primary 300**: `#93c5fd` - Éléments désactivés
- **Primary 400**: `#60a5fa` - Éléments interactifs secondaires
- **Primary 500**: `#3b82f6` - **Couleur principale**
- **Primary 600**: `#2563eb` - Boutons et liens
- **Primary 700**: `#1d4ed8` - États de survol
- **Primary 800**: `#1e40af` - États actifs
- **Primary 900**: `#1e3a8a` - Texte sur fond clair

### Couleurs Secondaires (Gris)
- **Secondary 50**: `#f8fafc` - Arrière-plans de page
- **Secondary 100**: `#f1f5f9` - Arrière-plans de sections
- **Secondary 200**: `#e2e8f0` - Bordures
- **Secondary 300**: `#cbd5e1` - Bordures de survol
- **Secondary 400**: `#94a3b8` - Texte désactivé
- **Secondary 500**: `#64748b` - Texte secondaire
- **Secondary 600**: `#475569` - Texte principal (thème sombre)
- **Secondary 700**: `#334155` - Arrière-plans (thème sombre)
- **Secondary 800**: `#1e293b` - Arrière-plans foncés
- **Secondary 900**: `#0f172a` - Arrière-plans très foncés

### Couleurs d'État
- **Success**: `#10b981` - Succès, validation
- **Warning**: `#f59e0b` - Avertissements
- **Error**: `#ef4444` - Erreurs, suppression
- **Info**: `#3b82f6` - Informations
- **Accent**: `#06b6d4` - Éléments d'accent

## 📐 Système de Grille et Espacement

### Espacement
- **xs**: 4px - Espacement minimal
- **sm**: 8px - Espacement petit
- **md**: 16px - Espacement standard
- **lg**: 24px - Espacement large
- **xl**: 32px - Espacement très large
- **2xl**: 48px - Espacement maximum

### Rayons de Bordure
- **sm**: 6px - Petits éléments
- **md**: 8px - Éléments standard
- **lg**: 12px - Cartes
- **xl**: 16px - Grandes cartes

### Ombres
- **sm**: `0 1px 2px 0 rgba(0, 0, 0, 0.05)` - Éléments légers
- **md**: `0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)` - Cartes
- **lg**: `0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)` - Modales
- **xl**: `0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)` - Éléments flottants

## 🔤 Typographie

### Hiérarchie des Titres
- **Title**: 32px, Bold - Titres principaux
- **Subtitle**: 20px, SemiBold - Sous-titres
- **Heading**: 18px, SemiBold - Titres de sections
- **Body**: 14px, Regular - Texte principal
- **Caption**: 12px, Regular - Texte secondaire

### Poids des Polices
- **Regular**: 400
- **Medium**: 500
- **SemiBold**: 600
- **Bold**: 700

### Famille de Polices
- **Principale**: 'Segoe UI', 'Inter', 'Arial', sans-serif
- **Monospace**: 'Fira Code', 'Consolas', monospace

## 🎯 Composants UI

### Boutons

#### Types
- **Primary**: Couleur principale, actions importantes
- **Secondary**: Arrière-plan gris, actions secondaires
- **Outline**: Bordure colorée, actions alternatives
- **Ghost**: Transparent, actions subtiles
- **Success/Warning/Danger**: Couleurs d'état

#### Tailles
- **Small**: 32px hauteur, 8px/16px padding
- **Medium**: 40px hauteur, 12px/24px padding (défaut)
- **Large**: 48px hauteur, 16px/32px padding

### Cartes

#### Types
- **Card**: Carte standard avec bordure
- **Stat Card**: Carte de statistique avec gradient
- **Action Card**: Carte interactive avec animations

#### Propriétés
- Bordure: 1px solid
- Rayon: 12px
- Padding: 20px-24px
- Ombre: shadow-sm par défaut, shadow-md au survol

### Navigation

#### Structure
- Largeur fixe: 250px
- Éléments: 48px hauteur
- Indicateur actif: 4px largeur, couleur primaire
- Animations: 200ms ease-out

## 🎭 Animations et Transitions

### Durées
- **Fast**: 150ms - Micro-interactions
- **Normal**: 250ms - Transitions standard
- **Slow**: 350ms - Animations complexes

### Courbes d'Easing
- **Ease-out**: Interactions utilisateur
- **Ease-in**: Disparitions
- **Ease-in-out**: Animations cycliques

### Types d'Animations
- **Hover**: Transform translateY(-2px), shadow upgrade
- **Click**: Scale(0.95) puis retour
- **Loading**: Rotation continue ou pulsation
- **Slide**: Entrée/sortie latérale
- **Fade**: Changement d'opacité

## 🌓 Thèmes

### Thème Clair (Défaut)
- Arrière-plan principal: `#ffffff`
- Arrière-plan secondaire: `#f8fafc`
- Texte principal: `#0f172a`
- Texte secondaire: `#475569`

### Thème Sombre
- Arrière-plan principal: `#0f172a`
- Arrière-plan secondaire: `#1e293b`
- Texte principal: `#f8fafc`
- Texte secondaire: `#cbd5e1`

## 📱 Responsive Design

### Points de Rupture
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

### Adaptations
- Navigation: Collapse en drawer sur mobile
- Cartes: Stack vertical sur mobile
- Tableaux: Scroll horizontal sur mobile

## ✅ Bonnes Pratiques

### Accessibilité
- Contraste minimum: 4.5:1 pour le texte normal
- Contraste minimum: 3:1 pour le texte large
- Focus visible sur tous les éléments interactifs
- Support clavier complet

### Performance
- Animations GPU-accelerated (transform, opacity)
- Éviter les animations sur layout (width, height)
- Utiliser will-change avec parcimonie

### Cohérence
- Utiliser les tokens de design définis
- Respecter la hiérarchie visuelle
- Maintenir l'alignement et l'espacement
- Tester sur différentes tailles d'écran

## 🔧 Implémentation

### Classes CSS Utilitaires
```css
/* Espacement */
.p-xs { padding: 4px; }
.p-sm { padding: 8px; }
.p-md { padding: 16px; }
.p-lg { padding: 24px; }

/* Couleurs */
.text-primary { color: #0f172a; }
.text-secondary { color: #475569; }
.text-muted { color: #64748b; }

/* Animations */
.transition-fast { transition: all 150ms ease-out; }
.transition-normal { transition: all 250ms ease-out; }
```

### Widgets PyQt5
- Utiliser `ModernTheme` pour les couleurs
- Appliquer les propriétés CSS via `setProperty("class", "nom")`
- Implémenter les animations avec `QPropertyAnimation`
- Respecter les durées et courbes d'easing définies
