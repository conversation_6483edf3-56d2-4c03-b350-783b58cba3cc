"""
Gestionnaire de base de données pour GSlim - Version 1.0.2
Conforme au cahier des charges avec architecture Singleton
Sécurité renforcée avec bcrypt et transactions
"""

import sqlite3
import bcrypt
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager
from datetime import datetime
import threading

from config.settings import config
from utils.logger import setup_logger


class DatabaseManager:
    """
    Gestionnaire de base de données SQLite - Pattern Singleton
    Conforme aux spécifications du cahier des charges
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Implémentation du pattern Singleton"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(DatabaseManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialiser le gestionnaire de base de données"""
        if hasattr(self, '_initialized'):
            return

        self.db_path = str(config.DATABASE_PATH)
        self.logger = setup_logger(__name__)
        self.connection = None
        self._initialized = True

    @property
    def cursor(self):
        """Obtenir un curseur de base de données"""
        if self.connection:
            return self.connection.cursor()
        return None

    def connect(self):
        """Établir une connexion persistante à la base de données"""
        try:
            if not self.connection:
                # Créer le répertoire data s'il n'existe pas
                Path(self.db_path).parent.mkdir(exist_ok=True)
                
                self.connection = sqlite3.connect(self.db_path)
                self.connection.row_factory = sqlite3.Row
                self.logger.info("Connexion à la base de données établie")
            return self.connection
        except Exception as e:
            self.logger.error(f"Erreur lors de la connexion à la base de données: {e}")
            raise

    def disconnect(self):
        """Fermer la connexion à la base de données"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.logger.info("Connexion à la base de données fermée")

    @contextmanager
    def get_connection(self):
        """Gestionnaire de contexte pour les connexions"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Erreur de base de données: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """
        Exécuter une requête SELECT et retourner les résultats
        
        Args:
            query: Requête SQL
            params: Paramètres de la requête
            
        Returns:
            Liste des résultats sous forme de dictionnaires
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                
                # Convertir les résultats en dictionnaires
                columns = [description[0] for description in cursor.description]
                results = []
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))
                
                return results
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'exécution de la requête: {e}")
            return []

    def execute_update(self, query: str, params: tuple = ()) -> bool:
        """
        Exécuter une requête INSERT/UPDATE/DELETE
        
        Args:
            query: Requête SQL
            params: Paramètres de la requête
            
        Returns:
            True si succès, False sinon
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour: {e}")
            return False

    def get_last_insert_id(self) -> Optional[int]:
        """Obtenir l'ID du dernier enregistrement inséré"""
        try:
            if self.connection:
                return self.connection.lastrowid
            return None
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de l'ID: {e}")
            return None

    def table_exists(self, table_name: str) -> bool:
        """Vérifier si une table existe"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table_name,))
                return cursor.fetchone() is not None
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification de table: {e}")
            return False

    def get_table_info(self, table_name: str) -> List[Dict]:
        """Obtenir les informations sur une table"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"PRAGMA table_info({table_name})")
                
                columns = [description[0] for description in cursor.description]
                results = []
                for row in cursor.fetchall():
                    results.append(dict(zip(columns, row)))
                
                return results
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des infos de table: {e}")
            return []

    def backup_database(self, backup_path: str) -> bool:
        """Créer une sauvegarde de la base de données"""
        try:
            import shutil
            shutil.copy2(self.db_path, backup_path)
            self.logger.info(f"Sauvegarde créée: {backup_path}")
            return True
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            return False

    def get_database_stats(self) -> Dict[str, Any]:
        """Obtenir les statistiques de la base de données"""
        try:
            stats = {}
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Taille du fichier
                import os
                if os.path.exists(self.db_path):
                    stats['file_size'] = os.path.getsize(self.db_path)
                
                # Nombre de tables
                cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                stats['table_count'] = cursor.fetchone()[0]
                
                # Statistiques par table
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                table_stats = {}
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    table_stats[table_name] = cursor.fetchone()[0]
                
                stats['tables'] = table_stats
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des stats: {e}")
            return {}

    def initialize_database(self):
        """Initialiser la base de données avec les tables nécessaires"""
        try:
            self.logger.info("Initialisation de la base de données...")

            # S'assurer que la connexion est établie
            if not self.connection:
                self.connect()

            cursor = self.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")

            # Créer les tables principales
            self._create_tables(cursor)

            # Valider les changements
            self.connection.commit()

            self.logger.info("Base de données initialisée avec succès")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation de la base de données: {e}")
            raise

    def _create_tables(self, cursor):
        """
        Créer toutes les tables selon le schéma du cahier des charges
        Avec contraintes de clés étrangères et index optimisés
        """

        # Table users - Gestion des utilisateurs avec sécurité renforcée
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT FALSE,
                email TEXT,
                full_name TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                last_login TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)

        # Table categories - Catégories de produits
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)

        # Table suppliers - Fournisseurs avec évaluation
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                contact_person TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                rating INTEGER DEFAULT 0 CHECK (rating >= 0 AND rating <= 5),
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)

        # Table clients - Clients avec points de fidélité
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                address TEXT,
                loyalty_points INTEGER DEFAULT 0,
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)

        # Table products - Produits selon spécifications
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                category_id INTEGER,
                supplier_id INTEGER,
                price REAL NOT NULL CHECK (price > 0),
                stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
                minimum_stock INTEGER DEFAULT 0,
                barcode TEXT UNIQUE,
                unit TEXT DEFAULT 'pièce',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id) ON DELETE SET NULL
            )
        """)

        # Table sales - Ventes avec statuts et calculs
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_number TEXT NOT NULL UNIQUE,
                client_id INTEGER,
                user_id INTEGER NOT NULL,
                sale_date TEXT NOT NULL,
                status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'confirmed', 'invoiced', 'cancelled')),
                subtotal REAL DEFAULT 0,
                discount_percentage REAL DEFAULT 0 CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
                discount_amount REAL DEFAULT 0,
                tax_rate REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                total_amount REAL DEFAULT 0,
                notes TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE SET NULL,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE RESTRICT
            )
        """)

        # Table sale_items - Lignes de vente
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL CHECK (quantity > 0),
                unit_price REAL NOT NULL CHECK (unit_price > 0),
                line_total REAL NOT NULL,
                created_at TEXT NOT NULL,
                FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE RESTRICT
            )
        """)

        # Table stock_movements - Mouvements de stock avec traçabilité
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL CHECK (movement_type IN ('IN', 'OUT', 'SALE', 'ADJUST', 'RETURN')),
                quantity INTEGER NOT NULL,
                reference_type TEXT,
                reference_id INTEGER,
                user_id INTEGER,
                notes TEXT,
                movement_date TEXT NOT NULL,
                created_at TEXT NOT NULL,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE RESTRICT,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
            )
        """)

        # Créer les index pour optimiser les performances
        self._create_indexes(cursor)

        self.logger.info("Tables créées avec succès")

    def _create_indexes(self, cursor):
        """Créer les index pour optimiser les requêtes"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products (category_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_supplier ON products (supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products (barcode)",
            "CREATE INDEX IF NOT EXISTS idx_clients_name ON clients (name)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers (name)",
            "CREATE INDEX IF NOT EXISTS idx_sales_date ON sales (sale_date)",
            "CREATE INDEX IF NOT EXISTS idx_sales_client ON sales (client_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_user ON sales (user_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_status ON sales (status)",
            "CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items (sale_id)",
            "CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items (product_id)",
            "CREATE INDEX IF NOT EXISTS idx_stock_movements_product ON stock_movements (product_id)",
            "CREATE INDEX IF NOT EXISTS idx_stock_movements_date ON stock_movements (movement_date)",
            "CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON stock_movements (movement_type)"
        ]

        for index_sql in indexes:
            cursor.execute(index_sql)

        self.logger.info("Index créés avec succès")

        # Créer l'utilisateur admin par défaut
        self.create_default_admin()

    def create_default_admin(self):
        """Créer l'utilisateur admin par défaut si la table users est vide"""
        try:
            cursor = self.cursor

            # Vérifier si des utilisateurs existent
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]

            if user_count == 0:
                # Créer l'admin par défaut
                now = datetime.now().isoformat()
                password_hash = self.hash_password(config.DEFAULT_ADMIN_PASSWORD)

                cursor.execute("""
                    INSERT INTO users (username, password_hash, is_admin, full_name, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    config.DEFAULT_ADMIN_USERNAME,
                    password_hash,
                    True,
                    "Administrateur",
                    True,
                    now,
                    now
                ))

                self.connection.commit()
                self.logger.info(f"Utilisateur admin par défaut créé: {config.DEFAULT_ADMIN_USERNAME}")

        except Exception as e:
            self.logger.error(f"Erreur lors de la création de l'admin par défaut: {e}")

    # Méthodes d'authentification avec bcrypt
    def hash_password(self, password: str) -> str:
        """Hacher un mot de passe avec bcrypt"""
        salt = bcrypt.gensalt(rounds=config.BCRYPT_ROUNDS)
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

    def verify_password(self, password: str, password_hash: str) -> bool:
        """Vérifier un mot de passe contre son hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception as e:
            self.logger.error(f"Erreur lors de la vérification du mot de passe: {e}")
            return False

    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authentifier un utilisateur avec bcrypt"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor
            cursor.execute("""
                SELECT id, username, password_hash, is_admin, email, full_name, is_active
                FROM users
                WHERE username = ? AND is_active = 1
            """, (username,))

            user_row = cursor.fetchone()
            if not user_row:
                return None

            user_data = dict(user_row)

            # Vérifier le mot de passe
            if self.verify_password(password, user_data['password_hash']):
                # Mettre à jour la dernière connexion
                now = datetime.now().isoformat()
                cursor.execute("""
                    UPDATE users SET last_login = ?, updated_at = ? WHERE id = ?
                """, (now, now, user_data['id']))
                self.connection.commit()

                # Retourner les données utilisateur (sans le hash du mot de passe)
                del user_data['password_hash']
                return user_data

            return None

        except Exception as e:
            self.logger.error(f"Erreur lors de l'authentification: {e}")
            return None

    def close(self):
        """Fermer la connexion à la base de données (alias pour disconnect)"""
        self.disconnect()

    # Méthodes CRUD pour les catégories
    def get_categories(self) -> List[Dict[str, Any]]:
        """Récupérer toutes les catégories"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor
            cursor.execute("""
                SELECT id, name, description, created_at, updated_at
                FROM categories
                ORDER BY name
            """)

            categories = []
            for row in cursor.fetchall():
                categories.append(dict(row))

            return categories

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des catégories: {e}")
            return []

    def create_category(self, name: str, description: str = "") -> Optional[int]:
        """Créer une nouvelle catégorie"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                INSERT INTO categories (name, description, created_at, updated_at)
                VALUES (?, ?, ?, ?)
            """, (name, description, now, now))

            self.connection.commit()
            category_id = cursor.lastrowid

            self.logger.info(f"Catégorie créée: {name} (ID: {category_id})")
            return category_id

        except Exception as e:
            self.logger.error(f"Erreur lors de la création de la catégorie: {e}")
            return None

    def update_category(self, category_id: int, category_data: Dict[str, Any]) -> bool:
        """Mettre à jour une catégorie"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                UPDATE categories SET
                    name = ?, description = ?, updated_at = ?
                WHERE id = ?
            """, (
                category_data['name'],
                category_data.get('description', ''),
                now,
                category_id
            ))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Catégorie mise à jour: {category_data['name']} (ID: {category_id})")
                return True
            else:
                self.logger.warning(f"Aucune catégorie trouvée avec l'ID: {category_id}")
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de la catégorie: {e}")
            return False

    def delete_category(self, category_id: int) -> bool:
        """Supprimer une catégorie (met les produits à NULL)"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor

            # Vérifier si la catégorie existe
            cursor.execute("SELECT name FROM categories WHERE id = ?", (category_id,))
            category = cursor.fetchone()

            if not category:
                self.logger.warning(f"Catégorie non trouvée: {category_id}")
                return False

            # Mettre les produits de cette catégorie à NULL
            cursor.execute("""
                UPDATE products SET category_id = NULL WHERE category_id = ?
            """, (category_id,))

            # Supprimer la catégorie
            cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Catégorie supprimée: {category['name']} (ID: {category_id})")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de la catégorie: {e}")
            return False

    # Méthodes CRUD pour les produits
    def get_products(self, search_term: str = "", category_id: int = None) -> List[Dict[str, Any]]:
        """Récupérer les produits avec filtres optionnels"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor

            # Construction de la requête avec jointures
            query = """
                SELECT
                    p.id, p.name, p.description, p.price, p.stock_quantity,
                    p.minimum_stock, p.barcode, p.unit, p.is_active,
                    p.created_at, p.updated_at,
                    c.name as category_name,
                    s.name as supplier_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE p.is_active = 1
            """

            params = []

            # Filtre de recherche
            if search_term:
                query += " AND (p.name LIKE ? OR p.description LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern])

            # Filtre de catégorie
            if category_id:
                query += " AND p.category_id = ?"
                params.append(category_id)

            query += " ORDER BY p.name"

            cursor.execute(query, params)

            products = []
            for row in cursor.fetchall():
                products.append(dict(row))

            return products

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des produits: {e}")
            return []

    def create_product(self, product_data: Dict[str, Any]) -> Optional[int]:
        """Créer un nouveau produit"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                INSERT INTO products (
                    name, description, category_id, supplier_id, price,
                    stock_quantity, minimum_stock, barcode, unit,
                    is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                product_data['name'],
                product_data.get('description', ''),
                product_data.get('category_id'),
                product_data.get('supplier_id'),
                product_data['price'],
                product_data['stock_quantity'],
                product_data.get('minimum_stock', 0),
                product_data.get('barcode', ''),
                product_data.get('unit', 'pièce'),
                True,
                now,
                now
            ))

            self.connection.commit()
            product_id = cursor.lastrowid

            self.logger.info(f"Produit créé: {product_data['name']} (ID: {product_id})")
            return product_id

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du produit: {e}")
            return None

    def update_product(self, product_id: int, product_data: Dict[str, Any]) -> bool:
        """Mettre à jour un produit"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                UPDATE products SET
                    name = ?, description = ?, category_id = ?, supplier_id = ?,
                    price = ?, stock_quantity = ?, minimum_stock = ?,
                    barcode = ?, unit = ?, updated_at = ?
                WHERE id = ?
            """, (
                product_data['name'],
                product_data.get('description', ''),
                product_data.get('category_id'),
                product_data.get('supplier_id'),
                product_data['price'],
                product_data['stock_quantity'],
                product_data.get('minimum_stock', 0),
                product_data.get('barcode', ''),
                product_data.get('unit', 'pièce'),
                now,
                product_id
            ))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Produit mis à jour: {product_data['name']} (ID: {product_id})")
                return True
            else:
                self.logger.warning(f"Aucun produit trouvé avec l'ID: {product_id}")
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du produit: {e}")
            return False

    def delete_product(self, product_id: int) -> bool:
        """Supprimer un produit (soft delete)"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            # Vérifier si le produit a du stock
            cursor.execute("SELECT stock_quantity, name FROM products WHERE id = ?", (product_id,))
            product = cursor.fetchone()

            if not product:
                self.logger.warning(f"Produit non trouvé: {product_id}")
                return False

            if product['stock_quantity'] > 0:
                self.logger.warning(f"Impossible de supprimer le produit {product['name']}: stock non nul")
                return False

            # Soft delete
            cursor.execute("""
                UPDATE products SET is_active = 0, updated_at = ? WHERE id = ?
            """, (now, product_id))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Produit supprimé: {product['name']} (ID: {product_id})")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression du produit: {e}")
            return False

    # Méthodes pour les statistiques du dashboard
    def get_dashboard_stats(self) -> Dict[str, Any]:
        """Récupérer les statistiques pour le dashboard"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor
            stats = {}

            # Nombre total de produits
            cursor.execute("SELECT COUNT(*) FROM products WHERE is_active = 1")
            stats['total_products'] = cursor.fetchone()[0]

            # Valeur totale du stock
            cursor.execute("SELECT SUM(price * stock_quantity) FROM products WHERE is_active = 1")
            result = cursor.fetchone()[0]
            stats['stock_value'] = result if result else 0.0

            # Produits en stock bas
            cursor.execute("SELECT COUNT(*) FROM products WHERE is_active = 1 AND stock_quantity <= minimum_stock AND minimum_stock > 0")
            stats['low_stock'] = cursor.fetchone()[0]

            # Produits en rupture
            cursor.execute("SELECT COUNT(*) FROM products WHERE is_active = 1 AND stock_quantity = 0")
            stats['out_of_stock'] = cursor.fetchone()[0]

            # Ventes du jour (vraies données)
            cursor.execute("""
                SELECT COALESCE(SUM(total_amount), 0) as daily_sales
                FROM sales
                WHERE DATE(sale_date) = DATE('now') AND status = 'confirmed'
            """)
            stats['daily_sales'] = cursor.fetchone()[0]

            # Ventes du mois (vraies données)
            cursor.execute("""
                SELECT COALESCE(SUM(total_amount), 0) as monthly_sales
                FROM sales
                WHERE strftime('%Y-%m', sale_date) = strftime('%Y-%m', 'now')
                AND status = 'confirmed'
            """)
            stats['monthly_sales'] = cursor.fetchone()[0]

            # Nombre de clients
            cursor.execute("SELECT COUNT(*) FROM clients WHERE is_active = 1")
            stats['total_clients'] = cursor.fetchone()[0]

            # Nombre de fournisseurs
            cursor.execute("SELECT COUNT(*) FROM suppliers WHERE is_active = 1")
            stats['total_suppliers'] = cursor.fetchone()[0]

            return stats

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des statistiques: {e}")
            return {
                'total_products': 0,
                'stock_value': 0.0,
                'low_stock': 0,
                'out_of_stock': 0,
                'daily_sales': 0.0,
                'monthly_sales': 0.0,
                'total_clients': 0,
                'total_suppliers': 0
            }

    # Méthodes CRUD pour les clients
    def get_clients(self, search_term: str = "") -> List[Dict[str, Any]]:
        """Récupérer les clients avec filtre de recherche optionnel"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor

            query = """
                SELECT id, name, email, phone, address, loyalty_points, notes,
                       is_active, created_at, updated_at
                FROM clients
                WHERE is_active = 1
            """

            params = []

            # Filtre de recherche
            if search_term:
                query += " AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern, search_pattern])

            query += " ORDER BY name"

            cursor.execute(query, params)

            clients = []
            for row in cursor.fetchall():
                clients.append(dict(row))

            return clients

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des clients: {e}")
            return []

    def create_client(self, client_data: Dict[str, Any]) -> Optional[int]:
        """Créer un nouveau client"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                INSERT INTO clients (
                    name, email, phone, address, loyalty_points, notes,
                    is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                client_data['name'],
                client_data.get('email', ''),
                client_data.get('phone', ''),
                client_data.get('address', ''),
                client_data.get('loyalty_points', 0),
                client_data.get('notes', ''),
                True,
                now,
                now
            ))

            self.connection.commit()
            client_id = cursor.lastrowid

            self.logger.info(f"Client créé: {client_data['name']} (ID: {client_id})")
            return client_id

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du client: {e}")
            return None

    def update_client(self, client_id: int, client_data: Dict[str, Any]) -> bool:
        """Mettre à jour un client"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                UPDATE clients SET
                    name = ?, email = ?, phone = ?, address = ?,
                    loyalty_points = ?, notes = ?, updated_at = ?
                WHERE id = ?
            """, (
                client_data['name'],
                client_data.get('email', ''),
                client_data.get('phone', ''),
                client_data.get('address', ''),
                client_data.get('loyalty_points', 0),
                client_data.get('notes', ''),
                now,
                client_id
            ))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Client mis à jour: {client_data['name']} (ID: {client_id})")
                return True
            else:
                self.logger.warning(f"Aucun client trouvé avec l'ID: {client_id}")
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du client: {e}")
            return False

    def delete_client(self, client_id: int) -> bool:
        """Supprimer un client (soft delete avec anonymisation des ventes)"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            # Vérifier si le client existe
            cursor.execute("SELECT name FROM clients WHERE id = ?", (client_id,))
            client = cursor.fetchone()

            if not client:
                self.logger.warning(f"Client non trouvé: {client_id}")
                return False

            # Anonymiser les ventes passées (mettre client_id à NULL)
            cursor.execute("""
                UPDATE sales SET client_id = NULL WHERE client_id = ?
            """, (client_id,))

            # Soft delete du client
            cursor.execute("""
                UPDATE clients SET is_active = 0, updated_at = ? WHERE id = ?
            """, (now, client_id))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Client supprimé: {client['name']} (ID: {client_id})")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression du client: {e}")
            return False

    def update_loyalty_points(self, client_id: int, points_change: int, reason: str = "") -> bool:
        """Mettre à jour les points de fidélité d'un client"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            # Récupérer les points actuels
            cursor.execute("SELECT loyalty_points, name FROM clients WHERE id = ?", (client_id,))
            client = cursor.fetchone()

            if not client:
                self.logger.warning(f"Client non trouvé: {client_id}")
                return False

            new_points = max(0, client['loyalty_points'] + points_change)  # Pas de points négatifs

            # Mettre à jour les points
            cursor.execute("""
                UPDATE clients SET loyalty_points = ?, updated_at = ? WHERE id = ?
            """, (new_points, now, client_id))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Points de fidélité mis à jour pour {client['name']}: {client['loyalty_points']} → {new_points} ({reason})")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour des points de fidélité: {e}")
            return False

    # Méthodes CRUD pour les fournisseurs
    def get_suppliers(self, search_term: str = "") -> List[Dict[str, Any]]:
        """Récupérer les fournisseurs avec filtre de recherche optionnel"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor

            query = """
                SELECT id, name, contact_person, email, phone, address, rating,
                       notes, is_active, created_at, updated_at
                FROM suppliers
                WHERE is_active = 1
            """

            params = []

            # Filtre de recherche
            if search_term:
                query += " AND (name LIKE ? OR contact_person LIKE ? OR email LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern, search_pattern])

            query += " ORDER BY name"

            cursor.execute(query, params)

            suppliers = []
            for row in cursor.fetchall():
                suppliers.append(dict(row))

            return suppliers

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des fournisseurs: {e}")
            return []

    def create_supplier(self, supplier_data: Dict[str, Any]) -> Optional[int]:
        """Créer un nouveau fournisseur"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                INSERT INTO suppliers (
                    name, contact_person, email, phone, address, rating, notes,
                    is_active, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                supplier_data['name'],
                supplier_data.get('contact_person', ''),
                supplier_data.get('email', ''),
                supplier_data.get('phone', ''),
                supplier_data.get('address', ''),
                supplier_data.get('rating', 0),
                supplier_data.get('notes', ''),
                True,
                now,
                now
            ))

            self.connection.commit()
            supplier_id = cursor.lastrowid

            self.logger.info(f"Fournisseur créé: {supplier_data['name']} (ID: {supplier_id})")
            return supplier_id

        except Exception as e:
            self.logger.error(f"Erreur lors de la création du fournisseur: {e}")
            return None

    def update_supplier(self, supplier_id: int, supplier_data: Dict[str, Any]) -> bool:
        """Mettre à jour un fournisseur"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            cursor.execute("""
                UPDATE suppliers SET
                    name = ?, contact_person = ?, email = ?, phone = ?, address = ?,
                    rating = ?, notes = ?, updated_at = ?
                WHERE id = ?
            """, (
                supplier_data['name'],
                supplier_data.get('contact_person', ''),
                supplier_data.get('email', ''),
                supplier_data.get('phone', ''),
                supplier_data.get('address', ''),
                supplier_data.get('rating', 0),
                supplier_data.get('notes', ''),
                now,
                supplier_id
            ))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Fournisseur mis à jour: {supplier_data['name']} (ID: {supplier_id})")
                return True
            else:
                self.logger.warning(f"Aucun fournisseur trouvé avec l'ID: {supplier_id}")
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du fournisseur: {e}")
            return False

    def delete_supplier(self, supplier_id: int) -> bool:
        """Supprimer un fournisseur (soft delete avec gestion des produits liés)"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            # Vérifier si le fournisseur existe
            cursor.execute("SELECT name FROM suppliers WHERE id = ?", (supplier_id,))
            supplier = cursor.fetchone()

            if not supplier:
                self.logger.warning(f"Fournisseur non trouvé: {supplier_id}")
                return False

            # Mettre les produits de ce fournisseur à NULL
            cursor.execute("""
                UPDATE products SET supplier_id = NULL WHERE supplier_id = ?
            """, (supplier_id,))

            # Soft delete du fournisseur
            cursor.execute("""
                UPDATE suppliers SET is_active = 0, updated_at = ? WHERE id = ?
            """, (now, supplier_id))

            self.connection.commit()

            if cursor.rowcount > 0:
                self.logger.info(f"Fournisseur supprimé: {supplier['name']} (ID: {supplier_id})")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression du fournisseur: {e}")
            return False

    # Méthodes CRUD pour les ventes
    def get_sales(self, search_term: str = "", status: str = "") -> List[Dict[str, Any]]:
        """Récupérer les ventes avec filtres optionnels"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor

            query = """
                SELECT s.id, s.sale_number, s.sale_date, s.subtotal, s.discount_amount,
                       s.tax_amount, s.total_amount, s.status, s.notes, s.created_at, s.updated_at,
                       c.name as client_name, u.username as user_name
                FROM sales s
                LEFT JOIN clients c ON s.client_id = c.id
                LEFT JOIN users u ON s.user_id = u.id
                WHERE 1=1
            """

            params = []

            # Filtre de recherche
            if search_term:
                query += " AND (c.name LIKE ? OR s.notes LIKE ?)"
                search_pattern = f"%{search_term}%"
                params.extend([search_pattern, search_pattern])

            # Filtre de statut
            if status:
                query += " AND s.status = ?"
                params.append(status)

            query += " ORDER BY s.sale_date DESC, s.id DESC"

            cursor.execute(query, params)

            sales = []
            for row in cursor.fetchall():
                sale = dict(row)
                # Récupérer les lignes de vente
                sale['items'] = self._get_sale_items(sale['id'])
                sales.append(sale)

            return sales

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des ventes: {e}")
            return []

    def _get_sale_items(self, sale_id: int) -> List[Dict[str, Any]]:
        """Récupérer les lignes d'une vente"""
        try:
            cursor = self.cursor

            cursor.execute("""
                SELECT si.id, si.product_id, si.quantity, si.unit_price, si.line_total,
                       p.name as product_name, p.unit
                FROM sale_items si
                LEFT JOIN products p ON si.product_id = p.id
                WHERE si.sale_id = ?
                ORDER BY si.id
            """, (sale_id,))

            items = []
            for row in cursor.fetchall():
                items.append(dict(row))

            return items

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des lignes de vente: {e}")
            return []

    def create_sale(self, sale_data: Dict[str, Any], items: List[Dict[str, Any]]) -> Optional[int]:
        """Créer une nouvelle vente avec ses lignes"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            # Commencer une transaction
            cursor.execute("BEGIN TRANSACTION")

            # Générer un numéro de vente unique
            cursor.execute("SELECT COUNT(*) FROM sales")
            sale_count = cursor.fetchone()[0]
            sale_number = f"VTE{sale_count + 1:06d}"

            # Créer la vente
            cursor.execute("""
                INSERT INTO sales (
                    sale_number, client_id, user_id, sale_date, subtotal, discount_amount,
                    tax_amount, total_amount, status, notes, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                sale_number,
                sale_data.get('client_id'),
                sale_data.get('user_id'),
                sale_data.get('sale_date', now),
                sale_data['total_amount'],
                sale_data.get('discount', 0),
                sale_data.get('tax_amount', 0),
                sale_data['final_amount'],
                sale_data.get('status', 'draft'),
                sale_data.get('notes', ''),
                now,
                now
            ))

            sale_id = cursor.lastrowid

            # Créer les lignes de vente et mettre à jour le stock
            for item in items:
                # Créer la ligne de vente
                cursor.execute("""
                    INSERT INTO sale_items (
                        sale_id, product_id, quantity, unit_price, line_total, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    sale_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price'],
                    now
                ))

                # Mettre à jour le stock si la vente est confirmée
                if sale_data.get('status') == 'confirmed':
                    cursor.execute("""
                        UPDATE products
                        SET stock_quantity = stock_quantity - ?, updated_at = ?
                        WHERE id = ? AND stock_quantity >= ?
                    """, (item['quantity'], now, item['product_id'], item['quantity']))

                    if cursor.rowcount == 0:
                        # Stock insuffisant
                        cursor.execute("ROLLBACK")
                        self.logger.warning(f"Stock insuffisant pour le produit ID: {item['product_id']}")
                        return None

            # Valider la transaction
            cursor.execute("COMMIT")

            self.logger.info(f"Vente créée: {sale_data['final_amount']}€ (ID: {sale_id})")
            return sale_id

        except Exception as e:
            cursor.execute("ROLLBACK")
            self.logger.error(f"Erreur lors de la création de la vente: {e}")
            return None

    def update_sale_status(self, sale_id: int, new_status: str) -> bool:
        """Mettre à jour le statut d'une vente"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            # Récupérer l'ancien statut
            cursor.execute("SELECT status FROM sales WHERE id = ?", (sale_id,))
            result = cursor.fetchone()
            if not result:
                return False

            old_status = result['status']

            # Commencer une transaction
            cursor.execute("BEGIN TRANSACTION")

            # Mettre à jour le statut
            cursor.execute("""
                UPDATE sales SET status = ?, updated_at = ? WHERE id = ?
            """, (new_status, now, sale_id))

            # Gérer les changements de stock selon le statut
            if old_status != 'confirmed' and new_status == 'confirmed':
                # Déduire du stock
                cursor.execute("""
                    UPDATE products
                    SET stock_quantity = stock_quantity - si.quantity, updated_at = ?
                    FROM sale_items si
                    WHERE products.id = si.product_id AND si.sale_id = ?
                    AND products.stock_quantity >= si.quantity
                """, (now, sale_id))

            elif old_status == 'confirmed' and new_status != 'confirmed':
                # Remettre en stock
                cursor.execute("""
                    UPDATE products
                    SET stock_quantity = stock_quantity + si.quantity, updated_at = ?
                    FROM sale_items si
                    WHERE products.id = si.product_id AND si.sale_id = ?
                """, (now, sale_id))

            cursor.execute("COMMIT")

            self.logger.info(f"Statut de vente mis à jour: {old_status} → {new_status} (ID: {sale_id})")
            return True

        except Exception as e:
            cursor.execute("ROLLBACK")
            self.logger.error(f"Erreur lors de la mise à jour du statut: {e}")
            return False

    def delete_sale(self, sale_id: int) -> bool:
        """Supprimer une vente (avec remise en stock si nécessaire)"""
        try:
            if not self.connection:
                self.connect()

            now = datetime.now().isoformat()
            cursor = self.cursor

            # Récupérer les informations de la vente
            cursor.execute("SELECT status FROM sales WHERE id = ?", (sale_id,))
            result = cursor.fetchone()
            if not result:
                return False

            status = result['status']

            # Commencer une transaction
            cursor.execute("BEGIN TRANSACTION")

            # Si la vente était confirmée, remettre en stock
            if status == 'confirmed':
                cursor.execute("""
                    UPDATE products
                    SET stock_quantity = stock_quantity + si.quantity, updated_at = ?
                    FROM sale_items si
                    WHERE products.id = si.product_id AND si.sale_id = ?
                """, (now, sale_id))

            # Supprimer les lignes de vente
            cursor.execute("DELETE FROM sale_items WHERE sale_id = ?", (sale_id,))

            # Supprimer la vente
            cursor.execute("DELETE FROM sales WHERE id = ?", (sale_id,))

            cursor.execute("COMMIT")

            self.logger.info(f"Vente supprimée (ID: {sale_id})")
            return True

        except Exception as e:
            cursor.execute("ROLLBACK")
            self.logger.error(f"Erreur lors de la suppression de la vente: {e}")
            return False

    def get_sales_statistics(self) -> Dict[str, Any]:
        """Récupérer les statistiques de ventes"""
        try:
            if not self.connection:
                self.connect()

            cursor = self.cursor
            stats = {}

            # Ventes du jour
            cursor.execute("""
                SELECT COALESCE(SUM(total_amount), 0) as daily_sales
                FROM sales
                WHERE DATE(sale_date) = DATE('now') AND status = 'confirmed'
            """)
            stats['daily_sales'] = cursor.fetchone()[0]

            # Ventes du mois
            cursor.execute("""
                SELECT COALESCE(SUM(total_amount), 0) as monthly_sales
                FROM sales
                WHERE strftime('%Y-%m', sale_date) = strftime('%Y-%m', 'now')
                AND status = 'confirmed'
            """)
            stats['monthly_sales'] = cursor.fetchone()[0]

            # Nombre de ventes en attente
            cursor.execute("""
                SELECT COUNT(*) as pending_sales
                FROM sales
                WHERE status = 'draft'
            """)
            stats['pending_sales'] = cursor.fetchone()[0]

            # Produit le plus vendu
            cursor.execute("""
                SELECT p.name, SUM(si.quantity) as total_sold
                FROM sale_items si
                JOIN products p ON si.product_id = p.id
                JOIN sales s ON si.sale_id = s.id
                WHERE s.status = 'confirmed'
                GROUP BY p.id, p.name
                ORDER BY total_sold DESC
                LIMIT 1
            """)
            result = cursor.fetchone()
            stats['top_product'] = result['name'] if result else "Aucun"

            return stats

        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des statistiques de ventes: {e}")
            return {
                'daily_sales': 0,
                'monthly_sales': 0,
                'pending_sales': 0,
                'top_product': "Aucun"
            }
