#!/usr/bin/env python3
"""
Correctif Complet de Tous les Modules - GSlim
Corrige toutes les erreurs de chargement des modules
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def fix_article_controller_complete():
    """Corriger complètement le contrôleur d'articles"""
    print("🔧 Correction complète du contrôleur d'articles...")
    
    try:
        # Lire le fichier actuel
        with open("src/controllers/article_controller.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Ajouter toutes les méthodes manquantes
        additional_methods = '''
    def get_stock_statistics(self):
        """Obtenir les statistiques de stock complètes"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {
                    'total_articles': 0,
                    'low_stock_count': 0,
                    'total_value': 0.0,
                    'out_of_stock': 0,
                    'average_price': 0.0,
                    'total_quantity': 0
                }
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {
                    'total_articles': 0,
                    'low_stock_count': 0,
                    'total_value': 0.0,
                    'out_of_stock': 0,
                    'average_price': 0.0,
                    'total_quantity': 0
                }
            
            # Total des articles
            cursor.execute("SELECT COUNT(*) FROM articles")
            total_articles = cursor.fetchone()[0]
            
            # Articles en stock faible
            cursor.execute("SELECT COUNT(*) FROM articles WHERE quantite_stock <= seuil_alerte")
            low_stock_count = cursor.fetchone()[0]
            
            # Valeur totale du stock
            cursor.execute("SELECT SUM(prix_unitaire * quantite_stock) FROM articles")
            result = cursor.fetchone()[0]
            total_value = result if result else 0.0
            
            # Articles en rupture
            cursor.execute("SELECT COUNT(*) FROM articles WHERE quantite_stock = 0")
            out_of_stock = cursor.fetchone()[0]
            
            # Prix moyen
            cursor.execute("SELECT AVG(prix_unitaire) FROM articles")
            result = cursor.fetchone()[0]
            average_price = result if result else 0.0
            
            # Quantité totale
            cursor.execute("SELECT SUM(quantite_stock) FROM articles")
            result = cursor.fetchone()[0]
            total_quantity = result if result else 0
            
            return {
                'total_articles': total_articles,
                'low_stock_count': low_stock_count,
                'total_value': float(total_value),
                'out_of_stock': out_of_stock,
                'average_price': float(average_price),
                'total_quantity': total_quantity
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques de stock: {e}")
            return {
                'total_articles': 0,
                'low_stock_count': 0,
                'total_value': 0.0,
                'out_of_stock': 0,
                'average_price': 0.0,
                'total_quantity': 0
            }
    
    def get_all(self):
        """Alias pour get_all_articles (compatibilité)"""
        return self.get_all_articles()
    
    def get_low_stock_articles(self, limit=10):
        """Obtenir les articles en stock faible"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT * FROM articles 
                WHERE quantite_stock <= seuil_alerte 
                ORDER BY quantite_stock ASC 
                LIMIT ?
            """, (limit,))
            
            articles = cursor.fetchall()
            if articles:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in articles]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles en stock faible: {e}")
            return []
    
    def get_articles_by_category(self, category_id):
        """Obtenir les articles par catégorie"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("SELECT * FROM articles WHERE categorie_id = ?", (category_id,))
            articles = cursor.fetchall()
            
            if articles:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in articles]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles par catégorie: {e}")
            return []
'''
        
        # Vérifier si les méthodes existent déjà
        if 'def get_stock_statistics(self):' not in content:
            content += additional_methods
            
            # Sauvegarder le fichier modifié
            with open("src/controllers/article_controller.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Méthodes ajoutées au contrôleur d'articles")
        else:
            print("✅ Méthodes déjà présentes dans le contrôleur d'articles")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        return False


def fix_supplier_controller_complete():
    """Corriger complètement le contrôleur de fournisseurs"""
    print("🔧 Correction complète du contrôleur de fournisseurs...")
    
    try:
        # Lire le fichier actuel
        with open("src/controllers/supplier_controller.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Ajouter toutes les méthodes manquantes
        additional_methods = '''
    def count(self):
        """Compter le nombre total de fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return 0
            
            cursor = self.db_manager.cursor
            if not cursor:
                return 0
            
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            result = cursor.fetchone()
            return result[0] if result else 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors du comptage des fournisseurs: {e}")
            return 0
    
    def get_supplier_by_id(self, supplier_id):
        """Récupérer un fournisseur par son ID"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return None
            
            cursor = self.db_manager.cursor
            if not cursor:
                return None
            
            cursor.execute("SELECT * FROM suppliers WHERE id = ?", (supplier_id,))
            result = cursor.fetchone()
            
            if result:
                columns = [desc[0] for desc in cursor.description]
                return dict(zip(columns, result))
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du fournisseur {supplier_id}: {e}")
            return None
    
    def update_supplier(self, supplier_id, supplier_data):
        """Mettre à jour un fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            query = """
                UPDATE suppliers 
                SET nom = ?, contact = ?, telephone = ?, email = ?, adresse = ?
                WHERE id = ?
            """
            
            cursor.execute(query, (
                supplier_data.get('nom'),
                supplier_data.get('contact'),
                supplier_data.get('telephone'),
                supplier_data.get('email'),
                supplier_data.get('adresse'),
                supplier_id
            ))
            
            self.db_manager.connection.commit()
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour du fournisseur: {e}")
            return False
    
    def delete_supplier(self, supplier_id):
        """Supprimer un fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Vérifier s'il y a des articles liés
            cursor.execute("SELECT COUNT(*) FROM articles WHERE fournisseur_id = ?", (supplier_id,))
            article_count = cursor.fetchone()[0]
            
            if article_count > 0:
                raise Exception(f"Impossible de supprimer: {article_count} articles liés à ce fournisseur")
            
            cursor.execute("DELETE FROM suppliers WHERE id = ?", (supplier_id,))
            self.db_manager.connection.commit()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression du fournisseur: {e}")
            raise
    
    def get_suppliers_with_articles_count(self):
        """Obtenir les fournisseurs avec le nombre d'articles"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT s.*, COUNT(a.id) as articles_count
                FROM suppliers s
                LEFT JOIN articles a ON s.id = a.fournisseur_id
                GROUP BY s.id
                ORDER BY s.nom
            """)
            
            suppliers = cursor.fetchall()
            if suppliers:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in suppliers]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des fournisseurs avec comptage: {e}")
            return []
'''
        
        # Vérifier si les méthodes existent déjà
        if 'def count(self):' not in content:
            content += additional_methods
            
            # Sauvegarder le fichier modifié
            with open("src/controllers/supplier_controller.py", 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Méthodes ajoutées au contrôleur de fournisseurs")
        else:
            print("✅ Méthodes déjà présentes dans le contrôleur de fournisseurs")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la correction: {e}")
        return False


def create_missing_controllers():
    """Créer les contrôleurs manquants"""
    print("🔧 Création des contrôleurs manquants...")
    
    # Créer stock_movement_controller.py
    stock_movement_controller = '''"""
Contrôleur pour la gestion des mouvements de stock
"""

from utils.logger import setup_logger
from datetime import datetime


class StockMovementController:
    """Contrôleur pour la gestion des mouvements de stock"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
        
        self.logger.info("StockMovementController initialisé")
    
    def get_statistics(self):
        """Obtenir les statistiques des mouvements"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'total_movements': 0, 'entries': 0, 'exits': 0}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'total_movements': 0, 'entries': 0, 'exits': 0}
            
            # Total des mouvements
            cursor.execute("SELECT COUNT(*) FROM movements")
            total = cursor.fetchone()[0]
            
            # Entrées
            cursor.execute("SELECT COUNT(*) FROM movements WHERE type_mouvement = 'entrée'")
            entries = cursor.fetchone()[0]
            
            # Sorties
            cursor.execute("SELECT COUNT(*) FROM movements WHERE type_mouvement = 'sortie'")
            exits = cursor.fetchone()[0]
            
            return {
                'total_movements': total,
                'entries': entries,
                'exits': exits
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {'total_movements': 0, 'entries': 0, 'exits': 0}
    
    def get_recent_movements(self, limit=10):
        """Obtenir les mouvements récents"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT m.*, a.nom as article_nom
                FROM movements m
                LEFT JOIN articles a ON m.article_id = a.id
                ORDER BY m.date_mouvement DESC
                LIMIT ?
            """, (limit,))
            
            movements = cursor.fetchall()
            if movements:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in movements]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements récents: {e}")
            return []
'''
    
    # Créer order_controller.py
    order_controller = '''"""
Contrôleur pour la gestion des commandes
"""

from utils.logger import setup_logger
from datetime import datetime


class OrderController:
    """Contrôleur pour la gestion des commandes"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
        
        self.logger.info("OrderController initialisé")
    
    def get_statistics(self):
        """Obtenir les statistiques des commandes"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'total_orders': 0, 'pending': 0, 'completed': 0}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'total_orders': 0, 'pending': 0, 'completed': 0}
            
            # Total des commandes
            cursor.execute("SELECT COUNT(*) FROM orders")
            total = cursor.fetchone()[0]
            
            # En attente
            cursor.execute("SELECT COUNT(*) FROM orders WHERE statut = 'en_attente'")
            pending = cursor.fetchone()[0]
            
            # Terminées
            cursor.execute("SELECT COUNT(*) FROM orders WHERE statut = 'terminée'")
            completed = cursor.fetchone()[0]
            
            return {
                'total_orders': total,
                'pending': pending,
                'completed': completed
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {'total_orders': 0, 'pending': 0, 'completed': 0}
    
    def get_all_orders(self):
        """Récupérer toutes les commandes"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT o.*, s.nom as supplier_name
                FROM orders o
                LEFT JOIN suppliers s ON o.supplier_id = s.id
                ORDER BY o.date_commande DESC
            """)
            
            orders = cursor.fetchall()
            if orders:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in orders]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des commandes: {e}")
            return []
'''
    
    # Créer report_controller.py
    report_controller = '''"""
Contrôleur pour la génération de rapports
"""

from utils.logger import setup_logger
from datetime import datetime


class ReportController:
    """Contrôleur pour la génération de rapports"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
        
        self.logger.info("ReportController initialisé")
    
    def get_stock_report(self):
        """Générer un rapport de stock"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'articles': [], 'summary': {}}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'articles': [], 'summary': {}}
            
            # Articles avec détails
            cursor.execute("""
                SELECT a.*, s.nom as supplier_name, c.name as category_name
                FROM articles a
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                LEFT JOIN categories c ON a.categorie_id = c.id
                ORDER BY a.nom
            """)
            
            articles = cursor.fetchall()
            articles_list = []
            if articles:
                columns = [desc[0] for desc in cursor.description]
                articles_list = [dict(zip(columns, row)) for row in articles]
            
            # Résumé
            cursor.execute("SELECT COUNT(*), SUM(quantite_stock), SUM(prix_unitaire * quantite_stock) FROM articles")
            summary_data = cursor.fetchone()
            
            summary = {
                'total_articles': summary_data[0] if summary_data[0] else 0,
                'total_quantity': summary_data[1] if summary_data[1] else 0,
                'total_value': summary_data[2] if summary_data[2] else 0.0
            }
            
            return {
                'articles': articles_list,
                'summary': summary
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport de stock: {e}")
            return {'articles': [], 'summary': {}}
'''
    
    try:
        # Sauvegarder les contrôleurs
        controllers = [
            ("src/controllers/stock_movement_controller.py", stock_movement_controller),
            ("src/controllers/order_controller.py", order_controller),
            ("src/controllers/report_controller.py", report_controller)
        ]
        
        for file_path, content in controllers:
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ {file_path} créé")
            else:
                print(f"✅ {file_path} existe déjà")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des contrôleurs: {e}")
        return False


def main():
    """Fonction principale"""
    print("🔧 CORRECTIF COMPLET DE TOUS LES MODULES - GSLIM")
    print("="*60)
    
    fixes = [
        ("Contrôleur d'articles complet", fix_article_controller_complete),
        ("Contrôleur de fournisseurs complet", fix_supplier_controller_complete),
        ("Contrôleurs manquants", create_missing_controllers)
    ]
    
    success_count = 0
    for fix_name, fix_func in fixes:
        print(f"\n📋 {fix_name}...")
        try:
            result = fix_func()
            if result:
                success_count += 1
                print(f"✅ {fix_name} - Succès")
            else:
                print(f"❌ {fix_name} - Échec")
        except Exception as e:
            print(f"❌ {fix_name} - Erreur: {e}")
    
    print(f"\n📊 Correctifs appliqués: {success_count}/{len(fixes)}")
    
    if success_count == len(fixes):
        print("\n🎉 TOUS LES MODULES CORRIGÉS !")
        print("✅ Contrôleur d'articles avec toutes les méthodes")
        print("✅ Contrôleur de fournisseurs avec toutes les méthodes")
        print("✅ Contrôleurs de mouvements, commandes et rapports créés")
        
        print("\n🚀 Votre application devrait maintenant fonctionner sans erreurs !")
        print("   python main.py")
        print("   Connectez-vous avec: admin / admin123")
        
        return True
    else:
        print("\n⚠️  Correctifs partiels appliqués")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
