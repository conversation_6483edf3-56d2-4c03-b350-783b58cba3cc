# Script PowerShell avec bypass des politiques d'exécution
param(
    [switch]$Force
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   🔧 ACTIVATION ENVIRONNEMENT VIRTUEL" -ForegroundColor Cyan
Write-Host "   (Avec bypass des politiques)" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Fonction pour activer l'environnement manuellement
function Activate-VirtualEnv {
    $venvPath = "venv"
    $activateScript = Join-Path $venvPath "Scripts\Activate.ps1"
    
    if (-not (Test-Path $venvPath)) {
        Write-Host "❌ Le dossier venv n'existe pas" -ForegroundColor Red
        Write-Host "💡 Créez d'abord un environnement virtuel avec:" -ForegroundColor Yellow
        Write-Host "   python -m venv venv" -ForegroundColor White
        return $false
    }
    
    if (-not (Test-Path $activateScript)) {
        Write-Host "❌ Script d'activation non trouvé" -ForegroundColor Red
        return $false
    }
    
    try {
        # Méthode 1: Bypass direct
        Write-Host "🔄 Tentative d'activation avec bypass..." -ForegroundColor Yellow
        & PowerShell -ExecutionPolicy Bypass -File $activateScript
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Environnement virtuel activé avec bypass !" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "⚠️ Bypass échoué, tentative manuelle..." -ForegroundColor Yellow
    }
    
    # Méthode 2: Activation manuelle
    try {
        $venvScripts = Join-Path $venvPath "Scripts"
        $pythonPath = Join-Path $venvScripts "python.exe"
        $pipPath = Join-Path $venvScripts "pip.exe"
        
        if (Test-Path $pythonPath) {
            # Définir les variables d'environnement manuellement
            $env:VIRTUAL_ENV = Resolve-Path $venvPath
            $env:PATH = "$venvScripts;$env:PATH"
            $env:PYTHONHOME = $null
            
            Write-Host "✅ Environnement virtuel activé manuellement !" -ForegroundColor Green
            Write-Host "📍 VIRTUAL_ENV: $env:VIRTUAL_ENV" -ForegroundColor Cyan
            
            # Vérifier l'activation
            Write-Host ""
            Write-Host "🔍 Vérification de l'activation:" -ForegroundColor Cyan
            & $pythonPath --version
            & $pipPath --version
            
            return $true
        }
    }
    catch {
        Write-Host "❌ Activation manuelle échouée: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    return $false
}

# Fonction pour lancer GSlim
function Start-GSlim {
    Write-Host ""
    Write-Host "🚀 Lancement de GSlim..." -ForegroundColor Cyan
    
    $pythonPath = "venv\Scripts\python.exe"
    if (Test-Path $pythonPath) {
        & $pythonPath "main.py"
    } else {
        python "main.py"
    }
}

# Script principal
Write-Host "🔍 Diagnostic du problème PowerShell..." -ForegroundColor Yellow
$executionPolicy = Get-ExecutionPolicy
Write-Host "📋 Politique d'exécution actuelle: $executionPolicy" -ForegroundColor White

if ($executionPolicy -eq "Restricted") {
    Write-Host "⚠️ Politique d'exécution restrictive détectée" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "💡 Solutions recommandées:" -ForegroundColor Cyan
    Write-Host "   1. Exécuter en tant qu'administrateur:" -ForegroundColor White
    Write-Host "      Set-ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Gray
    Write-Host "   2. Utiliser Command Prompt (cmd) à la place" -ForegroundColor White
    Write-Host "   3. Utiliser le bypass (ce script)" -ForegroundColor White
    Write-Host ""
}

# Activer l'environnement
if (Activate-VirtualEnv) {
    Write-Host ""
    Write-Host "🎉 SUCCÈS ! Environnement virtuel activé" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 Commandes disponibles:" -ForegroundColor Cyan
    Write-Host "   python main.py          - Lancer GSlim" -ForegroundColor White
    Write-Host "   python --version        - Vérifier Python" -ForegroundColor White
    Write-Host "   pip list                - Voir les packages" -ForegroundColor White
    Write-Host ""
    
    $response = Read-Host "Voulez-vous lancer GSlim maintenant? (O/N)"
    if ($response -eq "O" -or $response -eq "o" -or $response -eq "Y" -or $response -eq "y") {
        Start-GSlim
    }
} else {
    Write-Host ""
    Write-Host "❌ Impossible d'activer l'environnement virtuel" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Solutions alternatives:" -ForegroundColor Yellow
    Write-Host "   1. Utiliser Command Prompt:" -ForegroundColor White
    Write-Host "      cmd /c 'venv\Scripts\activate.bat && python main.py'" -ForegroundColor Gray
    Write-Host "   2. Modifier la politique PowerShell (en admin):" -ForegroundColor White
    Write-Host "      Set-ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Gray
    Write-Host "   3. Utiliser le fichier .bat:" -ForegroundColor White
    Write-Host "      activer_venv.bat" -ForegroundColor Gray
}

Write-Host ""
Read-Host "Appuyez sur Entrée pour continuer"
