"""
Gestionnaire de Thèmes Unifié pour GSlim
Intègre tous les thèmes : Moderne, Professionnel, Fluent et Cyberpunk
"""

import os
import json
from enum import Enum
from PyQt5.QtCore import QObject, pyqtSignal, QSettings
from PyQt5.QtWidgets import QApplication

from .modern_theme import ModernTheme
from .professional_theme import ProfessionalTheme
from .fluent_theme import FluentTheme
from .futuristic_theme import FuturisticTheme

try:
    from qfluentwidgets import setTheme, Theme, setThemeColor
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False


class ThemeType(Enum):
    """Types de thèmes disponibles"""
    CLASSIC = "classic"
    MODERN = "modern"
    PROFESSIONAL = "professional"
    FLUENT = "fluent"
    CYBERPUNK = "cyberpunk"


class ThemeMode(Enum):
    """Modes de thème (clair/sombre)"""
    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"


class UnifiedThemeManager(QObject):
    """Gestionnaire unifié de tous les thèmes"""
    
    # Signaux
    theme_changed = pyqtSignal(str, str)  # theme_type, theme_mode
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings("GSlim", "ThemeManager")
        self.current_theme = ThemeType.MODERN
        self.current_mode = ThemeMode.DARK
        
        # Charger les préférences sauvegardées
        self.load_preferences()
    
    def get_available_themes(self):
        """Obtenir la liste des thèmes disponibles"""
        themes = {
            ThemeType.CLASSIC: {
                "name": "Classique",
                "description": "Thème PyQt5 standard",
                "icon": "🎨",
                "features": ["Simple", "Léger", "Compatible"]
            },
            ThemeType.MODERN: {
                "name": "Moderne",
                "description": "Design moderne avec dégradés",
                "icon": "✨",
                "features": ["Dégradés", "Animations", "Cards modernes"]
            },
            ThemeType.PROFESSIONAL: {
                "name": "Professionnel",
                "description": "Interface business élégante",
                "icon": "💼",
                "features": ["Élégant", "Business", "Glassmorphism"]
            },
            ThemeType.FLUENT: {
                "name": "Fluent Design",
                "description": "Microsoft Fluent Design System",
                "icon": "🌊",
                "features": ["Fluent Widgets", "Mica Effect", "Navigation moderne"],
                "requires": "PyQt-Fluent-Widgets"
            },
            ThemeType.CYBERPUNK: {
                "name": "Cyberpunk",
                "description": "Interface futuriste sci-fi",
                "icon": "🚀",
                "features": ["Néons", "Hologrammes", "Particules", "Matrix"]
            }
        }
        return themes
    
    def set_theme(self, theme_type: ThemeType, theme_mode: ThemeMode = None):
        """Appliquer un thème"""
        if theme_mode is None:
            theme_mode = self.current_mode
        
        self.current_theme = theme_type
        self.current_mode = theme_mode
        
        # Obtenir le stylesheet approprié
        stylesheet = self._get_theme_stylesheet(theme_type, theme_mode)
        
        # Appliquer le thème
        app = QApplication.instance()
        if app:
            app.setStyleSheet(stylesheet)
        
        # Appliquer les configurations spécifiques Fluent
        if theme_type == ThemeType.FLUENT and FLUENT_AVAILABLE:
            self._apply_fluent_theme(theme_mode)
        
        # Sauvegarder les préférences
        self.save_preferences()
        
        # Émettre le signal
        self.theme_changed.emit(theme_type.value, theme_mode.value)
        
        return True
    
    def _get_theme_stylesheet(self, theme_type: ThemeType, theme_mode: ThemeMode):
        """Obtenir le stylesheet pour un thème donné"""
        is_dark = theme_mode == ThemeMode.DARK
        
        if theme_type == ThemeType.CLASSIC:
            return self._get_classic_stylesheet(is_dark)
        elif theme_type == ThemeType.MODERN:
            return ModernTheme.get_modern_stylesheet(is_dark)
        elif theme_type == ThemeType.PROFESSIONAL:
            if is_dark:
                return ProfessionalTheme.get_professional_stylesheet()
            else:
                return ProfessionalTheme.get_professional_stylesheet()  # Adapter pour le mode clair
        elif theme_type == ThemeType.FLUENT:
            if is_dark:
                return FluentTheme.get_fluent_dark_stylesheet()
            else:
                return FluentTheme.get_fluent_light_stylesheet()
        elif theme_type == ThemeType.CYBERPUNK:
            return FuturisticTheme.get_complete_futuristic_theme()
        
        return ""
    
    def _get_classic_stylesheet(self, is_dark: bool):
        """Obtenir le stylesheet classique"""
        if is_dark:
            return """
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QFrame {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 4px;
            }
            """
        else:
            return """
            QWidget {
                background-color: #ffffff;
                color: #000000;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QFrame {
                background-color: #f5f5f5;
                border: 1px solid #cccccc;
                border-radius: 4px;
            }
            """
    
    def _apply_fluent_theme(self, theme_mode: ThemeMode):
        """Appliquer les configurations Fluent spécifiques"""
        if not FLUENT_AVAILABLE:
            return
        
        if theme_mode == ThemeMode.DARK:
            setTheme(Theme.DARK)
        else:
            setTheme(Theme.LIGHT)
        
        # Couleur d'accent
        setThemeColor('#0078d4')
    
    def toggle_theme_mode(self):
        """Basculer entre mode clair et sombre"""
        new_mode = ThemeMode.LIGHT if self.current_mode == ThemeMode.DARK else ThemeMode.DARK
        self.set_theme(self.current_theme, new_mode)
    
    def get_current_theme(self):
        """Obtenir le thème actuel"""
        return self.current_theme, self.current_mode
    
    def get_theme_info(self, theme_type: ThemeType):
        """Obtenir les informations d'un thème"""
        themes = self.get_available_themes()
        return themes.get(theme_type, {})
    
    def is_theme_available(self, theme_type: ThemeType):
        """Vérifier si un thème est disponible"""
        if theme_type == ThemeType.FLUENT:
            return FLUENT_AVAILABLE
        return True
    
    def get_theme_preview_colors(self, theme_type: ThemeType, theme_mode: ThemeMode):
        """Obtenir les couleurs de prévisualisation d'un thème"""
        is_dark = theme_mode == ThemeMode.DARK
        
        if theme_type == ThemeType.MODERN:
            colors = ModernTheme.get_theme_colors(is_dark)
        elif theme_type == ThemeType.PROFESSIONAL:
            colors = {
                'primary': '#667eea',
                'background': '#f5f7fa' if not is_dark else '#121212',
                'text': '#2d3748' if not is_dark else '#f0f0f0'
            }
        elif theme_type == ThemeType.FLUENT:
            colors = FluentTheme.get_current_theme_colors(is_dark)
        elif theme_type == ThemeType.CYBERPUNK:
            colors = FuturisticTheme.get_color_palette()
        else:  # Classic
            colors = {
                'primary': '#0078d4',
                'background': '#ffffff' if not is_dark else '#2b2b2b',
                'text': '#000000' if not is_dark else '#ffffff'
            }
        
        return colors
    
    def save_preferences(self):
        """Sauvegarder les préférences de thème"""
        self.settings.setValue("current_theme", self.current_theme.value)
        self.settings.setValue("current_mode", self.current_mode.value)
    
    def load_preferences(self):
        """Charger les préférences de thème"""
        theme_value = self.settings.value("current_theme", ThemeType.MODERN.value)
        mode_value = self.settings.value("current_mode", ThemeMode.DARK.value)
        
        try:
            self.current_theme = ThemeType(theme_value)
            self.current_mode = ThemeMode(mode_value)
        except ValueError:
            # Valeurs par défaut si les préférences sont corrompues
            self.current_theme = ThemeType.MODERN
            self.current_mode = ThemeMode.DARK
    
    def reset_to_default(self):
        """Réinitialiser aux paramètres par défaut"""
        self.set_theme(ThemeType.MODERN, ThemeMode.DARK)
    
    def export_theme_config(self, filepath: str):
        """Exporter la configuration de thème"""
        config = {
            "theme_type": self.current_theme.value,
            "theme_mode": self.current_mode.value,
            "version": "1.0.0"
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
    
    def import_theme_config(self, filepath: str):
        """Importer une configuration de thème"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            theme_type = ThemeType(config.get("theme_type", ThemeType.MODERN.value))
            theme_mode = ThemeMode(config.get("theme_mode", ThemeMode.DARK.value))
            
            self.set_theme(theme_type, theme_mode)
            return True
        except Exception as e:
            print(f"Erreur lors de l'import de la configuration: {e}")
            return False


# Instance globale du gestionnaire de thèmes
theme_manager = UnifiedThemeManager()


def get_theme_manager():
    """Obtenir l'instance du gestionnaire de thèmes"""
    return theme_manager


def apply_theme(theme_type: str, theme_mode: str = "dark"):
    """Fonction utilitaire pour appliquer un thème"""
    try:
        theme_enum = ThemeType(theme_type)
        mode_enum = ThemeMode(theme_mode)
        return theme_manager.set_theme(theme_enum, mode_enum)
    except ValueError:
        print(f"Thème ou mode invalide: {theme_type}, {theme_mode}")
        return False


def get_current_stylesheet():
    """Obtenir le stylesheet actuel"""
    theme_type, theme_mode = theme_manager.get_current_theme()
    return theme_manager._get_theme_stylesheet(theme_type, theme_mode)
