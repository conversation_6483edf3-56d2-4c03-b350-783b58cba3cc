#!/usr/bin/env python3
"""
Script pour créer des données de test pour les clients
"""

import sys
import os

# Ajouter src au path
sys.path.insert(0, 'src')

def create_clients_test_data():
    """Créer des données de test pour les clients"""
    print("👥 Création de données de test pour les clients...")
    
    try:
        from database.manager import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        
        print("✅ Base de données initialisée")
        
        # Créer des clients de test
        clients = [
            {
                'name': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '01 23 45 67 89',
                'address': '123 Rue de la Paix\n75001 Paris',
                'loyalty_points': 150,
                'notes': 'Client fidèle depuis 2020'
            },
            {
                'name': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '06 12 34 56 78',
                'address': '45 Avenue des Champs\n69000 Lyon',
                'loyalty_points': 320,
                'notes': '<PERSON><PERSON>fère les commandes par email'
            },
            {
                'name': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '04 56 78 90 12',
                'address': '78 Boulevard du Commerce\n13000 Marseille',
                'loyalty_points': 85,
                'notes': 'Commandes professionnelles'
            },
            {
                'name': 'Sophie Leroy',
                'email': '<EMAIL>',
                'phone': '02 34 56 78 90',
                'address': '12 Place du Marché\n44000 Nantes',
                'loyalty_points': 0,
                'notes': 'Nouveau client'
            },
            {
                'name': '<PERSON> <PERSON>',
                'email': '<EMAIL>',
                'phone': '05 67 89 01 23',
                'address': '89 Rue de la République\n31000 Toulouse',
                'loyalty_points': 245,
                'notes': 'Achète souvent du matériel informatique'
            },
            {
                'name': 'Entreprise TechSolutions',
                'email': '<EMAIL>',
                'phone': '01 45 67 89 01',
                'address': '456 Avenue de la Technologie\n92000 Nanterre',
                'loyalty_points': 500,
                'notes': 'Client entreprise - Remise 10%'
            },
            {
                'name': 'Isabelle Moreau',
                'email': '<EMAIL>',
                'phone': '03 78 90 12 34',
                'address': '23 Rue des Fleurs\n67000 Strasbourg',
                'loyalty_points': 75,
                'notes': 'Préfère les livraisons le matin'
            },
            {
                'name': 'François Petit',
                'email': '<EMAIL>',
                'phone': '04 89 01 23 45',
                'address': '67 Cours de la Liberté\n38000 Grenoble',
                'loyalty_points': 180,
                'notes': 'Client depuis 2019'
            },
            {
                'name': 'Association Les Amis du Livre',
                'email': '<EMAIL>',
                'phone': '02 90 12 34 56',
                'address': '34 Rue de la Culture\n35000 Rennes',
                'loyalty_points': 120,
                'notes': 'Association - Facture nécessaire'
            },
            {
                'name': 'Catherine Rousseau',
                'email': '<EMAIL>',
                'phone': '05 01 23 45 67',
                'address': '90 Avenue du Soleil\n33000 Bordeaux',
                'loyalty_points': 0,
                'notes': 'Intéressée par les produits écologiques'
            }
        ]
        
        for client_data in clients:
            client_id = db.create_client(client_data)
            if client_id:
                print(f"✅ Client créé: {client_data['name']} ({client_data['loyalty_points']} points)")
            else:
                print(f"❌ Erreur création client: {client_data['name']}")
        
        print(f"\n🎉 Données de test clients créées avec succès !")
        print(f"👥 {len(clients)} clients créés")
        print(f"🏆 Total points de fidélité: {sum(c['loyalty_points'] for c in clients)} points")
        print("\n📊 Répartition des points:")
        for client in sorted(clients, key=lambda x: x['loyalty_points'], reverse=True):
            print(f"   • {client['name']}: {client['loyalty_points']} points")
        
        print("\n🚀 Vous pouvez maintenant tester le module Clients !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données de test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("👥 Script de Création de Données de Test - Clients")
    print("=" * 60)
    
    success = create_clients_test_data()
    
    if success:
        print("\n✅ SUCCÈS ! Données de test clients créées")
        print("\n📋 Prochaines étapes:")
        print("   1. Lancez l'application: python main.py")
        print("   2. Connectez-vous: admin / admin123")
        print("   3. Allez dans le module Clients")
        print("   4. Testez toutes les fonctionnalités:")
        print("      • Visualisation des 10 clients")
        print("      • Recherche par nom/email/téléphone")
        print("      • Ajout d'un nouveau client")
        print("      • Modification d'un client existant")
        print("      • Gestion des points de fidélité")
        print("      • Suppression d'un client")
        return True
    else:
        print("\n❌ ÉCHEC de la création des données")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
