#!/usr/bin/env python3
"""
Test simple pour identifier les erreurs
"""

import sys
import os

# Ajouter src au path
sys.path.insert(0, 'src')

def test_import(module_name, import_statement):
    """Tester un import spécifique"""
    try:
        exec(import_statement)
        print(f"✅ {module_name}")
        return True
    except Exception as e:
        print(f"❌ {module_name}: {e}")
        return False

def main():
    print("🔍 Test des imports critiques...")
    
    # Tests de base
    tests = [
        ("PyQt5 Widgets", "from PyQt5.QtWidgets import QApplication"),
        ("PyQt5 Core", "from PyQt5.QtCore import Qt"),
        ("Configuration", "from config.settings import config"),
        ("Database Manager", "from database.manager import DatabaseManager"),
        ("Logger", "from utils.logger import setup_logger"),
    ]
    
    success = 0
    for name, statement in tests:
        if test_import(name, statement):
            success += 1
    
    print(f"\n📊 {success}/{len(tests)} imports de base réussis")
    
    if success == len(tests):
        print("\n🔍 Test des vues...")
        
        # Tests des vues
        view_tests = [
            ("Login Window", "from views.login_window import LoginWindow"),
            ("Dashboard Window", "from views.dashboard_window import DashboardWindow"),
            ("Articles Window", "from views.articles_window import ArticlesWindow"),
        ]
        
        view_success = 0
        for name, statement in view_tests:
            if test_import(name, statement):
                view_success += 1
        
        print(f"\n📊 {view_success}/{len(view_tests)} vues réussies")
        
        if view_success == len(view_tests):
            print("\n🔍 Test de l'application...")
            if test_import("GSlim App", "from src.app import GSlimApp"):
                print("\n🎉 TOUS LES TESTS RÉUSSIS !")
                return True
    
    print("\n❌ Des erreurs persistent")
    return False

if __name__ == "__main__":
    main()
