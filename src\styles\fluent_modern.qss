/* 
GSlim v1.0.0 - Fluent Modern Theme
Styles CSS modernes avec animations et effets Fluent Design
*/

/* === VARIABLES DE COULEURS === */
/* Ces couleurs seront remplacées dynamiquement par le code Python */

/* === STYLES GÉNÉRAUX === */
* {
    font-family: 'Segoe UI', 'Roboto', 'Poppins', sans-serif;
    outline: none;
}

QWidget {
    background-color: #121212;
    color: #F0F0F0;
    font-size: 14px;
    selection-background-color: #2196F3;
    selection-color: white;
}

/* === FENÊTRE PRINCIPALE === */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #121212, stop:1 #1F1F1F);
    border: none;
}

/* === CARTES FLUENT MODERNES === */
QFrame[class="fluent-card"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(31, 31, 31, 0.9), stop:1 rgba(37, 37, 37, 0.9));
    border: 1px solid rgba(44, 44, 44, 0.8);
    border-radius: 16px;
    padding: 24px;
    margin: 12px;
    backdrop-filter: blur(10px);
}

QFrame[class="fluent-card"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(37, 37, 37, 0.95), stop:1 rgba(44, 44, 44, 0.95));
    border: 1px solid rgba(33, 150, 243, 0.6);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(33, 150, 243, 0.15);
}

/* === CARTES DE STATISTIQUES AVEC GLASSMORPHISM === */
QFrame[class="stat-card"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(31, 31, 31, 0.85), stop:1 rgba(37, 37, 37, 0.85));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 28px;
    margin: 16px;
    min-height: 160px;
    backdrop-filter: blur(15px);
}

QFrame[class="stat-card"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(37, 37, 37, 0.9), stop:1 rgba(44, 44, 44, 0.9));
    border: 1px solid rgba(33, 150, 243, 0.4);
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 16px 48px rgba(33, 150, 243, 0.2);
}

/* === CARTES COLORÉES AVEC DÉGRADÉS === */
QFrame[class="stat-card-primary"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #2196F3, stop:0.5 #21CBF3, stop:1 #1976D2);
    border: none;
    color: white;
    box-shadow: 0 8px 32px rgba(33, 150, 243, 0.3);
}

QFrame[class="stat-card-primary"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #1976D2, stop:0.5 #1E88E5, stop:1 #0D47A1);
    box-shadow: 0 12px 40px rgba(33, 150, 243, 0.4);
}

QFrame[class="stat-card-success"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #4CAF50, stop:0.5 #66BB6A, stop:1 #388E3C);
    border: none;
    color: white;
    box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
}

QFrame[class="stat-card-success"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #388E3C, stop:0.5 #43A047, stop:1 #2E7D32);
    box-shadow: 0 12px 40px rgba(76, 175, 80, 0.4);
}

QFrame[class="stat-card-warning"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #FF9800, stop:0.5 #FFB74D, stop:1 #F57C00);
    border: none;
    color: white;
    box-shadow: 0 8px 32px rgba(255, 152, 0, 0.3);
}

QFrame[class="stat-card-warning"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #F57C00, stop:0.5 #FB8C00, stop:1 #E65100);
    box-shadow: 0 12px 40px rgba(255, 152, 0, 0.4);
}

QFrame[class="stat-card-error"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #F44336, stop:0.5 #EF5350, stop:1 #D32F2F);
    border: none;
    color: white;
    box-shadow: 0 8px 32px rgba(244, 67, 54, 0.3);
}

QFrame[class="stat-card-error"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #D32F2F, stop:0.5 #E53935, stop:1 #B71C1C);
    box-shadow: 0 12px 40px rgba(244, 67, 54, 0.4);
}

QFrame[class="stat-card-info"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #00BCD4, stop:0.5 #26C6DA, stop:1 #0097A7);
    border: none;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 188, 212, 0.3);
}

QFrame[class="stat-card-info"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #0097A7, stop:0.5 #00ACC1, stop:1 #006064);
    box-shadow: 0 12px 40px rgba(0, 188, 212, 0.4);
}

/* === BOUTONS FLUENT AVEC ANIMATIONS === */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #2196F3, stop:1 #1976D2);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    font-size: 14px;
    font-weight: 600;
    min-height: 20px;
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.3);
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #1976D2, stop:1 #0D47A1);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.4);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #0D47A1, stop:1 #01579B);
    transform: translateY(0px);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

QPushButton:disabled {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #424242, stop:1 #616161);
    color: #9E9E9E;
    transform: none;
    box-shadow: none;
}

/* === BOUTONS SECONDAIRES === */
QPushButton[class="secondary"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(37, 37, 37, 0.9), stop:1 rgba(44, 44, 44, 0.9));
    color: #F0F0F0;
    border: 1px solid rgba(33, 150, 243, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

QPushButton[class="secondary"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(44, 44, 44, 0.95), stop:1 rgba(60, 60, 60, 0.95));
    border-color: rgba(33, 150, 243, 0.6);
    box-shadow: 0 4px 16px rgba(33, 150, 243, 0.2);
}

/* === BOUTONS COLORÉS === */
QPushButton[class="success"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #4CAF50, stop:1 #388E3C);
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
}

QPushButton[class="success"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #388E3C, stop:1 #2E7D32);
    box-shadow: 0 8px 24px rgba(76, 175, 80, 0.4);
}

QPushButton[class="warning"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #FF9800, stop:1 #F57C00);
    box-shadow: 0 4px 16px rgba(255, 152, 0, 0.3);
}

QPushButton[class="warning"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #F57C00, stop:1 #E65100);
    box-shadow: 0 8px 24px rgba(255, 152, 0, 0.4);
}

QPushButton[class="danger"] {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #F44336, stop:1 #D32F2F);
    box-shadow: 0 4px 16px rgba(244, 67, 54, 0.3);
}

QPushButton[class="danger"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #D32F2F, stop:1 #B71C1C);
    box-shadow: 0 8px 24px rgba(244, 67, 54, 0.4);
}

/* === LABELS AVEC TYPOGRAPHIE MODERNE === */
QLabel[class="title"] {
    color: #F0F0F0;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
}

QLabel[class="subtitle"] {
    color: #B0B0B0;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 16px;
    letter-spacing: -0.2px;
}

QLabel[class="body"] {
    color: #F0F0F0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.6;
}

QLabel[class="caption"] {
    color: #808080;
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.4px;
}

QLabel[class="stat-value"] {
    color: inherit;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 8px;
    letter-spacing: -1px;
}

QLabel[class="stat-label"] {
    color: inherit;
    font-size: 14px;
    font-weight: 500;
    opacity: 0.9;
    letter-spacing: 0.2px;
}

/* === CHAMPS DE SAISIE MODERNES === */
QLineEdit, QTextEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(31, 31, 31, 0.9), stop:1 rgba(37, 37, 37, 0.9));
    border: 2px solid rgba(44, 44, 44, 0.8);
    border-radius: 12px;
    padding: 14px 16px;
    font-size: 14px;
    color: #F0F0F0;
    backdrop-filter: blur(10px);
}

QLineEdit:focus, QTextEdit:focus {
    border-color: rgba(33, 150, 243, 0.8);
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(37, 37, 37, 0.95), stop:1 rgba(44, 44, 44, 0.95));
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
}

QLineEdit:hover, QTextEdit:hover {
    border-color: rgba(60, 60, 60, 0.8);
}

/* === SCROLLBARS MODERNES === */
QScrollBar:vertical {
    background: rgba(31, 31, 31, 0.5);
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(60, 60, 60, 0.8), stop:1 rgba(80, 80, 80, 0.8));
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 rgba(33, 150, 243, 0.6), stop:1 rgba(25, 118, 210, 0.6));
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* === TOOLTIPS MODERNES === */
QToolTip {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 rgba(44, 44, 44, 0.95), stop:1 rgba(60, 60, 60, 0.95));
    color: #F0F0F0;
    border: 1px solid rgba(33, 150, 243, 0.3);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

/* === ANIMATIONS GLOBALES === */
* {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

*:hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* === EFFETS SPÉCIAUX === */
QFrame[class="glass-effect"] {
    background: rgba(31, 31, 31, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

QFrame[class="neon-border"] {
    border: 2px solid transparent;
    background: linear-gradient(45deg, #2196F3, #21CBF3) border-box;
    border-radius: 12px;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
    QFrame[class="stat-card"] {
        min-height: 120px;
        padding: 20px;
        margin: 8px;
    }
    
    QLabel[class="title"] {
        font-size: 24px;
    }
    
    QLabel[class="stat-value"] {
        font-size: 28px;
    }
}
