"""
Correctif pour les icônes FluentIcon manquantes
"""

from qfluentwidgets import FluentIcon


def get_safe_icon(icon_name, default_icon='DOCUMENT'):
    """Obtenir une icône de manière sécurisée"""
    try:
        # Essayer d'obtenir l'icône demandée
        if hasattr(FluentIcon, icon_name):
            return getattr(FluentIcon, icon_name)
        
        # Icônes alternatives
        alternatives = {
            'CHART': ['PIE_CHART', 'BAR_CHART', 'GRAPH', 'ANALYTICS', 'DOCUMENT'],
            'GRAPH': ['CHART', 'PIE_CHART', 'BAR_CHART', 'ANALYTICS', 'DOCUMENT'],
            'ANALYTICS': ['CHART', 'GRAPH', 'PIE_CHART', 'DOCUMENT']
        }
        
        if icon_name in alternatives:
            for alt_icon in alternatives[icon_name]:
                if hasattr(FluentIcon, alt_icon):
                    return getattr(FluentIcon, alt_icon)
        
        # Icône par défaut
        if hasattr(FluentIcon, default_icon):
            return getattr(FluentIcon, default_icon)
        
        # Dernière option
        return FluentIcon.HOME if hasattr(FluentIcon, 'HOME') else None
        
    except Exception as e:
        print(f"Erreur icône {icon_name}: {e}")
        return None


# Icônes couramment utilisées
SAFE_CHART_ICON = get_safe_icon('CHART')
SAFE_GRAPH_ICON = get_safe_icon('GRAPH')
SAFE_ANALYTICS_ICON = get_safe_icon('ANALYTICS')
