"""
Composants Fluent réutilisables pour GSlim
Widgets modernes avec design Fluent et animations
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGraphicsDropShadowEffect, QGraphicsOpacityEffect
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect,
    QTimer, QParallelAnimationGroup, QSequentialAnimationGroup
)
from PyQt5.QtGui import QFont, QPainter, QColor, QLinearGradient

try:
    from qfluentwidgets import (
        CardWidget, PushButton as FluentButton, ToolButton,
        InfoBadge, FluentIcon, TitleLabel, BodyLabel, CaptionLabel,
        StrongBodyLabel, ToggleButton
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False


class FluentCard(CardWidget if FLUENT_AVAILABLE else QFrame):
    """Carte Fluent moderne avec animations"""
    
    clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_hovered = False
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface de base"""
        if not FLUENT_AVAILABLE:
            self.setProperty("class", "fluent-card")
        
        self.setCursor(Qt.PointingHandCursor)
        self.setMinimumSize(200, 120)
        
        # Effet d'ombre
        if not FLUENT_AVAILABLE:
            self.shadow_effect = QGraphicsDropShadowEffect()
            self.shadow_effect.setBlurRadius(20)
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))
            self.shadow_effect.setOffset(0, 4)
            self.setGraphicsEffect(self.shadow_effect)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de position
        self.position_animation = QPropertyAnimation(self, b"geometry")
        self.position_animation.setDuration(300)
        self.position_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Animation d'opacité
        if not FLUENT_AVAILABLE:
            self.opacity_effect = QGraphicsOpacityEffect()
            self.opacity_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
            self.opacity_animation.setDuration(200)
            self.opacity_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def enterEvent(self, event):
        """Animation d'entrée de survol"""
        self.is_hovered = True
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() - 4,
            current_rect.width(),
            current_rect.height()
        )
        
        self.position_animation.setStartValue(current_rect)
        self.position_animation.setEndValue(new_rect)
        self.position_animation.start()
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation de sortie de survol"""
        self.is_hovered = False
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() + 4,
            current_rect.width(),
            current_rect.height()
        )
        
        self.position_animation.setStartValue(current_rect)
        self.position_animation.setEndValue(new_rect)
        self.position_animation.start()
        
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """Gérer le clic"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class FluentStatCard(FluentCard):
    """Carte de statistique avec design Fluent"""
    
    def __init__(self, title: str, value: str, subtitle: str = "", 
                 icon: str = "INFO", color: str = "primary", parent=None):
        super().__init__(parent)
        self.title = title
        self.value = value
        self.subtitle = subtitle
        self.icon_name = icon
        self.color = color
        
        self.setup_content()
        self.apply_color_theme()
    
    def setup_content(self):
        """Configurer le contenu de la carte"""
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(12)
        
        # Header avec icône et badge
        header_layout = QHBoxLayout()
        
        # Icône
        if FLUENT_AVAILABLE:
            icon_widget = ToolButton()
            icon_widget.setIcon(getattr(FluentIcon, self.icon_name, FluentIcon.INFO))
            icon_widget.setFixedSize(32, 32)
            icon_widget.setEnabled(False)
            header_layout.addWidget(icon_widget)
        
        header_layout.addStretch()
        
        # Badge de statut
        if FLUENT_AVAILABLE:
            badge = InfoBadge.success("●", self)
            badge.setFixedSize(8, 8)
            header_layout.addWidget(badge)
        
        layout.addLayout(header_layout)
        
        # Valeur principale
        if FLUENT_AVAILABLE:
            self.value_label = TitleLabel(self.value)
        else:
            self.value_label = QLabel(self.value)
            self.value_label.setProperty("class", "stat-value")
        
        layout.addWidget(self.value_label)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = BodyLabel(self.title)
        else:
            title_label = QLabel(self.title)
            title_label.setProperty("class", "stat-label")
        
        layout.addWidget(title_label)
        
        # Sous-titre (optionnel)
        if self.subtitle:
            if FLUENT_AVAILABLE:
                subtitle_label = CaptionLabel(self.subtitle)
            else:
                subtitle_label = QLabel(self.subtitle)
                subtitle_label.setProperty("class", "caption")
            
            layout.addWidget(subtitle_label)
        
        self.setLayout(layout)
    
    def apply_color_theme(self):
        """Appliquer le thème de couleur"""
        if not FLUENT_AVAILABLE:
            self.setProperty("class", f"stat-card-{self.color}")
    
    def update_value(self, new_value: str, animate: bool = True):
        """Mettre à jour la valeur avec animation optionnelle"""
        if animate:
            # Animation de changement de valeur
            self.fade_out_and_update(new_value)
        else:
            self.value = new_value
            self.value_label.setText(new_value)
    
    def fade_out_and_update(self, new_value: str):
        """Animation de fondu pour le changement de valeur"""
        if not FLUENT_AVAILABLE:
            return
        
        # Animation de fondu sortant
        fade_out = QPropertyAnimation(self.opacity_effect, b"opacity")
        fade_out.setDuration(150)
        fade_out.setStartValue(1.0)
        fade_out.setEndValue(0.3)
        
        # Animation de fondu entrant
        fade_in = QPropertyAnimation(self.opacity_effect, b"opacity")
        fade_in.setDuration(150)
        fade_in.setStartValue(0.3)
        fade_in.setEndValue(1.0)
        
        # Séquence d'animations
        self.fade_sequence = QSequentialAnimationGroup()
        self.fade_sequence.addAnimation(fade_out)
        self.fade_sequence.addAnimation(fade_in)
        
        # Mettre à jour la valeur au milieu de l'animation
        fade_out.finished.connect(lambda: self.value_label.setText(new_value))
        
        self.fade_sequence.start()


class FluentButton(FluentButton if FLUENT_AVAILABLE else QPushButton):
    """Bouton Fluent avec animations"""
    
    def __init__(self, text: str, icon=None, button_type: str = "primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.is_loading = False
        
        if FLUENT_AVAILABLE and icon:
            self.setIcon(icon)
        
        self.setup_ui()
        self.setup_animations()
    
    def setup_ui(self):
        """Configurer l'interface du bouton"""
        if not FLUENT_AVAILABLE:
            self.setProperty("class", self.button_type)
        
        self.setMinimumHeight(36)
        self.setMinimumWidth(100)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de pulsation pour le loading
        self.pulse_animation = QPropertyAnimation(self, b"geometry")
        self.pulse_animation.setDuration(1000)
        self.pulse_animation.setEasingCurve(QEasingCurve.InOutSine)
        self.pulse_animation.setLoopCount(-1)
        
        # Animation de clic
        self.click_animation = QPropertyAnimation(self, b"geometry")
        self.click_animation.setDuration(100)
        self.click_animation.setEasingCurve(QEasingCurve.OutCubic)
    
    def set_loading(self, loading: bool):
        """Activer/désactiver l'état de chargement"""
        self.is_loading = loading
        
        if loading:
            self.setEnabled(False)
            original_text = self.text()
            self.setText("Chargement...")
            self.start_pulse_animation()
            
            # Restaurer le texte après 3 secondes
            QTimer.singleShot(3000, lambda: self.restore_button(original_text))
        else:
            self.setEnabled(True)
            self.stop_pulse_animation()
    
    def start_pulse_animation(self):
        """Démarrer l'animation de pulsation"""
        if not FLUENT_AVAILABLE:
            current_rect = self.geometry()
            expanded_rect = QRect(
                current_rect.x() - 2,
                current_rect.y() - 1,
                current_rect.width() + 4,
                current_rect.height() + 2
            )
            
            self.pulse_animation.setStartValue(current_rect)
            self.pulse_animation.setEndValue(expanded_rect)
            self.pulse_animation.start()
    
    def stop_pulse_animation(self):
        """Arrêter l'animation de pulsation"""
        self.pulse_animation.stop()
    
    def restore_button(self, original_text: str):
        """Restaurer l'état normal du bouton"""
        self.setText(original_text)
        self.set_loading(False)
    
    def mousePressEvent(self, event):
        """Animation de clic"""
        if event.button() == Qt.LeftButton and not self.is_loading:
            if not FLUENT_AVAILABLE:
                current_rect = self.geometry()
                pressed_rect = QRect(
                    current_rect.x() + 1,
                    current_rect.y() + 1,
                    current_rect.width() - 2,
                    current_rect.height() - 2
                )
                
                self.click_animation.setStartValue(current_rect)
                self.click_animation.setEndValue(pressed_rect)
                self.click_animation.start()
        
        super().mousePressEvent(event)


class FluentHeader(QWidget):
    """En-tête Fluent avec titre et actions"""
    
    theme_changed = pyqtSignal(bool)  # True pour sombre, False pour clair
    logout_requested = pyqtSignal()
    
    def __init__(self, title: str = "Tableau de Bord", parent=None):
        super().__init__(parent)
        self.title = title
        self.is_dark_theme = True
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurer l'interface de l'en-tête"""
        layout = QHBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        # Titre principal
        if FLUENT_AVAILABLE:
            title_label = TitleLabel(self.title)
        else:
            title_label = QLabel(self.title)
            title_label.setProperty("class", "title")
        
        layout.addWidget(title_label)
        layout.addStretch()
        
        # Actions
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(8)
        
        # Bouton changement de thème
        if FLUENT_AVAILABLE:
            self.theme_btn = ToggleButton()
            self.theme_btn.setText("🌙")
        else:
            self.theme_btn = QPushButton("🌙")
            self.theme_btn.setProperty("class", "secondary")
        
        self.theme_btn.clicked.connect(self.toggle_theme)
        actions_layout.addWidget(self.theme_btn)
        
        # Bouton déconnexion
        if FLUENT_AVAILABLE:
            logout_btn = FluentButton("Déconnexion")
            logout_btn.setIcon(FluentIcon.POWER_BUTTON)
        else:
            logout_btn = QPushButton("Déconnexion")
            logout_btn.setProperty("class", "danger")
        
        logout_btn.clicked.connect(self.logout_requested.emit)
        actions_layout.addWidget(logout_btn)
        
        layout.addLayout(actions_layout)
        
        # Info de mise à jour
        if FLUENT_AVAILABLE:
            update_info = CaptionLabel("Dernière mise à jour: maintenant")
        else:
            update_info = QLabel("Dernière mise à jour: maintenant")
            update_info.setProperty("class", "caption")
        
        layout.addWidget(update_info)
        
        self.setLayout(layout)
    
    def toggle_theme(self):
        """Basculer le thème"""
        self.is_dark_theme = not self.is_dark_theme
        
        if self.is_dark_theme:
            self.theme_btn.setText("🌙")
        else:
            self.theme_btn.setText("☀️")
        
        self.theme_changed.emit(self.is_dark_theme)


class FluentSidebar(QWidget):
    """Barre latérale Fluent avec navigation"""
    
    page_changed = pyqtSignal(str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_page = "dashboard"
        self.nav_buttons = {}
        
        self.setup_ui()
    
    def setup_ui(self):
        """Configurer l'interface de la sidebar"""
        self.setFixedWidth(250)
        self.setProperty("class", "sidebar")
        
        layout = QVBoxLayout()
        layout.setContentsMargins(16, 20, 16, 20)
        layout.setSpacing(8)
        
        # Logo/Titre
        if FLUENT_AVAILABLE:
            logo_label = StrongBodyLabel("GSlim v1.0.0")
        else:
            logo_label = QLabel("GSlim v1.0.0")
            logo_label.setProperty("class", "subtitle")
        
        layout.addWidget(logo_label)
        
        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("background-color: rgba(255, 255, 255, 0.1); height: 1px; margin: 16px 0;")
        layout.addWidget(separator)
        
        # Navigation items
        nav_items = [
            ("dashboard", "🏠 Tableau de bord"),
            ("products", "📦 Produits"),
            ("sales", "🛒 Ventes"),
            ("stats", "📈 Statistiques"),
            ("settings", "⚙️ Paramètres")
        ]
        
        for page_id, text in nav_items:
            btn = self.create_nav_button(page_id, text)
            self.nav_buttons[page_id] = btn
            layout.addWidget(btn)
        
        layout.addStretch()
        self.setLayout(layout)
        
        # Définir la page active par défaut
        self.set_active_page("dashboard")
    
    def create_nav_button(self, page_id: str, text: str):
        """Créer un bouton de navigation"""
        if FLUENT_AVAILABLE:
            btn = FluentButton(text)
        else:
            btn = QPushButton(text)
            btn.setProperty("class", "secondary")
        
        btn.setMinimumHeight(40)
        btn.clicked.connect(lambda: self.on_nav_clicked(page_id))
        
        return btn
    
    def on_nav_clicked(self, page_id: str):
        """Gérer le clic sur navigation"""
        self.set_active_page(page_id)
        self.page_changed.emit(page_id)
    
    def set_active_page(self, page_id: str):
        """Définir la page active"""
        self.current_page = page_id
        
        # Mettre à jour l'apparence des boutons
        for pid, btn in self.nav_buttons.items():
            if pid == page_id:
                if not FLUENT_AVAILABLE:
                    btn.setProperty("class", "primary")
            else:
                if not FLUENT_AVAILABLE:
                    btn.setProperty("class", "secondary")
            
            # Forcer la mise à jour du style
            btn.style().unpolish(btn)
            btn.style().polish(btn)
