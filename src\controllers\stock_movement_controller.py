"""
Contrôleur pour la gestion des mouvements de stock
"""

from utils.logger import setup_logger
from datetime import datetime


class StockMovementController:
    """Contrôleur pour la gestion des mouvements de stock"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
        
        self.logger.info("StockMovementController initialisé")
    
    def get_statistics(self):
        """Obtenir les statistiques des mouvements"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'total_movements': 0, 'entries': 0, 'exits': 0}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'total_movements': 0, 'entries': 0, 'exits': 0}
            
            # Total des mouvements
            cursor.execute("SELECT COUNT(*) FROM movements")
            result = cursor.fetchone()
            total = result[0] if result else 0
            
            # Entrées
            cursor.execute("SELECT COUNT(*) FROM movements WHERE type_mouvement = 'entrée'")
            result = cursor.fetchone()
            entries = result[0] if result else 0
            
            # Sorties
            cursor.execute("SELECT COUNT(*) FROM movements WHERE type_mouvement = 'sortie'")
            result = cursor.fetchone()
            exits = result[0] if result else 0
            
            return {
                'total_movements': total,
                'entries': entries,
                'exits': exits
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {'total_movements': 0, 'entries': 0, 'exits': 0}
    
    def get_recent_movements(self, limit=10):
        """Obtenir les mouvements récents"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT m.*, a.nom as article_nom
                FROM movements m
                LEFT JOIN articles a ON m.article_id = a.id
                ORDER BY m.date_mouvement DESC
                LIMIT ?
            """, (limit,))
            
            movements = cursor.fetchall()
            if movements:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in movements]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements récents: {e}")
            return []
    
    def add_movement(self, article_id, type_mouvement, quantite, commentaire=""):
        """Ajouter un mouvement de stock"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return False
            
            cursor = self.db_manager.cursor
            if not cursor:
                return False
            
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO movements (article_id, type_mouvement, quantite, date_mouvement, commentaire)
                VALUES (?, ?, ?, ?, ?)
            """, (article_id, type_mouvement, quantite, now, commentaire))
            
            self.db_manager.connection.commit()
            return True
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout du mouvement: {e}")
            return False
    
    def get_all_movements(self):
        """Récupérer tous les mouvements"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT m.*, a.nom as article_nom
                FROM movements m
                LEFT JOIN articles a ON m.article_id = a.id
                ORDER BY m.date_mouvement DESC
            """)
            
            movements = cursor.fetchall()
            if movements:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in movements]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des mouvements: {e}")
            return []
