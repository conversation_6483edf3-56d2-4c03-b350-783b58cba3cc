#!/usr/bin/env python3
"""
Script pour créer des données de test pour les fournisseurs
"""

import sys
import os

# Ajouter src au path
sys.path.insert(0, 'src')

def create_suppliers_test_data():
    """Créer des données de test pour les fournisseurs"""
    print("🏭 Création de données de test pour les fournisseurs...")
    
    try:
        from database.manager import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        
        print("✅ Base de données initialisée")
        
        # Créer des fournisseurs de test avec évaluations
        suppliers = [
            {
                'name': 'TechDistrib France',
                'contact_person': '<PERSON><PERSON><PERSON>',
                'email': '<EMAIL>',
                'phone': '01 42 56 78 90',
                'address': '123 Avenue de la Technologie\n92000 Nanterre',
                'rating': 5,
                'notes': 'Excellent fournisseur, livraisons rapides'
            },
            {
                'name': 'Informatique Solutions',
                'contact_person': '<PERSON>',
                'email': '<EMAIL>',
                'phone': '04 78 90 12 34',
                'address': '45 Rue de l\'Innovation\n69000 Lyon',
                'rating': 4,
                'notes': 'Bon rapport qualité-prix, service client réactif'
            },
            {
                'name': 'Bureau & Mobilier Pro',
                'contact_person': 'Pierre Leroy',
                'email': '<EMAIL>',
                'phone': '02 40 56 78 90',
                'address': '78 Boulevard du Commerce\n44000 Nantes',
                'rating': 3,
                'notes': 'Délais parfois longs mais produits de qualité'
            },
            {
                'name': 'Papeterie Centrale',
                'contact_person': 'Sophie Bernard',
                'email': '<EMAIL>',
                'phone': '05 61 23 45 67',
                'address': '12 Place du Marché\n31000 Toulouse',
                'rating': 4,
                'notes': 'Spécialiste fournitures bureau, prix compétitifs'
            },
            {
                'name': 'ElectroDistrib',
                'contact_person': 'Michel Rousseau',
                'email': '<EMAIL>',
                'phone': '03 88 12 34 56',
                'address': '89 Rue de l\'Électronique\n67000 Strasbourg',
                'rating': 5,
                'notes': 'Fournisseur premium, garanties étendues'
            },
            {
                'name': 'Alimentaire Gourmet',
                'contact_person': 'Catherine Moreau',
                'email': '<EMAIL>',
                'phone': '04 91 67 89 01',
                'address': '34 Avenue des Saveurs\n13000 Marseille',
                'rating': 4,
                'notes': 'Produits de qualité, certifications bio'
            },
            {
                'name': 'Import-Export Global',
                'contact_person': 'François Petit',
                'email': '<EMAIL>',
                'phone': '01 45 67 89 01',
                'address': '456 Rue du Commerce International\n75015 Paris',
                'rating': 2,
                'notes': 'Prix attractifs mais qualité variable'
            },
            {
                'name': 'Équipements Professionnels',
                'contact_person': 'Isabelle Garcia',
                'email': '<EMAIL>',
                'phone': '04 76 89 01 23',
                'address': '67 Cours de l\'Industrie\n38000 Grenoble',
                'rating': 5,
                'notes': 'Matériel professionnel haut de gamme'
            },
            {
                'name': 'Fournitures Express',
                'contact_person': 'David Lopez',
                'email': '<EMAIL>',
                'phone': '02 99 12 34 56',
                'address': '23 Rue de la Rapidité\n35000 Rennes',
                'rating': 3,
                'notes': 'Livraisons express mais prix élevés'
            },
            {
                'name': 'Eco-Fournisseurs',
                'contact_person': 'Anne Lefebvre',
                'email': '<EMAIL>',
                'phone': '05 56 78 90 12',
                'address': '90 Avenue du Développement Durable\n33000 Bordeaux',
                'rating': 4,
                'notes': 'Spécialiste produits écologiques et durables'
            }
        ]
        
        for supplier_data in suppliers:
            supplier_id = db.create_supplier(supplier_data)
            if supplier_id:
                rating = supplier_data['rating']
                stars = "★" * rating + "☆" * (5 - rating)
                print(f"✅ Fournisseur créé: {supplier_data['name']} - {stars} ({rating}/5)")
            else:
                print(f"❌ Erreur création fournisseur: {supplier_data['name']}")
        
        print(f"\n🎉 Données de test fournisseurs créées avec succès !")
        print(f"🏭 {len(suppliers)} fournisseurs créés")
        
        # Statistiques des évaluations
        ratings_count = {}
        for supplier in suppliers:
            rating = supplier['rating']
            ratings_count[rating] = ratings_count.get(rating, 0) + 1
        
        print("\n📊 Répartition des évaluations:")
        for rating in sorted(ratings_count.keys(), reverse=True):
            stars = "★" * rating + "☆" * (5 - rating)
            count = ratings_count[rating]
            print(f"   • {stars} ({rating}/5): {count} fournisseur{'s' if count > 1 else ''}")
        
        avg_rating = sum(s['rating'] for s in suppliers) / len(suppliers)
        print(f"\n⭐ Évaluation moyenne: {avg_rating:.1f}/5")
        
        print("\n🚀 Vous pouvez maintenant tester le module Fournisseurs !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données de test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("🏭 Script de Création de Données de Test - Fournisseurs")
    print("=" * 65)
    
    success = create_suppliers_test_data()
    
    if success:
        print("\n✅ SUCCÈS ! Données de test fournisseurs créées")
        print("\n📋 Prochaines étapes:")
        print("   1. Lancez l'application: python main.py")
        print("   2. Connectez-vous: admin / admin123")
        print("   3. Allez dans le module Fournisseurs")
        print("   4. Testez toutes les fonctionnalités:")
        print("      • Visualisation des 10 fournisseurs")
        print("      • Recherche par nom, contact ou email")
        print("      • Ajout d'un nouveau fournisseur")
        print("      • Modification avec système d'évaluation")
        print("      • Suppression d'un fournisseur")
        print("      • Système d'étoiles 1-5")
        return True
    else:
        print("\n❌ ÉCHEC de la création des données")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
