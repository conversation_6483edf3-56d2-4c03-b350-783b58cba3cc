"""
Fenêtre principale de l'application GSlim
Interface moderne avec PyQt5 et Fluent Widgets
"""

from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QMenuBar, QStatusBar, QFrame, QSplitter
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

try:
    from qfluentwidgets import (
        NavigationInterface, NavigationItemPosition, FluentIcon,
        PushButton, TitleLabel, CardWidget, setTheme, Theme
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from config.settings import config
from utils.logger import setup_logger
from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
from widgets.theme_selector import show_theme_selector
from .dashboard_window import DashboardWindow
from .articles_window import ArticlesWindow
from .suppliers_window import SuppliersWindow
from .movements_window import MovementsWindow
from .orders_window import OrdersWindow
from .reports_window import ReportsWindow


class MainWindow(QMainWindow):
    """Fenêtre principale de l'application"""
    
    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)

        # Variables pour les modules
        self.current_module = None
        self.modules = {}

        # Gestionnaire de thèmes
        self.theme_manager = get_theme_manager()

        # Configuration de la fenêtre
        self.setWindowTitle(f"{config.APP_NAME} v{config.APP_VERSION}")
        self.setMinimumSize(config.MIN_WIDTH, config.MIN_HEIGHT)
        self.resize(config.WINDOW_WIDTH, config.WINDOW_HEIGHT)

        # Appliquer le thème actuel
        self._apply_current_theme()

        # Centrer la fenêtre
        self._center_window()

        # Initialiser l'interface
        self._init_ui()

        # Connecter les signaux
        self._connect_signals()
    
    def _center_window(self):
        """Centrer la fenêtre sur l'écran"""
        from PyQt5.QtWidgets import QDesktopWidget
        
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2
        self.move(x, y)
    
    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Créer la navigation latérale
        self._create_navigation(main_layout)
        
        # Créer la zone de contenu principal
        self._create_content_area(main_layout)
        
        central_widget.setLayout(main_layout)
        
        # Créer la barre de menu
        self._create_menu_bar()
        
        # Créer la barre de statut
        self._create_status_bar()
    
    def _create_navigation(self, layout):
        """Créer la navigation latérale"""
        if FLUENT_AVAILABLE:
            # Utiliser NavigationInterface de Fluent Widgets
            self.navigation = NavigationInterface()
            self.navigation.setFixedWidth(250)
            
            # Ajouter les éléments de navigation
            self.navigation.addItem(
                routeKey="dashboard",
                icon=FluentIcon.HOME,
                text="Tableau de bord",
                onClick=lambda: self._switch_page("dashboard")
            )
            
            self.navigation.addItem(
                routeKey="articles",
                icon=FluentIcon.TAG,
                text="Articles",
                onClick=lambda: self._switch_page("articles")
            )
            
            self.navigation.addItem(
                routeKey="suppliers",
                icon=FluentIcon.PEOPLE,
                text="Fournisseurs",
                onClick=lambda: self._switch_page("suppliers")
            )
            
            self.navigation.addItem(
                routeKey="movements",
                icon=FluentIcon.SYNC,
                text="Mouvements",
                onClick=lambda: self._switch_page("movements")
            )
            
            self.navigation.addItem(
                routeKey="orders",
                icon=FluentIcon.SHOPPING_CART,
                text="Commandes",
                onClick=lambda: self._switch_page("orders")
            )
            
            self.navigation.addItem(
                routeKey="reports",
                icon=FluentIcon.DOCUMENT,
                text="Rapports",
                onClick=lambda: self._switch_page("reports")
            )
            
            # Éléments en bas
            self.navigation.addItem(
                routeKey="settings",
                icon=FluentIcon.SETTING,
                text="Paramètres",
                onClick=lambda: self._switch_page("settings"),
                position=NavigationItemPosition.BOTTOM
            )
            
            layout.addWidget(self.navigation)
            
        else:
            # Navigation simple avec PyQt5
            nav_frame = QFrame()
            nav_frame.setFixedWidth(250)
            nav_frame.setProperty("class", "navigation")
            
            nav_layout = QVBoxLayout()
            nav_layout.setContentsMargins(10, 10, 10, 10)
            nav_layout.setSpacing(5)
            
            # Titre de navigation
            nav_title = QLabel("Navigation")
            nav_title.setProperty("class", "subtitle")
            nav_layout.addWidget(nav_title)
            
            # Boutons de navigation
            nav_buttons = [
                ("Tableau de bord", "dashboard"),
                ("Articles", "articles"),
                ("Fournisseurs", "suppliers"),
                ("Mouvements", "movements"),
                ("Commandes", "orders"),
                ("Rapports", "reports"),
                ("Paramètres", "settings")
            ]
            
            self.nav_buttons = {}
            for text, key in nav_buttons:
                btn = QPushButton(text)
                btn.setProperty("class", "nav-button")
                btn.clicked.connect(lambda checked, k=key: self._switch_page(k))
                nav_layout.addWidget(btn)
                self.nav_buttons[key] = btn
            
            nav_layout.addStretch()
            nav_frame.setLayout(nav_layout)
            layout.addWidget(nav_frame)
    
    def _create_content_area(self, layout):
        """Créer la zone de contenu principal"""
        self.content_area = QFrame()
        self.content_area.setProperty("class", "content-area")
        
        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # En-tête de la page
        self._create_page_header(content_layout)
        
        # Zone de contenu dynamique
        self.dynamic_content = QFrame()
        self.dynamic_content.setProperty("class", "dynamic-content")
        content_layout.addWidget(self.dynamic_content)
        
        self.content_area.setLayout(content_layout)
        layout.addWidget(self.content_area)
        
        # Afficher le tableau de bord par défaut
        self._switch_page("dashboard")
    
    def _create_page_header(self, layout):
        """Créer l'en-tête de page"""
        header_layout = QHBoxLayout()
        
        # Titre de la page
        if FLUENT_AVAILABLE:
            self.page_title = TitleLabel("Tableau de bord")
        else:
            self.page_title = QLabel("Tableau de bord")
            self.page_title.setProperty("class", "title")
        
        header_layout.addWidget(self.page_title)
        
        # Spacer
        header_layout.addStretch()
        
        # Boutons d'action
        if FLUENT_AVAILABLE:
            theme_btn = PushButton("Changer thème")
            theme_btn.setIcon(FluentIcon.PALETTE)
            logout_btn = PushButton("Déconnexion")
            logout_btn.setIcon(FluentIcon.POWER_BUTTON)
        else:
            theme_btn = QPushButton("Changer thème")
            logout_btn = QPushButton("Déconnexion")
        
        theme_btn.clicked.connect(self._show_theme_selector)
        logout_btn.clicked.connect(self.app_instance.logout)
        
        header_layout.addWidget(theme_btn)
        header_layout.addWidget(logout_btn)
        
        layout.addLayout(header_layout)
    
    def _create_menu_bar(self):
        """Créer la barre de menu"""
        menubar = self.menuBar()
        
        # Menu Fichier
        file_menu = menubar.addMenu("Fichier")
        file_menu.addAction("Nouveau", self._new_file)
        file_menu.addAction("Ouvrir", self._open_file)
        file_menu.addSeparator()
        file_menu.addAction("Quitter", self.app_instance.on_closing)
        
        # Menu Édition
        edit_menu = menubar.addMenu("Édition")
        edit_menu.addAction("Préférences", self._show_preferences)
        
        # Menu Aide
        help_menu = menubar.addMenu("Aide")
        help_menu.addAction("À propos", self._show_about)
    
    def _create_status_bar(self):
        """Créer la barre de statut"""
        status_bar = self.statusBar()
        
        # Informations utilisateur
        user = self.app_instance.get_current_user()
        user_info = f"Connecté en tant que: {user['username']} ({user['role']})"
        status_bar.showMessage(user_info)
    
    def _switch_page(self, page_key):
        """Changer de page"""
        self.logger.info(f"Changement vers la page: {page_key}")
        
        # Mettre à jour le titre
        page_titles = {
            "dashboard": "Tableau de bord",
            "articles": "Gestion des articles",
            "suppliers": "Gestion des fournisseurs",
            "movements": "Mouvements de stock",
            "orders": "Gestion des commandes",
            "reports": "Rapports et analyses",
            "settings": "Paramètres"
        }
        
        self.page_title.setText(page_titles.get(page_key, "Page inconnue"))
        
        # Ici, on chargerait le contenu spécifique de chaque page
        # Pour l'instant, on affiche juste un message
        self._show_placeholder_content(page_key)
    
    def _show_placeholder_content(self, page_key):
        """Afficher le contenu de la page"""
        # Cacher tous les modules existants
        for module_key, module_widget in self.modules.items():
            if module_widget and hasattr(module_widget, 'hide'):
                module_widget.hide()

        # Obtenir ou créer le layout
        layout = self.dynamic_content.layout()
        if not layout:
            layout = QVBoxLayout()
            self.dynamic_content.setLayout(layout)

        # Charger le module approprié
        if page_key == "dashboard":
            self._show_dashboard_module(layout)
        elif page_key == "articles":
            self._show_articles_module(layout)
        elif page_key == "suppliers":
            self._show_suppliers_module(layout)
        elif page_key == "movements":
            self._show_movements_module(layout)
        elif page_key == "orders":
            self._show_orders_module(layout)
        elif page_key == "reports":
            self._show_reports_module(layout)
        else:
            # Afficher un message temporaire pour les autres modules
            if FLUENT_AVAILABLE:
                placeholder = CardWidget()
            else:
                placeholder = QFrame()
                placeholder.setProperty("class", "card")

            placeholder_layout = QVBoxLayout()

            message = QLabel(f"Module '{page_key}' en cours de développement")
            message.setAlignment(Qt.AlignCenter)
            message.setProperty("class", "subtitle")

            placeholder_layout.addWidget(message)
            placeholder.setLayout(placeholder_layout)

            layout.addWidget(placeholder)
            layout.addStretch()

    def _show_articles_module(self, layout):
        """Afficher le module de gestion des articles"""
        try:
            # Créer ou récupérer le module articles
            if "articles" not in self.modules or self.modules["articles"] is None:
                self.modules["articles"] = ArticlesWindow(self.app_instance)
                layout.addWidget(self.modules["articles"])

            articles_module = self.modules["articles"]
            articles_module.show()
            self.current_module = "articles"

            self.logger.info("Module articles affiché")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage du module articles: {e}")

            # Afficher un message d'erreur
            error_label = QLabel(f"Erreur lors du chargement du module articles:\n{e}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px;")
            layout.addWidget(error_label)

    def _show_suppliers_module(self, layout):
        """Afficher le module de gestion des fournisseurs"""
        try:
            # Créer ou récupérer le module fournisseurs
            if "suppliers" not in self.modules or self.modules["suppliers"] is None:
                self.modules["suppliers"] = SuppliersWindow(self.app_instance)
                layout.addWidget(self.modules["suppliers"])

            suppliers_module = self.modules["suppliers"]
            suppliers_module.show()
            self.current_module = "suppliers"

            self.logger.info("Module fournisseurs affiché")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage du module fournisseurs: {e}")

            # Afficher un message d'erreur
            error_label = QLabel(f"Erreur lors du chargement du module fournisseurs:\n{e}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px;")
            layout.addWidget(error_label)

    def _show_dashboard_module(self, layout):
        """Afficher le module tableau de bord"""
        try:
            # Vérifier si le module existe et est valide
            dashboard_exists = False
            if "dashboard" in self.modules:
                try:
                    # Tester si le widget est encore valide
                    self.modules["dashboard"].isVisible()
                    dashboard_exists = True
                except (RuntimeError, AttributeError):
                    # Le widget a été supprimé par Qt
                    dashboard_exists = False

            # Créer le module si nécessaire
            if not dashboard_exists:
                self.modules["dashboard"] = DashboardWindow(self.app_instance.get_database_manager())

                # Connecter les signaux de navigation
                self.modules["dashboard"].navigate_to_articles.connect(lambda: self._switch_page("articles"))
                self.modules["dashboard"].navigate_to_suppliers.connect(lambda: self._switch_page("suppliers"))
                self.modules["dashboard"].navigate_to_movements.connect(lambda: self._switch_page("movements"))
                self.modules["dashboard"].navigate_to_orders.connect(lambda: self._switch_page("orders"))

                layout.addWidget(self.modules["dashboard"])

            dashboard_module = self.modules["dashboard"]
            dashboard_module.show()
            self.current_module = "dashboard"

            self.logger.info("Module tableau de bord affiché")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage du module tableau de bord: {e}")

            # Afficher un message d'erreur
            error_label = QLabel(f"Erreur lors du chargement du module tableau de bord:\n{e}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px;")
            layout.addWidget(error_label)

    def _show_movements_module(self, layout):
        """Afficher le module des mouvements de stock"""
        try:
            # Créer ou récupérer le module mouvements
            if "movements" not in self.modules or self.modules["movements"] is None:
                self.modules["movements"] = MovementsWindow(self.app_instance.get_database_manager())
                layout.addWidget(self.modules["movements"])

            movements_module = self.modules["movements"]
            movements_module.show()
            self.current_module = "movements"

            self.logger.info("Module mouvements affiché")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage du module mouvements: {e}")

            # Afficher un message d'erreur
            error_label = QLabel(f"Erreur lors du chargement du module mouvements:\n{e}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px;")
            layout.addWidget(error_label)

    def _show_orders_module(self, layout):
        """Afficher le module des commandes"""
        try:
            # Créer ou récupérer le module commandes
            if "orders" not in self.modules or self.modules["orders"] is None:
                self.modules["orders"] = OrdersWindow(self.app_instance.get_database_manager())
                layout.addWidget(self.modules["orders"])

            orders_module = self.modules["orders"]
            orders_module.show()
            self.current_module = "orders"

            self.logger.info("Module commandes affiché")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage du module commandes: {e}")

            # Afficher un message d'erreur
            error_label = QLabel(f"Erreur lors du chargement du module commandes:\n{e}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px;")
            layout.addWidget(error_label)

    def _show_reports_module(self, layout):
        """Afficher le module des rapports"""
        try:
            # Créer ou récupérer le module rapports
            if "reports" not in self.modules or self.modules["reports"] is None:
                self.modules["reports"] = ReportsWindow(self.app_instance.get_database_manager())
                layout.addWidget(self.modules["reports"])

            reports_module = self.modules["reports"]
            reports_module.show()
            self.current_module = "reports"

            self.logger.info("Module rapports affiché")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage du module rapports: {e}")

            # Afficher un message d'erreur
            error_label = QLabel(f"Erreur lors du chargement du module rapports:\n{e}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #dc3545; padding: 20px;")
            layout.addWidget(error_label)

    def _connect_signals(self):
        """Connecter les signaux"""
        # Connecter le signal de changement de thème
        self.theme_manager.theme_changed.connect(self._on_theme_changed)

    def _apply_current_theme(self):
        """Appliquer le thème actuel"""
        current_theme, current_mode = self.theme_manager.get_current_theme()
        self.theme_manager.set_theme(current_theme, current_mode)

    def _show_theme_selector(self):
        """Afficher le sélecteur de thèmes"""
        if show_theme_selector(self):
            self.logger.info("Thème changé via le sélecteur")

    def _on_theme_changed(self, theme_type: str, theme_mode: str):
        """Gérer le changement de thème"""
        self.logger.info(f"Thème changé: {theme_type} ({theme_mode})")

        # Mettre à jour le titre de la fenêtre avec le thème actuel
        theme_info = self.theme_manager.get_theme_info(ThemeType(theme_type))
        theme_name = theme_info.get("name", theme_type.title())
        mode_name = "Sombre" if theme_mode == "dark" else "Clair"

        self.setWindowTitle(f"{config.APP_NAME} v{config.APP_VERSION} - {theme_name} ({mode_name})")
    
    # Méthodes pour les actions de menu
    def _new_file(self):
        """Nouveau fichier"""
        pass
    
    def _open_file(self):
        """Ouvrir fichier"""
        pass
    
    def _show_preferences(self):
        """Afficher les préférences"""
        pass
    
    def _show_about(self):
        """Afficher À propos"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.about(
            self,
            "À propos",
            f"{config.APP_NAME} v{config.APP_VERSION}\n\n"
            "Application de gestion de stock moderne\n"
            "Développée avec PyQt5 et Fluent Widgets"
        )
    
    def closeEvent(self, event):
        """Gérer la fermeture de la fenêtre"""
        self.app_instance.on_closing()
        event.accept()
