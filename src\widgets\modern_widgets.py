"""
Widgets modernes personnalisés pour GSlim
Composants UI avec animations, effets visuels et design moderne
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QFrame, QGraphicsDropShadowEffect, QGridLayout, QSizePolicy,
    QProgressBar, QGraphicsOpacityEffect, QApplication
)
from PyQt5.QtCore import (
    Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect,
    QTimer, QParallelAnimationGroup, QSequentialAnimationGroup,
    QAbstractAnimation, QPoint, QSize
)
from PyQt5.QtGui import (
    QFont, QPixmap, QPainter, QColor, QPen, QBrush,
    QPainterPath, QLinearGradient, QRadialGradient, QPalette
)

try:
    from qfluentwidgets import (
        CardWidget, IconWidget, <PERSON>luent<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>abe<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>B<PERSON>Label
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from styles.modern_theme import ModernTheme


class ModernStatCard(QWidget):
    """Carte de statistique moderne avec animations"""
    
    clicked = pyqtSignal()
    
    def __init__(self, title: str, value: str, subtitle: str = "", 
                 card_type: str = "default", icon: str = None):
        super().__init__()
        self.card_type = card_type
        self.setup_ui(title, value, subtitle, icon)
        self.setup_animations()
    
    def setup_ui(self, title: str, value: str, subtitle: str, icon: str):
        """Configurer l'interface utilisateur"""
        self.setFixedSize(280, 140)
        self.setCursor(Qt.PointingHandCursor)
        
        # Appliquer la classe CSS selon le type
        if self.card_type == "primary":
            self.setProperty("class", "stat-card-primary")
        elif self.card_type == "success":
            self.setProperty("class", "stat-card-success")
        elif self.card_type == "warning":
            self.setProperty("class", "stat-card-warning")
        elif self.card_type == "error":
            self.setProperty("class", "stat-card-error")
        else:
            self.setProperty("class", "stat-card")
        
        # Layout principal
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(8)
        
        # Header avec icône et titre
        header_layout = QHBoxLayout()
        header_layout.setSpacing(12)
        
        # Icône (si disponible)
        if icon and FLUENT_AVAILABLE:
            icon_widget = IconWidget(getattr(FluentIcon, icon, FluentIcon.INFO))
            icon_widget.setFixedSize(24, 24)
            header_layout.addWidget(icon_widget)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = CaptionLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setProperty("class", "stat-title")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Valeur principale
        if FLUENT_AVAILABLE:
            value_label = StrongBodyLabel(value)
            value_label.setStyleSheet("font-size: 28px; font-weight: 700;")
        else:
            value_label = QLabel(value)
            value_label.setProperty("class", "stat-value")
        
        layout.addWidget(value_label)
        
        # Sous-titre (si fourni)
        if subtitle:
            if FLUENT_AVAILABLE:
                subtitle_label = CaptionLabel(subtitle)
            else:
                subtitle_label = QLabel(subtitle)
                subtitle_label.setProperty("class", "stat-subtitle")
            
            layout.addWidget(subtitle_label)
        
        layout.addStretch()
        self.setLayout(layout)

        # Stocker les références aux labels pour les animations
        self.value_label = value_label
        self.title_label = title_label
        if subtitle:
            self.subtitle_label = subtitle_label

        # Effet d'ombre
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(15)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(4)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(self.shadow_effect)
    
    def setup_animations(self):
        """Configurer les animations"""
        # Animation de position
        self.position_animation = QPropertyAnimation(self, b"geometry")
        self.position_animation.setDuration(200)
        self.position_animation.setEasingCurve(QEasingCurve.OutCubic)

        # Animation de valeur (pour les changements de données)
        self.value_timer = QTimer()
        self.value_timer.timeout.connect(self.animate_value_step)
        self.current_display_value = 0
        self.target_value = 0
        self.animation_steps = 0
        self.current_step = 0
    
    def enterEvent(self, event):
        """Animation au survol"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() - 2,
            current_rect.width(),
            current_rect.height()
        )
        
        self.position_animation.setStartValue(current_rect)
        self.position_animation.setEndValue(new_rect)
        self.position_animation.start()
        
        # Augmenter l'ombre
        self.shadow_effect.setBlurRadius(20)
        self.shadow_effect.setYOffset(6)
        self.shadow_effect.setColor(QColor(0, 0, 0, 50))
        
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """Animation à la sortie du survol"""
        current_rect = self.geometry()
        new_rect = QRect(
            current_rect.x(),
            current_rect.y() + 2,
            current_rect.width(),
            current_rect.height()
        )
        
        self.position_animation.setStartValue(current_rect)
        self.position_animation.setEndValue(new_rect)
        self.position_animation.start()
        
        # Réduire l'ombre
        self.shadow_effect.setBlurRadius(15)
        self.shadow_effect.setYOffset(4)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """Gérer le clic"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)

    def update_value(self, new_value: str, animate: bool = True):
        """Mettre à jour la valeur avec animation optionnelle"""
        if animate and new_value.isdigit() and hasattr(self, 'value_label'):
            old_value = self.value_label.text()
            if old_value.isdigit():
                self.current_display_value = int(old_value)
                self.target_value = int(new_value)
                self.animation_steps = 30  # 30 étapes d'animation
                self.current_step = 0
                self.value_timer.start(50)  # 50ms par étape
            else:
                self.value_label.setText(new_value)
        else:
            if hasattr(self, 'value_label'):
                self.value_label.setText(new_value)

    def animate_value_step(self):
        """Étape d'animation de la valeur"""
        if self.current_step >= self.animation_steps:
            self.value_timer.stop()
            self.value_label.setText(str(self.target_value))
            return

        # Interpolation avec easing
        progress = self.current_step / self.animation_steps
        eased_progress = 1 - (1 - progress) ** 3  # Ease-out cubic

        current_value = int(self.current_display_value +
                          (self.target_value - self.current_display_value) * eased_progress)

        self.value_label.setText(str(current_value))
        self.current_step += 1


class ModernActionCard(QWidget):
    """Carte d'action moderne avec boutons"""
    
    def __init__(self, title: str, description: str, actions: list):
        super().__init__()
        self.setup_ui(title, description, actions)
    
    def setup_ui(self, title: str, description: str, actions: list):
        """Configurer l'interface utilisateur"""
        self.setProperty("class", "card")
        
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        # Titre
        if FLUENT_AVAILABLE:
            title_label = StrongBodyLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setProperty("class", "subtitle")
        
        layout.addWidget(title_label)
        
        # Description
        if FLUENT_AVAILABLE:
            desc_label = BodyLabel(description)
        else:
            desc_label = QLabel(description)
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet("color: #64748b; line-height: 1.5;")
        
        layout.addWidget(desc_label)
        
        # Boutons d'action
        if actions:
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(12)
            
            for action in actions:
                if FLUENT_AVAILABLE:
                    btn = PushButton(action['text'])
                    if 'icon' in action:
                        btn.setIcon(getattr(FluentIcon, action['icon'], FluentIcon.ACCEPT))
                else:
                    btn = QPushButton(action['text'])
                
                if 'style' in action:
                    btn.setProperty("class", action['style'])
                
                if 'callback' in action:
                    btn.clicked.connect(action['callback'])
                
                buttons_layout.addWidget(btn)
            
            buttons_layout.addStretch()
            layout.addLayout(buttons_layout)
        
        self.setLayout(layout)


class ModernProgressCard(QWidget):
    """Carte de progression moderne"""
    
    def __init__(self, title: str, current: int, total: int, color: str = "#2563eb"):
        super().__init__()
        self.current = current
        self.total = total
        self.color = color
        self.setup_ui(title)
    
    def setup_ui(self, title: str):
        """Configurer l'interface utilisateur"""
        self.setProperty("class", "card")
        self.setFixedHeight(120)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(12)
        
        # Header avec titre et pourcentage
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title_label = BodyLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: 600; color: #1e293b;")
        
        percentage = int((self.current / self.total * 100)) if self.total > 0 else 0
        
        if FLUENT_AVAILABLE:
            percent_label = CaptionLabel(f"{percentage}%")
        else:
            percent_label = QLabel(f"{percentage}%")
            percent_label.setStyleSheet("color: #64748b; font-weight: 500;")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(percent_label)
        
        layout.addLayout(header_layout)
        
        # Barre de progression
        progress_container = QWidget()
        progress_container.setFixedHeight(8)
        progress_container.setStyleSheet(f"""
            QWidget {{
                background-color: #e2e8f0;
                border-radius: 4px;
            }}
        """)
        
        # Barre de progression remplie
        progress_fill = QWidget(progress_container)
        fill_width = int((self.current / self.total * progress_container.width())) if self.total > 0 else 0
        progress_fill.setGeometry(0, 0, fill_width, 8)
        progress_fill.setStyleSheet(f"""
            QWidget {{
                background-color: {self.color};
                border-radius: 4px;
            }}
        """)
        
        layout.addWidget(progress_container)
        
        # Texte de progression
        progress_text = f"{self.current} / {self.total}"
        if FLUENT_AVAILABLE:
            progress_label = CaptionLabel(progress_text)
        else:
            progress_label = QLabel(progress_text)
            progress_label.setStyleSheet("color: #64748b; font-size: 12px;")
        
        layout.addWidget(progress_label)
        
        self.setLayout(layout)


class ModernAlertCard(QWidget):
    """Carte d'alerte moderne"""
    
    def __init__(self, title: str, message: str, alert_type: str = "info"):
        super().__init__()
        self.alert_type = alert_type
        self.setup_ui(title, message)
    
    def setup_ui(self, title: str, message: str):
        """Configurer l'interface utilisateur"""
        self.setProperty("class", "card")
        
        # Couleurs selon le type d'alerte
        colors = {
            "info": {"bg": "#dbeafe", "border": "#3b82f6", "text": "#1e40af"},
            "success": {"bg": "#dcfce7", "border": "#10b981", "text": "#065f46"},
            "warning": {"bg": "#fef3c7", "border": "#f59e0b", "text": "#92400e"},
            "error": {"bg": "#fee2e2", "border": "#ef4444", "text": "#991b1b"}
        }
        
        color_scheme = colors.get(self.alert_type, colors["info"])
        
        self.setStyleSheet(f"""
            QWidget[class="card"] {{
                background-color: {color_scheme["bg"]};
                border-left: 4px solid {color_scheme["border"]};
                border-radius: 8px;
            }}
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(8)
        
        # Titre
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            font-weight: 600;
            color: {color_scheme["text"]};
            font-size: 14px;
        """)
        layout.addWidget(title_label)
        
        # Message
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setStyleSheet(f"""
            color: {color_scheme["text"]};
            font-size: 13px;
            line-height: 1.4;
        """)
        layout.addWidget(message_label)
        
        self.setLayout(layout)


class AnimatedButton(QPushButton):
    """Bouton avec animations et effets visuels modernes"""

    def __init__(self, text: str, button_type: str = "primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.is_loading = False

        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        # Appliquer la classe CSS selon le type
        if self.button_type == "secondary":
            self.setProperty("class", "secondary")
        elif self.button_type == "outline":
            self.setProperty("class", "outline")
        elif self.button_type == "ghost":
            self.setProperty("class", "ghost")
        elif self.button_type == "success":
            self.setProperty("class", "success")
        elif self.button_type == "warning":
            self.setProperty("class", "warning")
        elif self.button_type == "danger":
            self.setProperty("class", "danger")

        # Taille par défaut
        self.setMinimumHeight(40)
        self.setMinimumWidth(100)

    def setup_animations(self):
        """Configurer les animations"""
        # Animation de pulsation pour le loading
        self.pulse_animation = QPropertyAnimation(self, b"geometry")
        self.pulse_animation.setDuration(1000)
        self.pulse_animation.setEasingCurve(QEasingCurve.InOutSine)
        self.pulse_animation.setLoopCount(-1)  # Boucle infinie

        # Animation de clic
        self.click_animation = QPropertyAnimation(self, b"geometry")
        self.click_animation.setDuration(100)
        self.click_animation.setEasingCurve(QEasingCurve.OutCubic)

    def set_loading(self, loading: bool):
        """Activer/désactiver l'état de chargement"""
        self.is_loading = loading

        if loading:
            self.setEnabled(False)
            self.setText("Chargement...")
            self.start_pulse_animation()
        else:
            self.setEnabled(True)
            self.stop_pulse_animation()

    def start_pulse_animation(self):
        """Démarrer l'animation de pulsation"""
        current_rect = self.geometry()
        expanded_rect = QRect(
            current_rect.x() - 2,
            current_rect.y() - 1,
            current_rect.width() + 4,
            current_rect.height() + 2
        )

        self.pulse_animation.setStartValue(current_rect)
        self.pulse_animation.setEndValue(expanded_rect)
        self.pulse_animation.start()

    def stop_pulse_animation(self):
        """Arrêter l'animation de pulsation"""
        self.pulse_animation.stop()

    def mousePressEvent(self, event):
        """Animation de clic"""
        if event.button() == Qt.LeftButton and not self.is_loading:
            current_rect = self.geometry()
            pressed_rect = QRect(
                current_rect.x() + 1,
                current_rect.y() + 1,
                current_rect.width() - 2,
                current_rect.height() - 2
            )

            self.click_animation.setStartValue(current_rect)
            self.click_animation.setEndValue(pressed_rect)
            self.click_animation.start()

        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """Animation de relâchement"""
        if event.button() == Qt.LeftButton and not self.is_loading:
            current_rect = self.geometry()
            released_rect = QRect(
                current_rect.x() - 1,
                current_rect.y() - 1,
                current_rect.width() + 2,
                current_rect.height() + 2
            )

            self.click_animation.setStartValue(current_rect)
            self.click_animation.setEndValue(released_rect)
            self.click_animation.start()

        super().mouseReleaseEvent(event)


class ModernProgressBar(QWidget):
    """Barre de progression moderne avec animations"""

    def __init__(self, minimum: int = 0, maximum: int = 100, parent=None):
        super().__init__(parent)
        self.minimum = minimum
        self.maximum = maximum
        self.current_value = minimum
        self.target_value = minimum

        self.setup_ui()
        self.setup_animations()

    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setFixedHeight(8)
        self.setMinimumWidth(200)

        # Couleurs du thème
        self.bg_color = ModernTheme.SECONDARY_200
        self.fill_color = ModernTheme.PRIMARY_600
        self.border_radius = 4

    def setup_animations(self):
        """Configurer les animations"""
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.animate_progress_step)
        self.animation_steps = 30
        self.current_step = 0

    def setValue(self, value: int, animate: bool = True):
        """Définir la valeur avec animation optionnelle"""
        value = max(self.minimum, min(self.maximum, value))

        if animate:
            self.target_value = value
            self.current_step = 0
            self.progress_timer.start(16)  # ~60 FPS
        else:
            self.current_value = value
            self.target_value = value
            self.update()

    def animate_progress_step(self):
        """Étape d'animation de la progression"""
        if self.current_step >= self.animation_steps:
            self.progress_timer.stop()
            self.current_value = self.target_value
            self.update()
            return

        # Interpolation avec easing
        progress = self.current_step / self.animation_steps
        eased_progress = 1 - (1 - progress) ** 3  # Ease-out cubic

        self.current_value = self.minimum + (self.target_value - self.minimum) * eased_progress
        self.current_step += 1
        self.update()

    def paintEvent(self, event):
        """Dessiner la barre de progression"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Arrière-plan
        bg_rect = self.rect()
        painter.setBrush(QBrush(QColor(self.bg_color)))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(bg_rect, self.border_radius, self.border_radius)

        # Progression
        if self.current_value > self.minimum:
            progress_ratio = (self.current_value - self.minimum) / (self.maximum - self.minimum)
            progress_width = int(bg_rect.width() * progress_ratio)

            progress_rect = QRect(0, 0, progress_width, bg_rect.height())
            painter.setBrush(QBrush(QColor(self.fill_color)))
            painter.drawRoundedRect(progress_rect, self.border_radius, self.border_radius)

        painter.end()
