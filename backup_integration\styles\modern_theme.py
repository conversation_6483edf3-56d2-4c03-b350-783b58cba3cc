"""
Thème moderne pour l'application GSlim
Couleurs et styles inspirés des design systems modernes (Material Design 3, Fluent Design)
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QPalette, QFont


class ModernTheme:
    """Classe pour gérer les thèmes modernes avec palette de couleurs étendue"""

    # === PALETTE DE COULEURS PRINCIPALES ===

    # Couleurs primaires (Bleu moderne inspiré de Material Design 3)
    PRIMARY_50 = "#eff6ff"
    PRIMARY_100 = "#dbeafe"
    PRIMARY_200 = "#bfdbfe"
    PRIMARY_300 = "#93c5fd"
    PRIMARY_400 = "#60a5fa"
    PRIMARY_500 = "#3b82f6"  # Couleur principale
    PRIMARY_600 = "#2563eb"
    PRIMARY_700 = "#1d4ed8"
    PRIMARY_800 = "#1e40af"
    PRIMARY_900 = "#1e3a8a"

    # Couleurs secondaires (Gris moderne)
    SECONDARY_50 = "#f8fafc"
    SECONDARY_100 = "#f1f5f9"
    SECONDARY_200 = "#e2e8f0"
    SECONDARY_300 = "#cbd5e1"
    SECONDARY_400 = "#94a3b8"
    SECONDARY_500 = "#64748b"
    SECONDARY_600 = "#475569"
    SECONDARY_700 = "#334155"
    SECONDARY_800 = "#1e293b"
    SECONDARY_900 = "#0f172a"

    # Couleurs d'accent et d'état
    ACCENT_COLOR = "#06b6d4"      # Cyan
    SUCCESS_COLOR = "#10b981"     # Vert émeraude
    WARNING_COLOR = "#f59e0b"     # Orange ambre
    ERROR_COLOR = "#ef4444"       # Rouge
    INFO_COLOR = "#3b82f6"        # Bleu info

    # === THÈME CLAIR ===

    # Couleurs de fond - Thème clair
    LIGHT_BACKGROUND_PRIMARY = "#ffffff"
    LIGHT_BACKGROUND_SECONDARY = "#f8fafc"
    LIGHT_BACKGROUND_TERTIARY = "#f1f5f9"
    LIGHT_BACKGROUND_ELEVATED = "#ffffff"

    # Couleurs de texte - Thème clair
    LIGHT_TEXT_PRIMARY = "#0f172a"
    LIGHT_TEXT_SECONDARY = "#475569"
    LIGHT_TEXT_MUTED = "#64748b"
    LIGHT_TEXT_DISABLED = "#94a3b8"

    # Couleurs de bordure - Thème clair
    LIGHT_BORDER_COLOR = "#e2e8f0"
    LIGHT_BORDER_HOVER = "#cbd5e1"
    LIGHT_BORDER_FOCUS = "#3b82f6"

    # === THÈME SOMBRE ===

    # Couleurs de fond - Thème sombre
    DARK_BACKGROUND_PRIMARY = "#0f172a"
    DARK_BACKGROUND_SECONDARY = "#1e293b"
    DARK_BACKGROUND_TERTIARY = "#334155"
    DARK_BACKGROUND_ELEVATED = "#1e293b"

    # Couleurs de texte - Thème sombre
    DARK_TEXT_PRIMARY = "#f8fafc"
    DARK_TEXT_SECONDARY = "#cbd5e1"
    DARK_TEXT_MUTED = "#94a3b8"
    DARK_TEXT_DISABLED = "#64748b"

    # Couleurs de bordure - Thème sombre
    DARK_BORDER_COLOR = "#334155"
    DARK_BORDER_HOVER = "#475569"
    DARK_BORDER_FOCUS = "#60a5fa"

    # === PROPRIÉTÉS VISUELLES ===

    # Rayons de bordure
    BORDER_RADIUS_SM = "6px"
    BORDER_RADIUS_MD = "8px"
    BORDER_RADIUS_LG = "12px"
    BORDER_RADIUS_XL = "16px"

    # Ombres
    SHADOW_SM = "0 1px 2px 0 rgba(0, 0, 0, 0.05)"
    SHADOW_MD = "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"
    SHADOW_LG = "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
    SHADOW_XL = "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"

    # Transitions
    TRANSITION_FAST = "150ms ease-in-out"
    TRANSITION_NORMAL = "250ms ease-in-out"
    TRANSITION_SLOW = "350ms ease-in-out"

    # === COMPATIBILITÉ AVEC L'ANCIEN SYSTÈME ===

    # Couleurs principales (pour compatibilité)
    PRIMARY_COLOR = PRIMARY_600
    SECONDARY_COLOR = SECONDARY_500
    SUCCESS_COLOR = "#10b981"
    WARNING_COLOR = "#f59e0b"
    ERROR_COLOR = "#ef4444"

    # Couleurs de fond (pour compatibilité)
    BACKGROUND_PRIMARY = LIGHT_BACKGROUND_PRIMARY
    BACKGROUND_SECONDARY = LIGHT_BACKGROUND_SECONDARY
    BACKGROUND_TERTIARY = LIGHT_BACKGROUND_TERTIARY

    # Couleurs de texte (pour compatibilité)
    TEXT_PRIMARY = LIGHT_TEXT_PRIMARY
    TEXT_SECONDARY = LIGHT_TEXT_SECONDARY
    TEXT_MUTED = LIGHT_TEXT_MUTED

    # Couleurs de bordure (pour compatibilité)
    BORDER_COLOR = LIGHT_BORDER_COLOR
    BORDER_HOVER = LIGHT_BORDER_HOVER
    
    @classmethod
    def get_theme_colors(cls, is_dark=False):
        """Retourner les couleurs du thème selon le mode"""
        if is_dark:
            return {
                'background_primary': cls.DARK_BACKGROUND_PRIMARY,
                'background_secondary': cls.DARK_BACKGROUND_SECONDARY,
                'background_tertiary': cls.DARK_BACKGROUND_TERTIARY,
                'background_elevated': cls.DARK_BACKGROUND_ELEVATED,
                'text_primary': cls.DARK_TEXT_PRIMARY,
                'text_secondary': cls.DARK_TEXT_SECONDARY,
                'text_muted': cls.DARK_TEXT_MUTED,
                'text_disabled': cls.DARK_TEXT_DISABLED,
                'border_color': cls.DARK_BORDER_COLOR,
                'border_hover': cls.DARK_BORDER_HOVER,
                'border_focus': cls.DARK_BORDER_FOCUS,
            }
        else:
            return {
                'background_primary': cls.LIGHT_BACKGROUND_PRIMARY,
                'background_secondary': cls.LIGHT_BACKGROUND_SECONDARY,
                'background_tertiary': cls.LIGHT_BACKGROUND_TERTIARY,
                'background_elevated': cls.LIGHT_BACKGROUND_ELEVATED,
                'text_primary': cls.LIGHT_TEXT_PRIMARY,
                'text_secondary': cls.LIGHT_TEXT_SECONDARY,
                'text_muted': cls.LIGHT_TEXT_MUTED,
                'text_disabled': cls.LIGHT_TEXT_DISABLED,
                'border_color': cls.LIGHT_BORDER_COLOR,
                'border_hover': cls.LIGHT_BORDER_HOVER,
                'border_focus': cls.LIGHT_BORDER_FOCUS,
            }

    @classmethod
    def get_modern_stylesheet(cls, is_dark=False):
        """Retourner le stylesheet moderne pour l'application"""
        colors = cls.get_theme_colors(is_dark)

        return f"""
        /* === STYLES GÉNÉRAUX === */
        QMainWindow {{
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: {cls.SECONDARY_800};
            font-family: 'Segoe UI', 'Inter', 'Arial', sans-serif;
            font-size: 14px;
        }}

        QWidget {{
            background-color: transparent;
            color: {cls.SECONDARY_800};
        }}

        /* === CONTENEUR PRINCIPAL === */
        QWidget[class="main-container"] {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 0px;
        }}

        QWidget[class="content-wrapper"] {{
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }}

        /* === NAVIGATION === */
        QWidget[class="navigation"] {{
            background-color: {colors['background_elevated']};
            border-right: 1px solid {colors['border_color']};
            box-shadow: {cls.SHADOW_SM};
        }}

        /* === CARTES MODERNES === */
        QWidget[class="card"] {{
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(99, 102, 241, 0.1);
            border-radius: 20px;
            padding: 24px;
            margin: 12px;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.1);
        }}

        QWidget[class="card"]:hover {{
            border-color: rgba(99, 102, 241, 0.3);
            box-shadow: 0 12px 40px rgba(99, 102, 241, 0.15);
            transform: translateY(-2px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}

        /* === CARTE PRINCIPALE (TABLEAU DE BORD) === */
        QWidget[class="dashboard-card"] {{
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: none;
            border-radius: 24px;
            padding: 32px;
            margin: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
        }}

        /* === HEADER MODERNE === */
        QWidget[class="header"] {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 20px;
        }}

        QLabel[class="header-title"] {{
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }}

        QLabel[class="header-subtitle"] {{
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 400;
        }}

        /* === CARTES DE STATISTIQUES === */
        QWidget[class="stat-card"] {{
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid rgba(99, 102, 241, 0.1);
            border-radius: 20px;
            padding: 24px;
            margin: 12px;
            min-height: 140px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
        }}

        QWidget[class="stat-card"]:hover {{
            transform: translateY(-4px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}

        QWidget[class="stat-card-primary"] {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
        }}

        QWidget[class="stat-card-success"] {{
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            box-shadow: 0 8px 32px rgba(17, 153, 142, 0.3);
        }}

        QWidget[class="stat-card-warning"] {{
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            box-shadow: 0 8px 32px rgba(240, 147, 251, 0.3);
        }}

        QWidget[class="stat-card-error"] {{
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            border: none;
            box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
        }}

        QWidget[class="stat-card-info"] {{
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            box-shadow: 0 8px 32px rgba(116, 185, 255, 0.3);
        }}

        QWidget[class="stat-card-accent"] {{
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
            color: white;
            border: none;
            box-shadow: 0 8px 32px rgba(253, 121, 168, 0.3);
        }}

        /* === TYPOGRAPHIE === */
        QLabel[class="title"] {{
            font-size: 32px;
            font-weight: 700;
            color: {colors['text_primary']};
            margin-bottom: 8px;
            letter-spacing: -0.5px;
        }}

        QLabel[class="subtitle"] {{
            font-size: 20px;
            font-weight: 600;
            color: {colors['text_secondary']};
            margin-bottom: 16px;
            letter-spacing: -0.25px;
        }}

        QLabel[class="heading"] {{
            font-size: 18px;
            font-weight: 600;
            color: {colors['text_primary']};
            margin-bottom: 12px;
        }}

        QLabel[class="body"] {{
            font-size: 14px;
            font-weight: 400;
            color: {colors['text_primary']};
            line-height: 1.5;
        }}

        QLabel[class="caption"] {{
            font-size: 12px;
            font-weight: 400;
            color: {colors['text_muted']};
        }}

        QLabel[class="stat-title"] {{
            font-size: 14px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 8px;
        }}

        QLabel[class="stat-value"] {{
            font-size: 36px;
            font-weight: 700;
            color: white;
            margin-bottom: 4px;
            letter-spacing: -1px;
        }}

        QLabel[class="stat-subtitle"] {{
            font-size: 12px;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.8);
        }}

        /* === BOUTONS MODERNES === */
        QPushButton {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px 28px;
            font-size: 14px;
            font-weight: 600;
            min-height: 20px;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }}

        QPushButton:hover {{
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }}

        QPushButton:pressed {{
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
            transform: translateY(0px);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }}

        QPushButton:disabled {{
            background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
            color: #94a3b8;
            transform: none;
            box-shadow: none;
        }}

        QPushButton[class="secondary"] {{
            background-color: {colors['background_tertiary']};
            color: {colors['text_primary']};
            border: 1px solid {colors['border_color']};
        }}

        QPushButton[class="secondary"]:hover {{
            background-color: {colors['border_hover']};
            border-color: {colors['border_hover']};
        }}

        QPushButton[class="outline"] {{
            background-color: transparent;
            color: {cls.PRIMARY_600};
            border: 2px solid {cls.PRIMARY_600};
        }}

        QPushButton[class="outline"]:hover {{
            background-color: {cls.PRIMARY_50};
            color: {cls.PRIMARY_700};
        }}

        QPushButton[class="ghost"] {{
            background-color: transparent;
            color: {colors['text_secondary']};
            border: none;
        }}

        QPushButton[class="ghost"]:hover {{
            background-color: {colors['background_tertiary']};
            color: {colors['text_primary']};
        }}

        QPushButton[class="success"] {{
            background-color: {cls.SUCCESS_COLOR};
        }}

        QPushButton[class="success"]:hover {{
            background-color: #059669;
        }}

        QPushButton[class="warning"] {{
            background-color: {cls.WARNING_COLOR};
        }}

        QPushButton[class="warning"]:hover {{
            background-color: #d97706;
        }}

        QPushButton[class="danger"] {{
            background-color: {cls.ERROR_COLOR};
        }}

        QPushButton[class="danger"]:hover {{
            background-color: #dc2626;
        }}

        QPushButton[class="small"] {{
            padding: 8px 16px;
            font-size: 12px;
            min-height: 16px;
        }}

        QPushButton[class="large"] {{
            padding: 16px 32px;
            font-size: 16px;
            min-height: 24px;
        }}

        /* === TABLES MODERNES === */
        QTableWidget {{
            background-color: {colors['background_primary']};
            border: 1px solid {colors['border_color']};
            border-radius: {cls.BORDER_RADIUS_MD};
            gridline-color: {colors['border_color']};
            selection-background-color: rgba(59, 130, 246, 0.1);
            alternate-background-color: {colors['background_secondary']};
        }}

        QTableWidget::item {{
            padding: 12px 16px;
            border-bottom: 1px solid {colors['border_color']};
            color: {colors['text_primary']};
        }}

        QTableWidget::item:selected {{
            background-color: rgba(59, 130, 246, 0.15);
            color: {colors['text_primary']};
        }}

        QTableWidget::item:hover {{
            background-color: {colors['background_tertiary']};
        }}

        QHeaderView::section {{
            background-color: {colors['background_tertiary']};
            color: {colors['text_secondary']};
            padding: 16px 12px;
            border: none;
            border-bottom: 2px solid {colors['border_color']};
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}

        QHeaderView::section:hover {{
            background-color: {colors['border_hover']};
        }}

        /* === CHAMPS DE SAISIE === */
        QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox {{
            background-color: {colors['background_primary']};
            border: 2px solid {colors['border_color']};
            border-radius: {cls.BORDER_RADIUS_MD};
            padding: 12px 16px;
            font-size: 14px;
            color: {colors['text_primary']};
            transition: all {cls.TRANSITION_FAST};
        }}

        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus {{
            border-color: {colors['border_focus']};
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }}

        QLineEdit:hover, QTextEdit:hover, QSpinBox:hover, QDoubleSpinBox:hover {{
            border-color: {colors['border_hover']};
        }}

        QLineEdit:disabled, QTextEdit:disabled, QSpinBox:disabled, QDoubleSpinBox:disabled {{
            background-color: {colors['background_tertiary']};
            color: {colors['text_disabled']};
            border-color: {colors['border_color']};
        }}

        QLineEdit[class="error"], QTextEdit[class="error"] {{
            border-color: {cls.ERROR_COLOR};
        }}

        QLineEdit[class="success"], QTextEdit[class="success"] {{
            border-color: {cls.SUCCESS_COLOR};
        }}

        /* === COMBOBOX === */
        QComboBox {{
            background-color: {colors['background_primary']};
            border: 2px solid {colors['border_color']};
            border-radius: {cls.BORDER_RADIUS_MD};
            padding: 12px 16px;
            font-size: 14px;
            color: {colors['text_primary']};
            min-width: 120px;
            transition: all {cls.TRANSITION_FAST};
        }}

        QComboBox:focus {{
            border-color: {colors['border_focus']};
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }}

        QComboBox:hover {{
            border-color: {colors['border_hover']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 30px;
            background-color: transparent;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {colors['text_secondary']};
        }}

        QComboBox QAbstractItemView {{
            background-color: {colors['background_primary']};
            border: 1px solid {colors['border_color']};
            border-radius: {cls.BORDER_RADIUS_MD};
            selection-background-color: rgba(59, 130, 246, 0.1);
            padding: 4px;
        }}

        QComboBox QAbstractItemView::item {{
            padding: 8px 12px;
            border-radius: {cls.BORDER_RADIUS_SM};
        }}

        QComboBox QAbstractItemView::item:hover {{
            background-color: {colors['background_tertiary']};
        }}

        /* === SCROLLBARS === */
        QScrollBar:vertical {{
            background-color: {colors['background_tertiary']};
            width: 12px;
            border-radius: 6px;
            margin: 0;
        }}

        QScrollBar::handle:vertical {{
            background-color: {colors['border_hover']};
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }}

        QScrollBar::handle:vertical:hover {{
            background-color: {colors['text_muted']};
        }}

        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}

        QScrollBar:horizontal {{
            background-color: {colors['background_tertiary']};
            height: 12px;
            border-radius: 6px;
            margin: 0;
        }}

        QScrollBar::handle:horizontal {{
            background-color: {colors['border_hover']};
            border-radius: 6px;
            min-width: 20px;
            margin: 2px;
        }}

        /* === ONGLETS === */
        QTabWidget::pane {{
            border: 1px solid {colors['border_color']};
            border-radius: {cls.BORDER_RADIUS_MD};
            background-color: {colors['background_primary']};
            top: -1px;
        }}

        QTabBar::tab {{
            background-color: {colors['background_tertiary']};
            color: {colors['text_secondary']};
            padding: 12px 24px;
            margin-right: 2px;
            border-top-left-radius: {cls.BORDER_RADIUS_MD};
            border-top-right-radius: {cls.BORDER_RADIUS_MD};
            font-weight: 500;
            transition: all {cls.TRANSITION_FAST};
        }}

        QTabBar::tab:selected {{
            background-color: {cls.PRIMARY_600};
            color: white;
        }}

        QTabBar::tab:hover:!selected {{
            background-color: {colors['border_hover']};
            color: {colors['text_primary']};
        }}

        /* === CHECKBOXES ET RADIO BUTTONS === */
        QCheckBox, QRadioButton {{
            color: {colors['text_primary']};
            font-size: 14px;
            spacing: 8px;
        }}

        QCheckBox::indicator, QRadioButton::indicator {{
            width: 18px;
            height: 18px;
            border: 2px solid {colors['border_color']};
            background-color: {colors['background_primary']};
        }}

        QCheckBox::indicator {{
            border-radius: 4px;
        }}

        QRadioButton::indicator {{
            border-radius: 9px;
        }}

        QCheckBox::indicator:checked, QRadioButton::indicator:checked {{
            background-color: {cls.PRIMARY_600};
            border-color: {cls.PRIMARY_600};
        }}

        QCheckBox::indicator:hover, QRadioButton::indicator:hover {{
            border-color: {colors['border_hover']};
        }}

        /* === TOOLTIPS === */
        QToolTip {{
            background-color: {colors['text_primary']};
            color: {colors['background_primary']};
            border: none;
            border-radius: {cls.BORDER_RADIUS_SM};
            padding: 8px 12px;
            font-size: 12px;
        }}
        """

    @classmethod
    def get_dark_stylesheet(cls):
        """Retourner le stylesheet sombre"""
        return cls.get_modern_stylesheet(is_dark=True)

    @classmethod
    def get_light_stylesheet(cls):
        """Retourner le stylesheet clair"""
        return cls.get_modern_stylesheet(is_dark=False)

    @classmethod
    def apply_theme_to_widget(cls, widget, theme_class="", is_dark=False):
        """Appliquer un thème spécifique à un widget"""
        if theme_class:
            widget.setProperty("class", theme_class)

        # Forcer la mise à jour du style
        widget.style().unpolish(widget)
        widget.style().polish(widget)
        widget.update()

    @classmethod
    def create_gradient_background(cls, color1, color2, direction="135deg"):
        """Créer un arrière-plan dégradé"""
        return f"background: linear-gradient({direction}, {color1} 0%, {color2} 100%);"

    @classmethod
    def create_shadow_effect(cls, shadow_type="md"):
        """Créer un effet d'ombre"""
        shadows = {
            "sm": cls.SHADOW_SM,
            "md": cls.SHADOW_MD,
            "lg": cls.SHADOW_LG,
            "xl": cls.SHADOW_XL
        }
        return f"box-shadow: {shadows.get(shadow_type, cls.SHADOW_MD)};"

    @classmethod
    def get_color_with_opacity(cls, color, opacity):
        """Obtenir une couleur avec opacité"""
        # Convertir hex en rgba
        color = color.lstrip('#')
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        return f"rgba({r}, {g}, {b}, {opacity})"

    @classmethod
    def get_status_colors(cls):
        """Retourner les couleurs de statut"""
        return {
            'success': cls.SUCCESS_COLOR,
            'warning': cls.WARNING_COLOR,
            'error': cls.ERROR_COLOR,
            'info': cls.INFO_COLOR,
            'primary': cls.PRIMARY_600,
            'secondary': cls.SECONDARY_500,
            'accent': cls.ACCENT_COLOR
        }
