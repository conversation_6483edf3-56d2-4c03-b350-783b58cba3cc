"""
Fenêtre principale moderne conforme au cahier des charges
Utilise FluentWindow et NavigationInterface selon les spécifications
"""

import sys
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget, QLabel
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        FluentWindow, NavigationInterface, NavigationItemPosition, 
        FluentIcon, InfoBar, InfoBarPosition, setTheme, Theme,
        isDarkTheme, qconfig, ConfigItem, OptionsConfigItem, BoolValidator
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False
    from PyQt5.QtWidgets import QMainWindow as FluentWindow

from config.settings import config
from utils.logger import setup_logger
from ui.theme_manager import theme_manager
from ui.modern_components import NotificationToast, FloatingActionButton

# Import des modules selon les spécifications
from .dashboard_widget import DashboardWidget
from .products_widget import ProductsWidget
from .clients_widget import ClientsWidget
from .suppliers_widget import SuppliersWidget
from .sales_widget import SalesWidget
from .settings_widget import SettingsWidget


class ModernMainWindow(FluentWindow if FLUENT_AVAILABLE else QWidget):
    """
    Fenêtre principale moderne conforme au cahier des charges
    Utilise FluentWindow avec NavigationInterface
    """
    
    # Signaux
    theme_changed = pyqtSignal(str)
    user_logout = pyqtSignal()

    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        
        # Variables d'instance
        self.current_user = None
        self.modules = {}
        self.current_module = None

        # Animations et effets
        self.transition_animation = None
        self.notification_container = None
        
        # Configuration de la fenêtre
        self._setup_window()
        
        # Initialiser l'interface
        self._init_ui()
        
        # Connecter les signaux
        self._connect_signals()
        
        # Configurer les animations
        self._setup_animations()

        self.logger.info("Fenêtre principale moderne initialisée")

    def _setup_animations(self):
        """Configurer les animations et transitions"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve
            from PyQt5.QtWidgets import QGraphicsOpacityEffect

            # Animation de transition entre modules
            self.transition_animation = QPropertyAnimation()
            self.transition_animation.setDuration(300)
            self.transition_animation.setEasingCurve(QEasingCurve.OutCubic)

            # Conteneur de notifications
            self.notification_container = QWidget(self)
            self.notification_container.setFixedWidth(350)
            self.notification_container.move(self.width() - 370, 20)

            self.logger.info("Animations configurées")

        except Exception as e:
            self.logger.error(f"Erreur lors de la configuration des animations: {e}")

    def animate_module_transition(self, new_module):
        """Animer la transition vers un nouveau module"""
        try:
            if not self.current_module or self.current_module == new_module:
                self.current_module = new_module
                return

            from PyQt5.QtWidgets import QGraphicsOpacityEffect
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve

            # Effet d'opacité pour l'ancien module
            old_effect = QGraphicsOpacityEffect()
            self.current_module.setGraphicsEffect(old_effect)

            # Animation de fondu sortant
            fade_out = QPropertyAnimation(old_effect, b"opacity")
            fade_out.setDuration(150)
            fade_out.setStartValue(1.0)
            fade_out.setEndValue(0.0)
            fade_out.setEasingCurve(QEasingCurve.OutCubic)

            # Effet d'opacité pour le nouveau module
            new_effect = QGraphicsOpacityEffect()
            new_module.setGraphicsEffect(new_effect)

            # Animation de fondu entrant
            fade_in = QPropertyAnimation(new_effect, b"opacity")
            fade_in.setDuration(150)
            fade_in.setStartValue(0.0)
            fade_in.setEndValue(1.0)
            fade_in.setEasingCurve(QEasingCurve.InCubic)

            # Connecter les animations
            fade_out.finished.connect(lambda: self._switch_module_content(new_module, fade_in))

            # Démarrer l'animation
            fade_out.start()

            self.current_module = new_module

        except Exception as e:
            self.logger.error(f"Erreur lors de l'animation de transition: {e}")
            # Fallback sans animation
            self.current_module = new_module

    def _switch_module_content(self, new_module, fade_in_animation):
        """Changer le contenu du module et démarrer le fondu entrant"""
        try:
            # Changer le module actuel dans le stack
            if hasattr(self, 'content_stack'):
                self.content_stack.setCurrentWidget(new_module)
            elif hasattr(self, 'stackedWidget'):
                self.stackedWidget.setCurrentWidget(new_module)

            # Démarrer le fondu entrant
            fade_in_animation.start()

        except Exception as e:
            self.logger.error(f"Erreur lors du changement de module: {e}")

    def show_modern_notification(self, message: str, notification_type: str = "info"):
        """Afficher une notification moderne"""
        try:
            # Créer la notification
            notification = NotificationToast(message, notification_type, self)

            # Positionner la notification
            notification.setFixedWidth(340)
            y_position = 20 + (self.notification_container.layout().count() * 70 if self.notification_container.layout() else 0)
            notification.move(self.width() - 360, y_position)

            # Afficher avec animation
            notification.show_animated()

            # Nettoyer après 5 secondes
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(5000, notification.deleteLater)

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage de la notification: {e}")

    def _setup_window(self):
        """Configurer la fenêtre selon les spécifications"""
        self.setWindowTitle(f"{config.APP_NAME} v{config.APP_VERSION} - {config.APP_AUTHOR}")
        self.setMinimumSize(config.MIN_WIDTH, config.MIN_HEIGHT)
        self.resize(config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
        
        # Centrer la fenêtre
        self._center_window()
        
        # Appliquer le thème par défaut
        if FLUENT_AVAILABLE:
            if config.DEFAULT_THEME == "dark":
                setTheme(Theme.DARK)
            else:
                setTheme(Theme.LIGHT)

    def _center_window(self):
        """Centrer la fenêtre sur l'écran"""
        screen = self.app_instance.app.primaryScreen().geometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        if FLUENT_AVAILABLE:
            self._init_fluent_ui()
        else:
            self._init_standard_ui()

    def _init_fluent_ui(self):
        """Initialiser l'interface avec Fluent Widgets"""
        try:
            # Initialiser les widgets des modules d'abord
            self._init_module_widgets()

            # Configuration de la navigation selon les spécifications
            if hasattr(self, 'navigationInterface'):
                self.navigationInterface.setExpandWidth(250)

                # Modules principaux selon le cahier des charges
                self._add_navigation_items()

                # Définir le module par défaut (Dashboard)
                if hasattr(self, 'stackedWidget') and 'dashboard' in self.modules:
                    self.stackedWidget.setCurrentWidget(self.modules['dashboard'])
                    self.navigationInterface.setCurrentItem('dashboard')

        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation Fluent UI: {e}")
            # Fallback vers l'interface standard
            self._init_standard_ui()

    def _init_standard_ui(self):
        """Interface de fallback sans Fluent Widgets"""
        try:
            # Initialiser les modules d'abord si pas déjà fait
            if not hasattr(self, 'modules') or not self.modules:
                self._init_module_widgets()

            # Layout principal
            main_layout = QHBoxLayout()
            self.setLayout(main_layout)

            # Navigation simple
            nav_widget = QWidget()
            nav_widget.setFixedWidth(200)
            nav_layout = QVBoxLayout(nav_widget)

            # Zone de contenu
            self.content_stack = QStackedWidget()

            # Ajouter les modules au stack
            for module_name, module_widget in self.modules.items():
                self.content_stack.addWidget(module_widget)

            # Afficher le dashboard par défaut
            if 'dashboard' in self.modules:
                dashboard_index = list(self.modules.keys()).index('dashboard')
                self.content_stack.setCurrentIndex(dashboard_index)

            main_layout.addWidget(nav_widget)
            main_layout.addWidget(self.content_stack)

            self.logger.info("Interface standard initialisée")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation de l'interface standard: {e}")
            # Interface minimale de secours
            self._init_minimal_ui()

    def _init_minimal_ui(self):
        """Interface minimale de secours"""
        layout = QVBoxLayout()
        self.setLayout(layout)

        title = QLabel("GSlim - Interface de Secours")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")

        message = QLabel("L'interface principale a rencontré un problème.\nVeuillez redémarrer l'application.")
        message.setStyleSheet("margin: 20px; color: red;")

        layout.addWidget(title)
        layout.addWidget(message)
        layout.addStretch()

        self.logger.info("Interface minimale de secours initialisée")

    def _add_navigation_items(self):
        """Ajouter les éléments de navigation selon les spécifications"""
        if not FLUENT_AVAILABLE or not hasattr(self, 'navigationInterface'):
            return

        try:
            # Dashboard - Module principal
            if 'dashboard' in self.modules:
                self.modules['dashboard'].setObjectName('dashboard_widget')
                self.addSubInterface(
                    self.modules['dashboard'],
                    FluentIcon.HOME,
                    'Tableau de Bord',
                    'dashboard'
                )

            # Séparateur logique
            self.navigationInterface.addSeparator()

            # Modules de gestion
            if 'products' in self.modules:
                self.modules['products'].setObjectName('products_widget')
                self.addSubInterface(
                    self.modules['products'],
                    FluentIcon.SHOPPING_CART,
                    'Produits',
                    'products'
                )

            if 'clients' in self.modules:
                self.modules['clients'].setObjectName('clients_widget')
                self.addSubInterface(
                    self.modules['clients'],
                    FluentIcon.PEOPLE,
                    'Clients',
                    'clients'
                )

            if 'suppliers' in self.modules:
                self.modules['suppliers'].setObjectName('suppliers_widget')
                self.addSubInterface(
                    self.modules['suppliers'],
                    FluentIcon.PEOPLE,
                    'Fournisseurs',
                    'suppliers'
                )

            # Séparateur logique
            self.navigationInterface.addSeparator()

            # Module de ventes
            if 'sales' in self.modules:
                self.modules['sales'].setObjectName('sales_widget')
                self.addSubInterface(
                    self.modules['sales'],
                    FluentIcon.MARKET,
                    'Ventes',
                    'sales'
                )

            # Paramètres en bas
            if 'settings' in self.modules:
                self.modules['settings'].setObjectName('settings_widget')
                self.addSubInterface(
                    self.modules['settings'],
                    FluentIcon.SETTING,
                    'Paramètres',
                    'settings',
                    NavigationItemPosition.BOTTOM
                )

        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout des éléments de navigation: {e}")

    def _init_module_widgets(self):
        """Initialiser tous les widgets des modules"""
        self.modules = {}

        # Initialiser chaque module individuellement avec gestion d'erreurs
        modules_to_init = [
            ('dashboard', DashboardWidget, 'Tableau de bord avec KPIs'),
            ('products', ProductsWidget, 'Gestion complète des produits'),
            ('clients', ClientsWidget, 'Gestion avec points de fidélité'),
            ('suppliers', SuppliersWidget, 'Gestion avec évaluation'),
            ('sales', SalesWidget, 'Système complet de ventes'),
            ('settings', SettingsWidget, 'Configuration et thèmes')
        ]

        for module_key, module_class, description in modules_to_init:
            try:
                self.modules[module_key] = module_class(self.app_instance)
                self.logger.info(f"Module {module_key} initialisé: {description}")
            except Exception as e:
                self.logger.error(f"Erreur lors de l'initialisation du module {module_key}: {e}")
                # Créer un widget de fallback
                fallback_widget = QWidget()
                fallback_layout = QVBoxLayout(fallback_widget)
                error_label = QLabel(f"Erreur module {module_key}: {e}")
                error_label.setStyleSheet("color: red; font-style: italic;")
                fallback_layout.addWidget(error_label)
                fallback_layout.addStretch()
                self.modules[module_key] = fallback_widget

        self.logger.info(f"{len(self.modules)} modules initialisés")

    def _connect_signals(self):
        """Connecter les signaux des modules"""
        try:
            # Signaux des modules
            for module_name, module_widget in self.modules.items():
                if hasattr(module_widget, 'error_occurred'):
                    module_widget.error_occurred.connect(self._show_error)
                if hasattr(module_widget, 'success_message'):
                    module_widget.success_message.connect(self._show_success)
                if hasattr(module_widget, 'info_message'):
                    module_widget.info_message.connect(self._show_info)
            
            # Signaux spécifiques
            if 'settings' in self.modules:
                self.modules['settings'].theme_changed.connect(self._on_theme_changed)
                self.modules['settings'].logout_requested.connect(self._on_logout_requested)
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la connexion des signaux: {e}")

    def set_current_user(self, user_data):
        """Définir l'utilisateur actuel"""
        self.current_user = user_data
        
        # Informer tous les modules de l'utilisateur actuel
        for module_widget in self.modules.values():
            if hasattr(module_widget, 'set_current_user'):
                module_widget.set_current_user(user_data)
        
        self.logger.info(f"Utilisateur défini: {user_data.get('username', 'Inconnu')}")

    def _on_theme_changed(self, theme_name):
        """Gérer le changement de thème"""
        if FLUENT_AVAILABLE:
            if theme_name.lower() == "dark":
                setTheme(Theme.DARK)
            else:
                setTheme(Theme.LIGHT)
        
        self.theme_changed.emit(theme_name)
        self._show_success(f"Thème changé vers: {theme_name}")

    def _on_logout_requested(self):
        """Gérer la demande de déconnexion"""
        self.user_logout.emit()

    def _show_error(self, message):
        """Afficher un message d'erreur"""
        if FLUENT_AVAILABLE:
            InfoBar.error(
                title="Erreur",
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=5000,
                parent=self
            )
        else:
            self.logger.error(message)

    def _show_success(self, message):
        """Afficher un message de succès"""
        if FLUENT_AVAILABLE:
            InfoBar.success(
                title="Succès",
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
        else:
            self.logger.info(message)

    def _show_info(self, message):
        """Afficher un message d'information"""
        if FLUENT_AVAILABLE:
            InfoBar.info(
                title="Information",
                content=message,
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
        else:
            self.logger.info(message)

    def closeEvent(self, event):
        """Gérer la fermeture de la fenêtre"""
        self.logger.info("Fermeture de la fenêtre principale")
        event.accept()
