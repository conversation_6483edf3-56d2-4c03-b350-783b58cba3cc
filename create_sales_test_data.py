#!/usr/bin/env python3
"""
Script pour créer des données de test pour les ventes
"""

import sys
import os
from datetime import datetime, timedelta
import random

# Ajouter src au path
sys.path.insert(0, 'src')

def create_sales_test_data():
    """Créer des données de test pour les ventes"""
    print("💰 Création de données de test pour les ventes...")
    
    try:
        from database.manager import DatabaseManager
        
        db = DatabaseManager()
        db.initialize_database()
        
        print("✅ Base de données initialisée")
        
        # Récupérer les données existantes
        products = db.get_products()
        clients = db.get_clients()
        
        if not products:
            print("❌ Aucun produit trouvé. Créez d'abord des produits.")
            return False
        
        if not clients:
            print("❌ Aucun client trouvé. Créez d'abord des clients.")
            return False
        
        print(f"📦 {len(products)} produits disponibles")
        print(f"👥 {len(clients)} clients disponibles")
        
        # Créer des ventes de test
        sales_created = 0
        total_revenue = 0
        
        # Ventes des 30 derniers jours
        for day_offset in range(30):
            sale_date = datetime.now() - timedelta(days=day_offset)
            
            # 1-3 ventes par jour
            num_sales = random.randint(1, 3)
            
            for _ in range(num_sales):
                # Sélectionner un client aléatoire (ou vente sans client)
                client = random.choice(clients + [None])
                
                # Sélectionner 1-4 produits aléatoires
                num_products = random.randint(1, 4)
                selected_products = random.sample(products, min(num_products, len(products)))
                
                # Créer les items de vente
                items = []
                subtotal = 0
                
                for product in selected_products:
                    # Quantité aléatoire (1-5)
                    max_qty = min(5, product['stock_quantity'])
                    if max_qty <= 0:
                        continue
                    
                    quantity = random.randint(1, max_qty)
                    unit_price = product['price']
                    total_price = unit_price * quantity
                    
                    items.append({
                        'product_id': product['id'],
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'total_price': total_price
                    })
                    
                    subtotal += total_price
                
                if not items:
                    continue
                
                # Calculs automatiques
                discount_percent = random.choice([0, 5, 10, 15])  # Remise aléatoire
                discount_amount = subtotal * (discount_percent / 100)
                amount_after_discount = subtotal - discount_amount
                
                tax_percent = 20  # TVA 20%
                tax_amount = amount_after_discount * (tax_percent / 100)
                final_amount = amount_after_discount + tax_amount
                
                # Statut aléatoire (plus de ventes confirmées)
                status = random.choices(
                    ['draft', 'confirmed', 'cancelled'],
                    weights=[10, 80, 10]  # 80% confirmées
                )[0]
                
                # Notes aléatoires
                notes_options = [
                    "",
                    "Livraison express",
                    "Client fidèle",
                    "Commande urgente",
                    "Paiement en espèces",
                    "Facture demandée",
                    "Remise commerciale appliquée"
                ]
                notes = random.choice(notes_options)
                
                # Données de vente
                sale_data = {
                    'client_id': client['id'] if client else None,
                    'user_id': 1,  # Admin
                    'sale_date': sale_date.isoformat(),
                    'total_amount': subtotal,
                    'discount': discount_amount,
                    'tax_amount': tax_amount,
                    'final_amount': final_amount,
                    'status': status,
                    'notes': notes
                }
                
                # Créer la vente
                sale_id = db.create_sale(sale_data, items)
                
                if sale_id:
                    sales_created += 1
                    if status == 'confirmed':
                        total_revenue += final_amount

                    client_name = client['name'] if client else "Sans client"
                    print(f"✅ Vente #{sale_id}: {final_amount:.2f}€ - {client_name} ({status})")
                else:
                    client_name = client['name'] if client else "Sans client"
                    print(f"❌ Erreur création vente pour {client_name}")
        
        print(f"\n🎉 Données de test ventes créées avec succès !")
        print(f"💰 {sales_created} ventes créées")
        print(f"💵 Chiffre d'affaires total: {total_revenue:.2f} €")
        
        # Statistiques par statut
        stats = db.get_sales_statistics()
        print(f"\n📊 Statistiques:")
        print(f"   • Ventes du jour: {stats['daily_sales']:.2f} €")
        print(f"   • Ventes du mois: {stats['monthly_sales']:.2f} €")
        print(f"   • Ventes en attente: {stats['pending_sales']}")
        print(f"   • Produit le plus vendu: {stats['top_product']}")
        
        print("\n🚀 Vous pouvez maintenant tester le module Ventes !")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des données de test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Fonction principale"""
    print("💰 Script de Création de Données de Test - Ventes")
    print("=" * 60)
    
    success = create_sales_test_data()
    
    if success:
        print("\n✅ SUCCÈS ! Données de test ventes créées")
        print("\n📋 Prochaines étapes:")
        print("   1. Lancez l'application: python main.py")
        print("   2. Connectez-vous: admin / admin123")
        print("   3. Allez dans le module Ventes")
        print("   4. Testez toutes les fonctionnalités:")
        print("      • Visualisation des ventes avec statistiques")
        print("      • Création d'une nouvelle vente")
        print("      • Calculs automatiques (HT, TVA, TTC)")
        print("      • Changement de statut des ventes")
        print("      • Filtrage par statut")
        print("      • Recherche par client")
        print("      • Gestion automatique du stock")
        return True
    else:
        print("\n❌ ÉCHEC de la création des données")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
