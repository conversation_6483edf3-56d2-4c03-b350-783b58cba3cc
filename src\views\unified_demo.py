"""
Démonstration Unifiée de Tous les Thèmes GSlim
Interface de test pour tous les thèmes : Moderne, Professionnel, Fluent et Cyberpunk
"""

import sys
import random
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QTabWidget, QScrollArea, QSplitter,
    QGroupBox, QComboBox, QSlider, QProgressBar, QTextEdit, QLineEdit
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont

from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
from widgets.theme_selector import ThemeSelector
from widgets.modern_widgets import ModernStatCard
from widgets.fluent_components import FluentStatCard
from views.cyberpunk_dashboard import Cyberpunk<PERSON>tat<PERSON>ard

try:
    from qfluentwidgets import FluentIcon
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False


class UnifiedDemoWindow(QMainWindow):
    """Fenêtre de démonstration unifiée"""
    
    def __init__(self):
        super().__init__()
        self.theme_manager = get_theme_manager()
        self.demo_data = self._generate_demo_data()
        
        self.setup_window()
        self.setup_ui()
        self.setup_demo_timer()
        
        # Appliquer le thème actuel
        self._apply_current_theme()
    
    def setup_window(self):
        """Configurer la fenêtre"""
        self.setWindowTitle("🎨 GSlim - Démonstration Unifiée des Thèmes")
        self.setGeometry(100, 100, 1400, 900)
    
    def setup_ui(self):
        """Configurer l'interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal avec splitter
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        splitter = QSplitter(Qt.Horizontal)
        
        # Panel de gauche - Sélecteur de thèmes
        left_panel = self._create_theme_panel()
        splitter.addWidget(left_panel)
        
        # Panel de droite - Démonstration
        right_panel = self._create_demo_panel()
        splitter.addWidget(right_panel)
        
        # Proportions du splitter
        splitter.setSizes([400, 1000])
        
        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)
    
    def _create_theme_panel(self):
        """Créer le panel de sélection de thèmes"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMaximumWidth(450)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Titre
        title = QLabel("🎨 Sélecteur de Thèmes")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        layout.addWidget(title)
        
        # Sélecteur de thèmes
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.theme_selector = ThemeSelector()
        self.theme_selector.theme_applied.connect(self._on_theme_applied)
        scroll_area.setWidget(self.theme_selector)
        
        layout.addWidget(scroll_area)
        
        # Boutons de test rapide
        quick_test_group = QGroupBox("🚀 Tests Rapides")
        quick_layout = QVBoxLayout()
        
        quick_buttons = [
            ("🎨 Moderne Sombre", ThemeType.MODERN, ThemeMode.DARK),
            ("💼 Professionnel", ThemeType.PROFESSIONAL, ThemeMode.DARK),
            ("🌊 Fluent Design", ThemeType.FLUENT, ThemeMode.DARK),
            ("🚀 Cyberpunk", ThemeType.CYBERPUNK, ThemeMode.DARK),
            ("☀️ Mode Clair", None, ThemeMode.LIGHT)
        ]
        
        for text, theme_type, theme_mode in quick_buttons:
            btn = QPushButton(text)
            btn.clicked.connect(lambda checked, t=theme_type, m=theme_mode: self._quick_apply_theme(t, m))
            quick_layout.addWidget(btn)
        
        quick_test_group.setLayout(quick_layout)
        layout.addWidget(quick_test_group)
        
        panel.setLayout(layout)
        return panel
    
    def _create_demo_panel(self):
        """Créer le panel de démonstration"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête de démonstration
        self._create_demo_header(layout)
        
        # Onglets de démonstration
        tabs = QTabWidget()
        
        # Onglet Cartes de statistiques
        stats_tab = self._create_stats_demo_tab()
        tabs.addTab(stats_tab, "📊 Statistiques")
        
        # Onglet Composants
        components_tab = self._create_components_demo_tab()
        tabs.addTab(components_tab, "🧩 Composants")
        
        # Onglet Formulaires
        forms_tab = self._create_forms_demo_tab()
        tabs.addTab(forms_tab, "📝 Formulaires")
        
        # Onglet Données
        data_tab = self._create_data_demo_tab()
        tabs.addTab(data_tab, "📈 Données")
        
        layout.addWidget(tabs)
        
        panel.setLayout(layout)
        return panel
    
    def _create_demo_header(self, layout):
        """Créer l'en-tête de démonstration"""
        header_frame = QFrame()
        header_frame.setProperty("class", "demo-header")
        header_frame.setFixedHeight(100)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(20, 15, 20, 15)
        
        # Titre et info
        title_layout = QVBoxLayout()
        
        self.demo_title = QLabel("GSlim - Interface Moderne")
        self.demo_title.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title_layout.addWidget(self.demo_title)
        
        self.demo_subtitle = QLabel("Démonstration des thèmes et composants")
        self.demo_subtitle.setStyleSheet("color: #666; font-size: 14px;")
        title_layout.addWidget(self.demo_subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        # Informations du thème actuel
        theme_info_layout = QVBoxLayout()
        
        self.current_theme_label = QLabel("Thème: Moderne")
        self.current_theme_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        theme_info_layout.addWidget(self.current_theme_label)
        
        self.current_mode_label = QLabel("Mode: Sombre")
        self.current_mode_label.setStyleSheet("color: #888;")
        theme_info_layout.addWidget(self.current_mode_label)
        
        header_layout.addLayout(theme_info_layout)
        
        header_frame.setLayout(header_layout)
        layout.addWidget(header_frame)
    
    def _create_stats_demo_tab(self):
        """Créer l'onglet de démonstration des statistiques"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Titre
        title = QLabel("📊 Cartes de Statistiques")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        layout.addWidget(title)
        
        # Grille de cartes
        cards_layout = QGridLayout()
        cards_layout.setSpacing(15)
        
        # Créer des cartes selon le thème actuel
        self.stat_cards = []
        self._create_theme_appropriate_cards(cards_layout)
        
        layout.addLayout(cards_layout)
        
        # Bouton de mise à jour des données
        update_btn = QPushButton("🔄 Mettre à jour les données")
        update_btn.clicked.connect(self._update_demo_data)
        layout.addWidget(update_btn)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def _create_components_demo_tab(self):
        """Créer l'onglet de démonstration des composants"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Titre
        title = QLabel("🧩 Composants UI")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        layout.addWidget(title)
        
        # Grille de composants
        components_layout = QGridLayout()
        
        # Boutons
        buttons_group = QGroupBox("Boutons")
        buttons_layout = QHBoxLayout()
        
        btn_types = [
            ("Primary", "primary"),
            ("Secondary", "secondary"),
            ("Success", "success"),
            ("Warning", "warning"),
            ("Danger", "danger")
        ]
        
        for text, btn_type in btn_types:
            btn = QPushButton(text)
            btn.setProperty("class", btn_type)
            buttons_layout.addWidget(btn)
        
        buttons_group.setLayout(buttons_layout)
        layout.addWidget(buttons_group)
        
        # Barres de progression
        progress_group = QGroupBox("Barres de progression")
        progress_layout = QVBoxLayout()
        
        for i, (label, value) in enumerate([("Chargement", 75), ("Synchronisation", 45), ("Traitement", 90)]):
            progress_label = QLabel(label)
            progress_layout.addWidget(progress_label)
            
            progress_bar = QProgressBar()
            progress_bar.setValue(value)
            progress_layout.addWidget(progress_bar)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def _create_forms_demo_tab(self):
        """Créer l'onglet de démonstration des formulaires"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Titre
        title = QLabel("📝 Formulaires")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        layout.addWidget(title)
        
        # Formulaire d'exemple
        form_group = QGroupBox("Exemple de formulaire")
        form_layout = QVBoxLayout()
        
        # Champs de saisie
        fields = [
            ("Nom du produit", "Entrez le nom"),
            ("Description", "Description détaillée"),
            ("Prix", "0.00"),
            ("Catégorie", "Sélectionnez une catégorie")
        ]
        
        for label_text, placeholder in fields:
            label = QLabel(label_text)
            form_layout.addWidget(label)
            
            if label_text == "Description":
                field = QTextEdit()
                field.setMaximumHeight(80)
                field.setPlaceholderText(placeholder)
            else:
                field = QLineEdit()
                field.setPlaceholderText(placeholder)
            
            form_layout.addWidget(field)
        
        # Boutons du formulaire
        form_buttons = QHBoxLayout()
        save_btn = QPushButton("💾 Sauvegarder")
        cancel_btn = QPushButton("❌ Annuler")
        
        form_buttons.addWidget(save_btn)
        form_buttons.addWidget(cancel_btn)
        form_buttons.addStretch()
        
        form_layout.addLayout(form_buttons)
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def _create_data_demo_tab(self):
        """Créer l'onglet de démonstration des données"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # Titre
        title = QLabel("📈 Flux de Données")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        layout.addWidget(title)
        
        # Zone de données en temps réel
        data_group = QGroupBox("Données en temps réel")
        data_layout = QVBoxLayout()
        
        self.data_display = QTextEdit()
        self.data_display.setMaximumHeight(200)
        self.data_display.setReadOnly(True)
        data_layout.addWidget(self.data_display)
        
        data_group.setLayout(data_layout)
        layout.addWidget(data_group)
        
        # Contrôles
        controls_group = QGroupBox("Contrôles")
        controls_layout = QHBoxLayout()
        
        start_btn = QPushButton("▶️ Démarrer")
        stop_btn = QPushButton("⏹️ Arrêter")
        clear_btn = QPushButton("🗑️ Effacer")
        
        start_btn.clicked.connect(self._start_data_stream)
        stop_btn.clicked.connect(self._stop_data_stream)
        clear_btn.clicked.connect(self.data_display.clear)
        
        controls_layout.addWidget(start_btn)
        controls_layout.addWidget(stop_btn)
        controls_layout.addWidget(clear_btn)
        controls_layout.addStretch()
        
        controls_group.setLayout(controls_layout)
        layout.addWidget(controls_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def _create_theme_appropriate_cards(self, layout):
        """Créer des cartes appropriées au thème actuel"""
        current_theme, _ = self.theme_manager.get_current_theme()
        
        # Données pour les cartes
        cards_data = [
            ("Articles", "1,234", "📦", "primary"),
            ("Ventes", "€45,678", "💰", "success"),
            ("Commandes", "89", "📋", "info"),
            ("Alertes", "3", "⚠️", "warning")
        ]
        
        for i, (title, value, icon, card_type) in enumerate(cards_data):
            row = i // 2
            col = i % 2
            
            if current_theme == ThemeType.CYBERPUNK:
                card = CyberpunkStatCard(title, value, f"↗ +{random.randint(5, 25)}%", card_type, icon)
            elif current_theme == ThemeType.FLUENT and FLUENT_AVAILABLE:
                card = FluentStatCard(title, value, icon, card_type)
            else:
                card = ModernStatCard(title, value, f"↗ +{random.randint(5, 25)}%", card_type)
            
            card.clicked.connect(lambda t=title: self._on_card_clicked(t))
            self.stat_cards.append(card)
            layout.addWidget(card, row, col)
    
    def _generate_demo_data(self):
        """Générer des données de démonstration"""
        return {
            "articles": random.randint(1000, 2000),
            "ventes": random.randint(40000, 60000),
            "commandes": random.randint(50, 150),
            "alertes": random.randint(1, 10)
        }
    
    def setup_demo_timer(self):
        """Configurer le timer de démonstration"""
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self._update_demo_data)
        self.demo_timer.start(5000)  # Mise à jour toutes les 5 secondes
        
        self.data_timer = QTimer()
        self.data_timer.timeout.connect(self._add_data_line)
    
    def _apply_current_theme(self):
        """Appliquer le thème actuel"""
        current_theme, current_mode = self.theme_manager.get_current_theme()
        self.theme_manager.set_theme(current_theme, current_mode)
        self._update_theme_info()
    
    def _quick_apply_theme(self, theme_type, theme_mode):
        """Appliquer rapidement un thème"""
        if theme_type is None:
            # Changer seulement le mode
            current_theme, _ = self.theme_manager.get_current_theme()
            self.theme_manager.set_theme(current_theme, theme_mode)
        else:
            self.theme_manager.set_theme(theme_type, theme_mode)
        
        self._update_theme_info()
    
    def _on_theme_applied(self, theme_type, theme_mode):
        """Gérer l'application d'un thème"""
        self._update_theme_info()
    
    def _update_theme_info(self):
        """Mettre à jour les informations du thème"""
        current_theme, current_mode = self.theme_manager.get_current_theme()
        
        theme_info = self.theme_manager.get_theme_info(current_theme)
        theme_name = theme_info.get("name", current_theme.value.title())
        mode_name = "Sombre" if current_mode == ThemeMode.DARK else "Clair"
        
        self.current_theme_label.setText(f"Thème: {theme_name}")
        self.current_mode_label.setText(f"Mode: {mode_name}")
        
        # Mettre à jour le titre selon le thème
        if current_theme == ThemeType.CYBERPUNK:
            self.demo_title.setText("GSLIM - CYBERPUNK INTERFACE")
            self.demo_subtitle.setText("Quantum Inventory Management System")
        elif current_theme == ThemeType.FLUENT:
            self.demo_title.setText("GSlim - Fluent Design")
            self.demo_subtitle.setText("Modern inventory management with Fluent UI")
        elif current_theme == ThemeType.PROFESSIONAL:
            self.demo_title.setText("GSlim - Interface Professionnelle")
            self.demo_subtitle.setText("Solution business élégante et moderne")
        else:
            self.demo_title.setText("GSlim - Interface Moderne")
            self.demo_subtitle.setText("Démonstration des thèmes et composants")
    
    def _update_demo_data(self):
        """Mettre à jour les données de démonstration"""
        self.demo_data = self._generate_demo_data()
        
        # Mettre à jour les cartes si elles existent
        if hasattr(self, 'stat_cards'):
            data_values = [
                f"{self.demo_data['articles']:,}",
                f"€{self.demo_data['ventes']:,}",
                str(self.demo_data['commandes']),
                str(self.demo_data['alertes'])
            ]
            
            for i, card in enumerate(self.stat_cards):
                if i < len(data_values):
                    card.update_value(data_values[i], animate=True)
    
    def _on_card_clicked(self, title):
        """Gérer le clic sur une carte"""
        self._add_log_message(f"📊 Carte '{title}' cliquée")
    
    def _start_data_stream(self):
        """Démarrer le flux de données"""
        self.data_timer.start(1000)  # Nouvelle ligne chaque seconde
        self._add_log_message("▶️ Flux de données démarré")
    
    def _stop_data_stream(self):
        """Arrêter le flux de données"""
        self.data_timer.stop()
        self._add_log_message("⏹️ Flux de données arrêté")
    
    def _add_data_line(self):
        """Ajouter une ligne de données"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        data_types = ["SYNC", "UPDATE", "INSERT", "DELETE", "QUERY"]
        data_type = random.choice(data_types)
        
        value = random.randint(100, 999)
        
        line = f"[{timestamp}] {data_type}: {value} records processed"
        self._add_log_message(line)
    
    def _add_log_message(self, message):
        """Ajouter un message au log"""
        if hasattr(self, 'data_display'):
            self.data_display.append(message)
            
            # Limiter le nombre de lignes
            if self.data_display.document().lineCount() > 50:
                cursor = self.data_display.textCursor()
                cursor.movePosition(cursor.Start)
                cursor.select(cursor.LineUnderCursor)
                cursor.removeSelectedText()
                cursor.deleteChar()  # Supprimer le saut de ligne
