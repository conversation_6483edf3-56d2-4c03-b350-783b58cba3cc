"""
Gestionnaire de thèmes CSS pour l'application GSlim
Thèmes modernes inspirés du Fluent Design System
"""

from enum import Enum
from typing import Dict

class ThemeType(Enum):
    """Types de thèmes disponibles"""
    LIGHT = "light"
    DARK = "dark"

class ThemeManager:
    """Gestionnaire des thèmes CSS"""
    
    def __init__(self):
        self.current_theme = ThemeType.DARK
        self._themes = {
            ThemeType.LIGHT: self._get_light_theme(),
            ThemeType.DARK: self._get_dark_theme()
        }
    
    def get_current_theme_css(self) -> str:
        """Retourner le CSS du thème actuel"""
        return self._themes[self.current_theme]
    
    def switch_theme(self) -> str:
        """Basculer entre les thèmes et retourner le nouveau CSS"""
        self.current_theme = ThemeType.LIGHT if self.current_theme == ThemeType.DARK else ThemeType.DARK
        return self.get_current_theme_css()
    
    def set_theme(self, theme_type: ThemeType) -> str:
        """Définir un thème spécifique"""
        self.current_theme = theme_type
        return self.get_current_theme_css()
    
    def _get_light_theme(self) -> str:
        """CSS pour le thème clair"""
        return """
        /* Thème Clair - Fluent Design */
        QMainWindow {
            background-color: #f3f3f3;
            color: #323130;
        }
        
        /* Fenêtres et conteneurs principaux */
        QWidget {
            background-color: #ffffff;
            color: #323130;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
        }
        
        /* Boutons modernes */
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: 600;
            min-height: 32px;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #f3f2f1;
            color: #a19f9d;
        }
        
        /* Boutons secondaires */
        QPushButton[class="secondary"] {
            background-color: transparent;
            color: #0078d4;
            border: 1px solid #0078d4;
        }
        
        QPushButton[class="secondary"]:hover {
            background-color: #f3f2f1;
        }
        
        /* Champs de saisie */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 1px solid #d2d0ce;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #0078d4;
            outline: none;
        }
        
        /* ComboBox */
        QComboBox {
            background-color: #ffffff;
            border: 1px solid #d2d0ce;
            border-radius: 4px;
            padding: 8px 12px;
            min-height: 20px;
        }
        
        QComboBox:focus {
            border: 2px solid #0078d4;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: url(icons/arrow-down.png);
            width: 12px;
            height: 12px;
        }
        
        /* Tables */
        QTableWidget {
            background-color: #ffffff;
            border: 1px solid #d2d0ce;
            border-radius: 4px;
            gridline-color: #edebe9;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #edebe9;
        }
        
        QTableWidget::item:selected {
            background-color: #deecf9;
            color: #323130;
        }
        
        QHeaderView::section {
            background-color: #f3f2f1;
            color: #323130;
            padding: 8px;
            border: none;
            border-bottom: 1px solid #d2d0ce;
            font-weight: 600;
        }
        
        /* Barres de défilement */
        QScrollBar:vertical {
            background-color: #f3f2f1;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #c8c6c4;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #a19f9d;
        }
        
        /* Menu et navigation */
        QMenuBar {
            background-color: #f3f2f1;
            color: #323130;
            border-bottom: 1px solid #d2d0ce;
        }
        
        QMenuBar::item {
            padding: 8px 16px;
        }
        
        QMenuBar::item:selected {
            background-color: #deecf9;
        }
        
        QMenu {
            background-color: #ffffff;
            border: 1px solid #d2d0ce;
            border-radius: 4px;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #deecf9;
        }
        
        /* Cartes et panneaux */
        QFrame[class="card"] {
            background-color: #ffffff;
            border: 1px solid #d2d0ce;
            border-radius: 8px;
            padding: 16px;
        }
        
        /* Labels et titres */
        QLabel[class="title"] {
            font-size: 24px;
            font-weight: 600;
            color: #323130;
        }
        
        QLabel[class="subtitle"] {
            font-size: 18px;
            font-weight: 600;
            color: #605e5c;
        }
        
        QLabel[class="caption"] {
            font-size: 12px;
            color: #605e5c;
        }
        """
    
    def _get_dark_theme(self) -> str:
        """CSS pour le thème sombre"""
        return """
        /* Thème Sombre - Fluent Design */
        QMainWindow {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        
        /* Fenêtres et conteneurs principaux */
        QWidget {
            background-color: #2d2d30;
            color: #ffffff;
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
        }
        
        /* Boutons modernes */
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: 600;
            min-height: 32px;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #3c3c3c;
            color: #858585;
        }
        
        /* Boutons secondaires */
        QPushButton[class="secondary"] {
            background-color: transparent;
            color: #0078d4;
            border: 1px solid #0078d4;
        }
        
        QPushButton[class="secondary"]:hover {
            background-color: #3c3c3c;
        }
        
        /* Champs de saisie */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            color: #ffffff;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border: 2px solid #0078d4;
            outline: none;
        }
        
        /* ComboBox */
        QComboBox {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 4px;
            padding: 8px 12px;
            min-height: 20px;
            color: #ffffff;
        }
        
        QComboBox:focus {
            border: 2px solid #0078d4;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: url(icons/arrow-down-white.png);
            width: 12px;
            height: 12px;
        }
        
        /* Tables */
        QTableWidget {
            background-color: #2d2d30;
            border: 1px solid #5a5a5a;
            border-radius: 4px;
            gridline-color: #3c3c3c;
            color: #ffffff;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #3c3c3c;
        }
        
        QTableWidget::item:selected {
            background-color: #094771;
            color: #ffffff;
        }
        
        QHeaderView::section {
            background-color: #3c3c3c;
            color: #ffffff;
            padding: 8px;
            border: none;
            border-bottom: 1px solid #5a5a5a;
            font-weight: 600;
        }
        
        /* Barres de défilement */
        QScrollBar:vertical {
            background-color: #3c3c3c;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #5a5a5a;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #858585;
        }
        
        /* Menu et navigation */
        QMenuBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-bottom: 1px solid #5a5a5a;
        }
        
        QMenuBar::item {
            padding: 8px 16px;
        }
        
        QMenuBar::item:selected {
            background-color: #094771;
        }
        
        QMenu {
            background-color: #2d2d30;
            border: 1px solid #5a5a5a;
            border-radius: 4px;
            color: #ffffff;
        }
        
        QMenu::item {
            padding: 8px 16px;
        }
        
        QMenu::item:selected {
            background-color: #094771;
        }
        
        /* Cartes et panneaux */
        QFrame[class="card"] {
            background-color: #2d2d30;
            border: 1px solid #5a5a5a;
            border-radius: 8px;
            padding: 16px;
        }
        
        /* Labels et titres */
        QLabel[class="title"] {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
        }
        
        QLabel[class="subtitle"] {
            font-size: 18px;
            font-weight: 600;
            color: #cccccc;
        }
        
        QLabel[class="caption"] {
            font-size: 12px;
            color: #cccccc;
        }
        """

# Instance globale du gestionnaire de thèmes
theme_manager = ThemeManager()
