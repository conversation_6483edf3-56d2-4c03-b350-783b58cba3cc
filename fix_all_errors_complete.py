#!/usr/bin/env python3
"""
Script de correction complète de toutes les erreurs dans GSlim
Corrige les imports, les dépendances et les erreurs de code
"""

import os
import sys
import subprocess
import traceback
from pathlib import Path

def print_header(title):
    """Afficher un en-tête formaté"""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")

def print_step(step):
    """Afficher une étape"""
    print(f"\n📋 {step}")
    print("-" * 40)

def run_command(command, description):
    """Exécuter une commande avec gestion d'erreurs"""
    print(f"⚡ {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} réussi")
            if result.stdout.strip():
                print(f"   📄 {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} échoué")
            if result.stderr.strip():
                print(f"   ⚠️ {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ Erreur lors de {description}: {e}")
        return False

def check_python_environment():
    """Vérifier l'environnement Python"""
    print_step("Vérification de l'environnement Python")
    
    # Vérifier la version Python
    python_version = sys.version_info
    print(f"🐍 Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python 3.8+ requis")
        return False
    
    print("✅ Version Python compatible")
    return True

def install_dependencies():
    """Installer les dépendances manquantes"""
    print_step("Installation des dépendances")
    
    # Vérifier si pip est disponible
    if not run_command("pip --version", "Vérification de pip"):
        print("❌ pip non disponible")
        return False
    
    # Installer les dépendances depuis requirements.txt
    if os.path.exists("requirements.txt"):
        success = run_command("pip install -r requirements.txt", "Installation des dépendances")
        if not success:
            print("⚠️ Tentative d'installation individuelle...")
            
            # Dépendances critiques
            critical_deps = [
                "PyQt5>=5.15.11",
                "bcrypt>=4.3.0",
                "pandas>=2.3.1",
                "python-dotenv>=1.1.1"
            ]
            
            for dep in critical_deps:
                run_command(f"pip install {dep}", f"Installation de {dep}")
        
        return True
    else:
        print("❌ requirements.txt non trouvé")
        return False

def check_imports():
    """Vérifier tous les imports critiques"""
    print_step("Vérification des imports")
    
    imports_to_test = [
        ("PyQt5", "from PyQt5.QtWidgets import QApplication"),
        ("Configuration", "from config.settings import config"),
        ("Database Manager", "from database.manager import DatabaseManager"),
        ("Logger", "from utils.logger import setup_logger"),
        ("Theme Manager", "from styles.themes import theme_manager"),
        ("Login Window", "from views.login_window import LoginWindow"),
        ("Main Window", "from views.main_window import MainWindow"),
    ]
    
    errors = []
    for name, import_statement in imports_to_test:
        try:
            # Ajouter src au path
            if 'src' not in sys.path:
                sys.path.insert(0, 'src')
            
            exec(import_statement)
            print(f"✅ {name}")
        except Exception as e:
            print(f"❌ {name}: {e}")
            errors.append((name, str(e)))
    
    return len(errors) == 0, errors

def fix_import_errors():
    """Corriger les erreurs d'import spécifiques"""
    print_step("Correction des erreurs d'import")
    
    # Vérifier et créer les fichiers __init__.py manquants
    init_files = [
        "src/__init__.py",
        "src/config/__init__.py",
        "src/database/__init__.py",
        "src/models/__init__.py",
        "src/views/__init__.py",
        "src/controllers/__init__.py",
        "src/utils/__init__.py",
        "src/styles/__init__.py",
        "src/widgets/__init__.py"
    ]
    
    for init_file in init_files:
        if not os.path.exists(init_file):
            print(f"📝 Création de {init_file}")
            os.makedirs(os.path.dirname(init_file), exist_ok=True)
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('"""Module package"""')
        else:
            print(f"✅ {init_file} existe")

def check_database():
    """Vérifier la base de données"""
    print_step("Vérification de la base de données")
    
    try:
        # Ajouter src au path
        if 'src' not in sys.path:
            sys.path.insert(0, 'src')
        
        from database.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        
        print("✅ Base de données initialisée")
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def test_application():
    """Tester l'application"""
    print_step("Test de l'application")
    
    try:
        # Ajouter src au path
        if 'src' not in sys.path:
            sys.path.insert(0, 'src')
        
        from src.app import GSlimApp
        print("✅ GSlimApp importée avec succès")
        
        # Test d'initialisation (sans lancer l'interface)
        print("🧪 Test d'initialisation...")
        app = GSlimApp()
        print("✅ Application initialisée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur application: {e}")
        print(f"📋 Détails: {traceback.format_exc()}")
        return False

def main():
    """Fonction principale de correction"""
    print_header("CORRECTION COMPLÈTE DE TOUTES LES ERREURS GSLIM")
    
    # Étapes de correction
    steps = [
        ("Environnement Python", check_python_environment),
        ("Installation dépendances", install_dependencies),
        ("Correction imports", fix_import_errors),
        ("Vérification base de données", check_database),
    ]
    
    success_count = 0
    total_steps = len(steps)
    
    for step_name, step_function in steps:
        try:
            if step_function():
                success_count += 1
                print(f"✅ {step_name} - SUCCÈS")
            else:
                print(f"❌ {step_name} - ÉCHEC")
        except Exception as e:
            print(f"❌ {step_name} - ERREUR: {e}")
    
    # Vérification finale
    print_step("Vérification finale")
    imports_ok, import_errors = check_imports()
    
    if imports_ok:
        success_count += 1
        print("✅ Tous les imports fonctionnent")
        
        # Test final de l'application
        if test_application():
            success_count += 1
            print("✅ Application testée avec succès")
    else:
        print("❌ Erreurs d'import persistantes:")
        for name, error in import_errors:
            print(f"   - {name}: {error}")
    
    # Résumé final
    print_header("RÉSUMÉ FINAL")
    
    if success_count >= total_steps:
        print("🎉 TOUTES LES ERREURS ONT ÉTÉ CORRIGÉES !")
        print("✅ L'application est prête à être lancée")
        print("\n🚀 Pour lancer l'application:")
        print("   python main.py")
        print("\n🔑 Connexion par défaut:")
        print("   Utilisateur: admin")
        print("   Mot de passe: admin123")
        
        return True
    else:
        print(f"⚠️ {total_steps + 2 - success_count} problème(s) restant(s)")
        print("💡 Consultez les messages d'erreur ci-dessus")
        
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Correction interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur critique: {e}")
        print(f"📋 Détails: {traceback.format_exc()}")
        sys.exit(1)
