"""
Fenêtre de connexion moderne avec PyQt5 et Fluent Widgets
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QMessageBox, QCheckBox, QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPainter, QBrush, QColor

try:
    from qfluentwidgets import (
        PushButton, LineEdit, PasswordLineEdit, CheckBox,
        FluentIcon, setTheme, Theme, CardWidget, TitleLabel, CaptionLabel
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False
    print("PyQt-Fluent-Widgets non disponible, utilisation des widgets PyQt5 standard")

from config.settings import config
from utils.logger import setup_logger


class LoginWindow(QWidget):
    """Fenêtre de connexion moderne"""
    
    login_success = pyqtSignal(dict)
    
    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        
        self.setWindowTitle(f"{config.APP_NAME} - Connexion")
        self.setFixedSize(450, 600)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        # Centrer la fenêtre
        self._center_window()
        
        # Initialiser l'interface
        self._init_ui()
        
        # Connecter les signaux
        self._connect_signals()
    
    def _center_window(self):
        """Centrer la fenêtre sur l'écran"""
        from PyQt5.QtWidgets import QDesktopWidget
        
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        x = (screen.width() - size.width()) // 2
        y = (screen.height() - size.height()) // 2
        self.move(x, y)
    
    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Layout principal
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(40, 40, 40, 40)
        main_layout.setSpacing(20)
        
        # Spacer du haut
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # Logo et titre
        self._create_header(main_layout)
        
        # Carte de connexion
        self._create_login_card(main_layout)
        
        # Spacer du bas
        main_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # Informations de version
        self._create_footer(main_layout)
        
        self.setLayout(main_layout)
    
    def _create_header(self, layout):
        """Créer l'en-tête avec logo et titre"""
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(10)
        
        # Titre principal
        if FLUENT_AVAILABLE:
            title = TitleLabel(config.APP_NAME)
            title.setAlignment(Qt.AlignCenter)
        else:
            title = QLabel(config.APP_NAME)
            title.setAlignment(Qt.AlignCenter)
            title.setProperty("class", "title")
            font = QFont("Segoe UI", 28, QFont.Bold)
            title.setFont(font)
        
        header_layout.addWidget(title)
        
        # Sous-titre
        if FLUENT_AVAILABLE:
            subtitle = CaptionLabel("Gestion de Stock Moderne")
            subtitle.setAlignment(Qt.AlignCenter)
        else:
            subtitle = QLabel("Gestion de Stock Moderne")
            subtitle.setAlignment(Qt.AlignCenter)
            subtitle.setProperty("class", "subtitle")
            font = QFont("Segoe UI", 14)
            subtitle.setFont(font)
        
        header_layout.addWidget(subtitle)
        
        layout.addLayout(header_layout)
    
    def _create_login_card(self, layout):
        """Créer la carte de connexion"""
        if FLUENT_AVAILABLE:
            card = CardWidget()
            card.setFixedHeight(300)
        else:
            card = QFrame()
            card.setProperty("class", "card")
            card.setFixedHeight(300)
        
        card_layout = QVBoxLayout()
        card_layout.setContentsMargins(30, 30, 30, 30)
        card_layout.setSpacing(20)
        
        # Titre de la carte
        login_title = QLabel("Connexion")
        login_title.setAlignment(Qt.AlignCenter)
        font = QFont("Segoe UI", 18, QFont.Bold)
        login_title.setFont(font)
        card_layout.addWidget(login_title)
        
        # Champ nom d'utilisateur
        username_layout = QVBoxLayout()
        username_layout.setSpacing(5)
        
        username_label = QLabel("Nom d'utilisateur")
        username_layout.addWidget(username_label)
        
        if FLUENT_AVAILABLE:
            self.username_input = LineEdit()
            self.username_input.setPlaceholderText("Entrez votre nom d'utilisateur")
        else:
            self.username_input = QLineEdit()
            self.username_input.setPlaceholderText("Entrez votre nom d'utilisateur")
        
        self.username_input.setText("admin")  # Valeur par défaut pour les tests
        username_layout.addWidget(self.username_input)
        card_layout.addLayout(username_layout)
        
        # Champ mot de passe
        password_layout = QVBoxLayout()
        password_layout.setSpacing(5)
        
        password_label = QLabel("Mot de passe")
        password_layout.addWidget(password_label)
        
        if FLUENT_AVAILABLE:
            self.password_input = PasswordLineEdit()
            self.password_input.setPlaceholderText("Entrez votre mot de passe")
        else:
            self.password_input = QLineEdit()
            self.password_input.setEchoMode(QLineEdit.Password)
            self.password_input.setPlaceholderText("Entrez votre mot de passe")
        
        self.password_input.setText("admin123")  # Valeur par défaut pour les tests
        password_layout.addWidget(self.password_input)
        card_layout.addLayout(password_layout)
        
        # Case "Se souvenir de moi"
        if FLUENT_AVAILABLE:
            self.remember_checkbox = CheckBox("Se souvenir de moi")
        else:
            self.remember_checkbox = QCheckBox("Se souvenir de moi")
        
        card_layout.addWidget(self.remember_checkbox)
        
        # Bouton de connexion
        if FLUENT_AVAILABLE:
            self.login_button = PushButton("Se connecter")
            self.login_button.setIcon(FluentIcon.ACCEPT)
        else:
            self.login_button = QPushButton("Se connecter")
        
        self.login_button.setFixedHeight(40)
        card_layout.addWidget(self.login_button)
        
        card.setLayout(card_layout)
        layout.addWidget(card)
    
    def _create_footer(self, layout):
        """Créer le pied de page avec informations"""
        footer_layout = QVBoxLayout()
        footer_layout.setAlignment(Qt.AlignCenter)
        footer_layout.setSpacing(5)
        
        # Version
        version_label = QLabel(f"Version {config.APP_VERSION}")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setProperty("class", "caption")
        footer_layout.addWidget(version_label)
        
        # Informations par défaut
        info_label = QLabel("Utilisateur par défaut: admin / admin123")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setProperty("class", "caption")
        footer_layout.addWidget(info_label)
        
        layout.addLayout(footer_layout)
    
    def _connect_signals(self):
        """Connecter les signaux"""
        self.login_button.clicked.connect(self._handle_login)
        self.username_input.returnPressed.connect(self._handle_login)
        self.password_input.returnPressed.connect(self._handle_login)
    
    def _handle_login(self):
        """Gérer la tentative de connexion"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self._show_error("Veuillez remplir tous les champs")
            return
        
        # Désactiver le bouton pendant la vérification
        self.login_button.setEnabled(False)
        self.login_button.setText("Connexion en cours...")
        
        # Utiliser un timer pour simuler une vérification asynchrone
        QTimer.singleShot(500, lambda: self._verify_credentials(username, password))
    
    def _verify_credentials(self, username, password):
        """Vérifier les identifiants"""
        try:
            user = self.app_instance.get_database_manager().authenticate_user(username, password)
            
            if user:
                self.logger.info(f"Connexion réussie pour: {username}")
                self.app_instance.on_login_success(user)
            else:
                self._show_error("Nom d'utilisateur ou mot de passe incorrect")
                
        except Exception as e:
            self.logger.error(f"Erreur lors de l'authentification: {e}")
            self._show_error(f"Erreur de connexion: {e}")
        
        finally:
            # Réactiver le bouton
            self.login_button.setEnabled(True)
            self.login_button.setText("Se connecter")
    
    def _show_error(self, message):
        """Afficher un message d'erreur"""
        QMessageBox.warning(self, "Erreur de connexion", message)
        
        # Effacer le mot de passe en cas d'erreur
        self.password_input.clear()
        self.password_input.setFocus()
    
    def closeEvent(self, event):
        """Gérer la fermeture de la fenêtre"""
        self.app_instance.on_closing()
        event.accept()
