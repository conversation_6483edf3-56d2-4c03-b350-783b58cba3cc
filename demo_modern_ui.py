#!/usr/bin/env python3
"""
Démonstration des composants UI modernes de GSlim
Application de test pour présenter les nouvelles fonctionnalités de design
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTabWidget, QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

from widgets.modern_widgets import ModernStatCard, ModernProgressBar
from widgets.advanced_ui import FloatingActionButton, NotificationToast, LoadingSpinner
from widgets.modern_navigation import ModernNavigationPanel
from styles.modern_theme import ModernTheme


class ModernUIDemo(QMainWindow):
    """Fenêtre de démonstration des composants UI modernes"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("GSlim - Démonstration UI Moderne")
        self.setGeometry(100, 100, 1200, 800)
        
        # Appliquer le thème moderne
        self.setStyleSheet(ModernTheme.get_modern_stylesheet())
        
        self.setup_ui()
        self.setup_demo_timer()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Navigation
        self.create_navigation(main_layout)
        
        # Zone de contenu avec onglets
        self.create_content_tabs(main_layout)
        
        central_widget.setLayout(main_layout)
        
        # Bouton d'action flottant
        self.create_floating_elements()
    
    def create_navigation(self, layout):
        """Créer la navigation de démonstration"""
        self.navigation = ModernNavigationPanel()
        self.navigation.page_changed.connect(self.on_page_changed)
        
        # Ajouter les éléments de navigation
        self.navigation.add_navigation_item("components", "Composants")
        self.navigation.add_navigation_item("animations", "Animations")
        self.navigation.add_navigation_item("themes", "Thèmes")
        self.navigation.add_navigation_item("examples", "Exemples")
        
        self.navigation.set_active_page("components")
        layout.addWidget(self.navigation)
    
    def create_content_tabs(self, layout):
        """Créer les onglets de contenu"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # Onglet Cartes de Statistiques
        self.create_stats_tab()
        
        # Onglet Boutons et Interactions
        self.create_buttons_tab()
        
        # Onglet Animations
        self.create_animations_tab()
        
        # Onglet Notifications
        self.create_notifications_tab()
        
        layout.addWidget(self.tab_widget)
    
    def create_stats_tab(self):
        """Créer l'onglet des cartes de statistiques"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(32, 32, 32, 32)
        layout.setSpacing(24)
        
        # Titre
        title = QLabel("Cartes de Statistiques Modernes")
        title.setProperty("class", "title")
        layout.addWidget(title)
        
        # Description
        desc = QLabel("Cartes interactives avec animations et différents styles")
        desc.setProperty("class", "body")
        desc.setStyleSheet(f"color: {ModernTheme.LIGHT_TEXT_SECONDARY};")
        layout.addWidget(desc)
        
        # Grille de cartes
        cards_layout = QHBoxLayout()
        cards_layout.setSpacing(16)
        
        # Carte primaire
        self.primary_card = ModernStatCard(
            "Ventes Totales", "1,234", "↗ +12% ce mois", "primary"
        )
        self.primary_card.clicked.connect(lambda: self.show_toast("info", "Carte Cliquée", "Carte des ventes totales"))
        cards_layout.addWidget(self.primary_card)
        
        # Carte de succès
        self.success_card = ModernStatCard(
            "Commandes", "89", "✓ Toutes livrées", "success"
        )
        self.success_card.clicked.connect(lambda: self.show_toast("success", "Succès", "Toutes les commandes sont livrées"))
        cards_layout.addWidget(self.success_card)
        
        # Carte d'avertissement
        self.warning_card = ModernStatCard(
            "Stock Faible", "23", "⚠ Attention requise", "warning"
        )
        self.warning_card.clicked.connect(lambda: self.show_toast("warning", "Attention", "23 articles en stock faible"))
        cards_layout.addWidget(self.warning_card)
        
        # Carte d'erreur
        self.error_card = ModernStatCard(
            "Erreurs", "3", "🔴 À résoudre", "error"
        )
        self.error_card.clicked.connect(lambda: self.show_toast("error", "Erreur", "3 erreurs nécessitent votre attention"))
        cards_layout.addWidget(self.error_card)
        
        layout.addLayout(cards_layout)
        
        # Section des barres de progression
        progress_title = QLabel("Barres de Progression Animées")
        progress_title.setProperty("class", "heading")
        layout.addWidget(progress_title)
        
        # Barres de progression
        progress_layout = QVBoxLayout()
        progress_layout.setSpacing(16)
        
        for i, (label, value) in enumerate([
            ("Objectif Mensuel", 75),
            ("Satisfaction Client", 92),
            ("Performance Équipe", 68),
            ("Croissance Annuelle", 84)
        ]):
            label_widget = QLabel(label)
            label_widget.setProperty("class", "body")
            progress_layout.addWidget(label_widget)
            
            progress = ModernProgressBar(0, 100)
            progress.setValue(value, animate=True)
            progress_layout.addWidget(progress)
        
        layout.addLayout(progress_layout)
        layout.addStretch()
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "Cartes & Statistiques")
    
    def create_buttons_tab(self):
        """Créer l'onglet des boutons"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(32, 32, 32, 32)
        layout.setSpacing(24)
        
        # Titre
        title = QLabel("Boutons et Interactions")
        title.setProperty("class", "title")
        layout.addWidget(title)
        
        # Différents types de boutons
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(16)
        
        # Boutons primaires
        primary_layout = QHBoxLayout()
        primary_btn = QPushButton("Bouton Primaire")
        primary_btn.clicked.connect(lambda: self.show_toast("info", "Clic", "Bouton primaire cliqué"))
        primary_layout.addWidget(primary_btn)
        
        secondary_btn = QPushButton("Bouton Secondaire")
        secondary_btn.setProperty("class", "secondary")
        secondary_btn.clicked.connect(lambda: self.show_toast("info", "Clic", "Bouton secondaire cliqué"))
        primary_layout.addWidget(secondary_btn)
        
        outline_btn = QPushButton("Bouton Outline")
        outline_btn.setProperty("class", "outline")
        outline_btn.clicked.connect(lambda: self.show_toast("info", "Clic", "Bouton outline cliqué"))
        primary_layout.addWidget(outline_btn)
        
        ghost_btn = QPushButton("Bouton Ghost")
        ghost_btn.setProperty("class", "ghost")
        ghost_btn.clicked.connect(lambda: self.show_toast("info", "Clic", "Bouton ghost cliqué"))
        primary_layout.addWidget(ghost_btn)
        
        primary_layout.addStretch()
        buttons_layout.addLayout(primary_layout)
        
        # Boutons d'état
        state_layout = QHBoxLayout()
        success_btn = QPushButton("Succès")
        success_btn.setProperty("class", "success")
        success_btn.clicked.connect(lambda: self.show_toast("success", "Succès", "Action réussie"))
        state_layout.addWidget(success_btn)
        
        warning_btn = QPushButton("Avertissement")
        warning_btn.setProperty("class", "warning")
        warning_btn.clicked.connect(lambda: self.show_toast("warning", "Attention", "Action d'avertissement"))
        state_layout.addWidget(warning_btn)
        
        danger_btn = QPushButton("Danger")
        danger_btn.setProperty("class", "danger")
        danger_btn.clicked.connect(lambda: self.show_toast("error", "Danger", "Action dangereuse"))
        state_layout.addWidget(danger_btn)
        
        state_layout.addStretch()
        buttons_layout.addLayout(state_layout)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "Boutons")
    
    def create_animations_tab(self):
        """Créer l'onglet des animations"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(32, 32, 32, 32)
        layout.setSpacing(24)
        
        # Titre
        title = QLabel("Animations et Micro-interactions")
        title.setProperty("class", "title")
        layout.addWidget(title)
        
        # Spinner de chargement
        spinner_layout = QHBoxLayout()
        spinner_label = QLabel("Spinner de chargement:")
        spinner_label.setProperty("class", "body")
        spinner_layout.addWidget(spinner_label)
        
        self.spinner = LoadingSpinner(48)
        self.spinner.start()
        spinner_layout.addWidget(self.spinner)
        
        spinner_layout.addStretch()
        layout.addLayout(spinner_layout)
        
        # Boutons de contrôle des animations
        control_layout = QHBoxLayout()
        
        animate_cards_btn = QPushButton("Animer les Cartes")
        animate_cards_btn.clicked.connect(self.animate_stat_cards)
        control_layout.addWidget(animate_cards_btn)
        
        update_values_btn = QPushButton("Mettre à Jour les Valeurs")
        update_values_btn.clicked.connect(self.update_card_values)
        control_layout.addWidget(update_values_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        layout.addStretch()
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "Animations")
    
    def create_notifications_tab(self):
        """Créer l'onglet des notifications"""
        tab = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(32, 32, 32, 32)
        layout.setSpacing(24)
        
        # Titre
        title = QLabel("Notifications Toast")
        title.setProperty("class", "title")
        layout.addWidget(title)
        
        # Boutons de test des notifications
        notifications_layout = QHBoxLayout()
        
        info_btn = QPushButton("Notification Info")
        info_btn.setProperty("class", "outline")
        info_btn.clicked.connect(lambda: self.show_toast("info", "Information", "Ceci est une notification d'information"))
        notifications_layout.addWidget(info_btn)
        
        success_btn = QPushButton("Notification Succès")
        success_btn.setProperty("class", "success")
        success_btn.clicked.connect(lambda: self.show_toast("success", "Succès", "Opération réalisée avec succès"))
        notifications_layout.addWidget(success_btn)
        
        warning_btn = QPushButton("Notification Avertissement")
        warning_btn.setProperty("class", "warning")
        warning_btn.clicked.connect(lambda: self.show_toast("warning", "Attention", "Ceci est un avertissement important"))
        notifications_layout.addWidget(warning_btn)
        
        error_btn = QPushButton("Notification Erreur")
        error_btn.setProperty("class", "danger")
        error_btn.clicked.connect(lambda: self.show_toast("error", "Erreur", "Une erreur s'est produite"))
        notifications_layout.addWidget(error_btn)
        
        notifications_layout.addStretch()
        layout.addLayout(notifications_layout)
        
        layout.addStretch()
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "Notifications")
    
    def create_floating_elements(self):
        """Créer les éléments flottants"""
        self.fab = FloatingActionButton("✨")
        self.fab.setParent(self)
        self.fab.clicked.connect(self.on_fab_clicked)
        
        # Positionner le FAB
        self.fab.move(self.width() - 80, self.height() - 80)
    
    def setup_demo_timer(self):
        """Configurer le timer de démonstration"""
        self.demo_timer = QTimer()
        self.demo_timer.timeout.connect(self.demo_animation)
        self.demo_timer.start(10000)  # Animation toutes les 10 secondes
    
    def demo_animation(self):
        """Animation de démonstration automatique"""
        self.update_card_values()
    
    def animate_stat_cards(self):
        """Animer les cartes de statistiques"""
        if hasattr(self, 'primary_card'):
            # Simuler des animations sur les cartes
            self.show_toast("info", "Animation", "Animation des cartes en cours...")
    
    def update_card_values(self):
        """Mettre à jour les valeurs des cartes avec animation"""
        import random
        
        if hasattr(self, 'primary_card'):
            new_value = random.randint(1000, 2000)
            self.primary_card.update_value(str(new_value), animate=True)
        
        if hasattr(self, 'success_card'):
            new_value = random.randint(50, 150)
            self.success_card.update_value(str(new_value), animate=True)
        
        if hasattr(self, 'warning_card'):
            new_value = random.randint(10, 50)
            self.warning_card.update_value(str(new_value), animate=True)
        
        if hasattr(self, 'error_card'):
            new_value = random.randint(0, 10)
            self.error_card.update_value(str(new_value), animate=True)
    
    def on_page_changed(self, route_key: str):
        """Gérer le changement de page dans la navigation"""
        self.show_toast("info", "Navigation", f"Page changée vers: {route_key}")
    
    def on_fab_clicked(self):
        """Gérer le clic sur le bouton d'action flottant"""
        self.show_toast("info", "Action Rapide", "Bouton d'action flottant activé!")
    
    def show_toast(self, toast_type: str, title: str, message: str):
        """Afficher une notification toast"""
        toast = NotificationToast(title, message, toast_type, 3000, self)
        toast.show()
    
    def resizeEvent(self, event):
        """Gérer le redimensionnement"""
        super().resizeEvent(event)
        if hasattr(self, 'fab'):
            self.fab.move(self.width() - 80, self.height() - 80)


def main():
    """Fonction principale"""
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("GSlim UI Demo")
    app.setApplicationVersion("1.0.0")
    
    # Créer et afficher la fenêtre
    window = ModernUIDemo()
    window.show()
    
    # Démarrer la boucle d'événements
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
