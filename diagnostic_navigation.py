#!/usr/bin/env python3
"""
Script de diagnostic pour identifier le problème de navigation
"""

import sys
import os

# Ajouter src au path
sys.path.insert(0, 'src')

def test_fluent_icons():
    """Tester les icônes FluentIcon disponibles"""
    print("🔍 Test des icônes FluentIcon...")
    
    try:
        from qfluentwidgets import FluentIcon
        
        # Tester les icônes utilisées
        icons_to_test = [
            'HOME', 'SHOPPING_CART', 'PEOPLE', 'CONTACT', 'MARKET', 
            'CALENDAR', 'SETTING', 'INFO', 'CANCEL', 'CLOSE',
            'DOLLAR', 'MONEY'
        ]
        
        available_icons = []
        missing_icons = []
        
        for icon_name in icons_to_test:
            try:
                icon = getattr(FluentIcon, icon_name)
                available_icons.append(icon_name)
                print(f"✅ FluentIcon.{icon_name} - Disponible")
            except AttributeError:
                missing_icons.append(icon_name)
                print(f"❌ FluentIcon.{icon_name} - MANQUANTE")
        
        print(f"\n📊 Résumé:")
        print(f"   • Icônes disponibles: {len(available_icons)}")
        print(f"   • Icônes manquantes: {len(missing_icons)}")
        
        if missing_icons:
            print(f"\n⚠️  Icônes à remplacer: {', '.join(missing_icons)}")
        
        return missing_icons
        
    except ImportError:
        print("❌ PyQt-Fluent-Widgets non disponible")
        return []

def test_navigation_interface():
    """Tester l'interface de navigation"""
    print("\n🧭 Test de l'interface de navigation...")
    
    try:
        from qfluentwidgets import FluentWindow, NavigationItemPosition
        print("✅ FluentWindow importé avec succès")
        print("✅ NavigationItemPosition importé avec succès")
        
        # Tester la création d'une fenêtre
        from PyQt5.QtWidgets import QApplication
        app = QApplication([])
        
        window = FluentWindow()
        print("✅ FluentWindow créé avec succès")
        
        # Tester l'ajout d'un widget simple
        from PyQt5.QtWidgets import QWidget, QLabel
        test_widget = QWidget()
        test_widget.setObjectName('test_widget')
        
        try:
            window.addSubInterface(
                test_widget,
                None,  # Pas d'icône pour le test
                'Test',
                'test'
            )
            print("✅ addSubInterface fonctionne")
        except Exception as e:
            print(f"❌ Erreur addSubInterface: {e}")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Erreur navigation: {e}")
        return False

def test_widget_object_names():
    """Tester les noms d'objets des widgets"""
    print("\n🏷️  Test des noms d'objets...")
    
    try:
        from PyQt5.QtWidgets import QWidget
        
        # Tester différents noms
        test_names = ['dashboard_widget', 'products_widget', 'test', '']
        
        for name in test_names:
            widget = QWidget()
            try:
                widget.setObjectName(name)
                actual_name = widget.objectName()
                if name == '':
                    print(f"⚠️  Nom vide: '{name}' → '{actual_name}'")
                else:
                    print(f"✅ Nom valide: '{name}' → '{actual_name}'")
            except Exception as e:
                print(f"❌ Erreur nom '{name}': {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test noms: {e}")
        return False

def main():
    """Fonction principale de diagnostic"""
    print("🔧 DIAGNOSTIC DE NAVIGATION GSLIM")
    print("=" * 50)
    
    # Test 1: Icônes FluentIcon
    missing_icons = test_fluent_icons()
    
    # Test 2: Interface de navigation
    nav_ok = test_navigation_interface()
    
    # Test 3: Noms d'objets
    names_ok = test_widget_object_names()
    
    print("\n📋 RÉSUMÉ DU DIAGNOSTIC:")
    print("=" * 30)
    
    if missing_icons:
        print(f"⚠️  Icônes manquantes: {', '.join(missing_icons)}")
        print("   → Remplacer par des icônes existantes")
    else:
        print("✅ Toutes les icônes sont disponibles")
    
    if nav_ok:
        print("✅ Interface de navigation fonctionnelle")
    else:
        print("❌ Problème avec l'interface de navigation")
    
    if names_ok:
        print("✅ Noms d'objets fonctionnels")
    else:
        print("❌ Problème avec les noms d'objets")
    
    print("\n🎯 RECOMMANDATIONS:")
    if missing_icons:
        print("1. Remplacer les icônes manquantes dans dashboard_widget.py")
    if not nav_ok:
        print("2. Vérifier l'installation de PyQt-Fluent-Widgets")
    
    print("3. Vérifier que tous les widgets ont des objectName non vides")
    print("4. Tester l'application après corrections")

if __name__ == "__main__":
    main()
