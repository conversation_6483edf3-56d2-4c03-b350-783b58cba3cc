#!/usr/bin/env python3
"""
Test de l'authentification GSlim
Vérifie que les méthodes d'authentification fonctionnent
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_authentication_methods():
    """Tester les méthodes d'authentification"""
    print("🧪 TEST DES MÉTHODES D'AUTHENTIFICATION")
    print("="*50)
    
    try:
        from database.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        print("✅ DatabaseManager créé")
        
        # Vérifier que les méthodes existent
        if hasattr(db_manager, 'authenticate_user'):
            print("✅ Méthode authenticate_user trouvée")
        else:
            print("❌ Méthode authenticate_user manquante")
            return False
        
        if hasattr(db_manager, 'close'):
            print("✅ Méthode close trouvée")
        else:
            print("❌ Méthode close manquante")
            return False
        
        # Tester l'authentification admin
        print("\n🔐 Test authentification admin...")
        user = db_manager.authenticate_user("admin", "admin123")
        if user:
            print(f"✅ Authentification admin réussie: {user['username']} ({user['role']})")
        else:
            print("❌ Authentification admin échouée")
            return False
        
        # Tester l'authentification utilisateur
        print("\n🔐 Test authentification utilisateur...")
        user = db_manager.authenticate_user("test", "test")
        if user:
            print(f"✅ Authentification utilisateur réussie: {user['username']} ({user['role']})")
        else:
            print("❌ Authentification utilisateur échouée")
            return False
        
        # Tester l'authentification échouée
        print("\n🔐 Test authentification invalide...")
        user = db_manager.authenticate_user("invalid", "wrong")
        if user is None:
            print("✅ Authentification invalide correctement rejetée")
        else:
            print("❌ Authentification invalide acceptée à tort")
            return False
        
        # Tester la méthode close
        print("\n🔧 Test méthode close...")
        db_manager.connect()
        db_manager.close()
        print("✅ Méthode close fonctionne")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_login_credentials():
    """Tester différents identifiants de connexion"""
    print("\n🧪 TEST DES IDENTIFIANTS DE CONNEXION")
    print("="*50)
    
    try:
        from database.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        
        # Identifiants à tester
        test_credentials = [
            ("admin", "admin123", True, "Administrateur principal"),
            ("test", "test", True, "Utilisateur de test"),
            ("demo", "demo", True, "Utilisateur de démonstration"),
            ("user", "123", True, "Utilisateur simple"),
            ("wrong", "password", False, "Identifiants invalides"),
            ("admin", "wrong", False, "Mot de passe incorrect"),
            ("", "", False, "Identifiants vides")
        ]
        
        print("🔐 Test des différents identifiants:")
        print("-" * 60)
        
        for username, password, should_succeed, description in test_credentials:
            user = db_manager.authenticate_user(username, password)
            
            if should_succeed:
                if user:
                    print(f"✅ {description}: {username}/{password} → {user['role']}")
                else:
                    print(f"❌ {description}: {username}/{password} → Échec inattendu")
            else:
                if user is None:
                    print(f"✅ {description}: {username}/{password} → Rejeté correctement")
                else:
                    print(f"❌ {description}: {username}/{password} → Accepté à tort")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


def create_login_guide():
    """Créer un guide de connexion"""
    print("\n📋 CRÉATION DU GUIDE DE CONNEXION")
    print("="*50)
    
    guide_content = """# 🔐 GUIDE DE CONNEXION - GSLIM

## 🎯 **IDENTIFIANTS DE CONNEXION DISPONIBLES**

### **👑 Administrateur**
- **Nom d'utilisateur:** `admin`
- **Mot de passe:** `admin123`
- **Rôle:** Administrateur complet
- **Accès:** Toutes les fonctionnalités

### **👤 Utilisateurs de Test**
- **Nom d'utilisateur:** `test` | **Mot de passe:** `test`
- **Nom d'utilisateur:** `demo` | **Mot de passe:** `demo`  
- **Nom d'utilisateur:** `user` | **Mot de passe:** `123`
- **Rôle:** Utilisateur standard
- **Accès:** Fonctionnalités de base

## 🚀 **COMMENT SE CONNECTER**

### **1. Lancer l'Application**
```bash
# Activer l'environnement virtuel
venv\\Scripts\\activate.bat

# Lancer l'application
python main.py
```

### **2. Écran de Connexion**
1. **Entrez vos identifiants** dans les champs
2. **Cliquez sur "Se connecter"**
3. **Accédez à l'interface principale**

### **3. Identifiants Recommandés**
- **Pour l'administration:** `admin` / `admin123`
- **Pour les tests:** `test` / `test`
- **Pour les démonstrations:** `demo` / `demo`

## 🎨 **FONCTIONNALITÉS DISPONIBLES**

### **✅ Interface Moderne**
- 🏠 **Dashboard** avec statistiques temps réel
- 📦 **Module Articles** avec recherche avancée
- 🏢 **Module Fournisseurs** avec gestion complète
- 🎨 **5 Thèmes** révolutionnaires

### **✅ Gestion Complète**
- 📊 **Statistiques** en temps réel
- 🔍 **Recherche et filtrage** avancés
- 📈 **Rapports** détaillés
- 🔔 **Alertes** de stock

## 🔧 **DÉPANNAGE**

### **Problèmes de Connexion**
- ✅ Vérifiez que vous utilisez les bons identifiants
- ✅ Respectez la casse (minuscules)
- ✅ Assurez-vous que l'environnement virtuel est activé

### **Identifiants Oubliés**
- 👑 **Admin:** `admin` / `admin123`
- 👤 **Test:** `test` / `test`
- 🎮 **Demo:** `demo` / `demo`

## 🎉 **PROFITEZ DE VOTRE INTERFACE !**

Une fois connecté, explorez toutes les fonctionnalités de votre interface GSlim révolutionnaire !

---

*Guide de connexion GSlim - Interface Révolutionnaire 2.0*
"""
    
    with open("GUIDE_CONNEXION.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ Guide de connexion créé: GUIDE_CONNEXION.md")
    return True


def main():
    """Fonction principale"""
    print("🔐 VÉRIFICATION DE L'AUTHENTIFICATION - GSLIM")
    print("="*60)
    
    tests = [
        ("Méthodes d'authentification", test_authentication_methods),
        ("Identifiants de connexion", test_login_credentials),
        ("Guide de connexion", create_login_guide)
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                success_count += 1
                print(f"\n✅ {test_name} - RÉUSSI")
            else:
                print(f"\n❌ {test_name} - ÉCHEC")
        except Exception as e:
            print(f"\n❌ {test_name} - ERREUR: {e}")
    
    print(f"\n📊 RÉSULTATS FINAUX")
    print("="*30)
    print(f"Tests réussis: {success_count}/{len(tests)}")
    
    if success_count == len(tests):
        print("\n🎉 AUTHENTIFICATION COMPLÈTEMENT FONCTIONNELLE !")
        print("✅ Toutes les méthodes d'authentification fonctionnent")
        print("✅ Identifiants de connexion configurés")
        print("✅ Guide de connexion créé")
        
        print("\n🔐 IDENTIFIANTS DISPONIBLES:")
        print("   👑 Admin: admin / admin123")
        print("   👤 Test: test / test")
        print("   🎮 Demo: demo / demo")
        
        print("\n🚀 Votre application est prête !")
        print("   python main.py")
        
        return True
    else:
        print("\n⚠️  Certains tests ont échoué")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
