#!/usr/bin/env python3
"""
Correctif Interface GSlim - Résolution des erreurs de modules
Corrige les erreurs de chargement des modules articles et fournisseurs
"""

import sys
import os
import sqlite3
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def create_missing_controllers():
    """Créer les contrôleurs manquants"""
    print("🔧 Création des contrôleurs manquants...")
    
    # Créer le répertoire controllers s'il n'existe pas
    controllers_dir = "src/controllers"
    if not os.path.exists(controllers_dir):
        os.makedirs(controllers_dir)
        print(f"✅ Répertoire {controllers_dir} créé")
    
    # Créer supplier_controller.py
    supplier_controller_content = '''"""
Contrôleur pour la gestion des fournisseurs
"""

from utils.logger import setup_logger


class SupplierController:
    """Contrôleur pour la gestion des fournisseurs"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
    
    def get_all_suppliers(self):
        """Récupérer tous les fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return []
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("SELECT * FROM suppliers ORDER BY nom")
            suppliers = cursor.fetchall()
            
            # Convertir en liste de dictionnaires
            if suppliers:
                columns = [desc[0] for desc in cursor.description]
                return [dict(zip(columns, row)) for row in suppliers]
            return []
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des fournisseurs: {e}")
            return []
    
    def create_supplier(self, supplier_data):
        """Créer un nouveau fournisseur"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                raise Exception("Base de données non connectée")
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            query = """
                INSERT INTO suppliers (nom, contact, telephone, email, adresse)
                VALUES (?, ?, ?, ?, ?)
            """
            
            cursor.execute(query, (
                supplier_data.get('nom'),
                supplier_data.get('contact'),
                supplier_data.get('telephone'),
                supplier_data.get('email'),
                supplier_data.get('adresse')
            ))
            
            self.db_manager.connection.commit()
            return cursor.lastrowid
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création du fournisseur: {e}")
            raise
    
    def get_statistics(self):
        """Récupérer les statistiques des fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'total_suppliers': 0}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'total_suppliers': 0}
            
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            total = cursor.fetchone()[0] if cursor.fetchone() else 0
            
            return {'total_suppliers': total}
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {'total_suppliers': 0}
'''
    
    supplier_file = f"{controllers_dir}/supplier_controller.py"
    with open(supplier_file, 'w', encoding='utf-8') as f:
        f.write(supplier_controller_content)
    
    print("✅ SupplierController créé")
    
    return True


def fix_database_initialization():
    """Corriger l'initialisation de la base de données"""
    print("🔧 Correction de l'initialisation de la base de données...")
    
    # Créer un script d'initialisation de la base de données
    init_db_content = '''"""
Script d'initialisation de la base de données GSlim
"""

import sqlite3
import os
from pathlib import Path


def initialize_database(db_path="data/gslim.db"):
    """Initialiser la base de données avec les tables nécessaires"""
    
    # Créer le répertoire data s'il n'existe pas
    data_dir = Path(db_path).parent
    data_dir.mkdir(exist_ok=True)
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Créer les tables
    tables = [
        # Table articles
        """
        CREATE TABLE IF NOT EXISTS articles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            description TEXT,
            prix_unitaire REAL NOT NULL,
            quantite_stock INTEGER NOT NULL,
            seuil_alerte INTEGER DEFAULT 0,
            categorie_id INTEGER,
            fournisseur_id INTEGER,
            date_creation TEXT,
            date_modification TEXT
        )
        """,
        
        # Table suppliers
        """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            contact TEXT,
            telephone TEXT,
            email TEXT,
            adresse TEXT,
            date_creation TEXT,
            date_modification TEXT
        )
        """,
        
        # Table categories
        """
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL UNIQUE,
            description TEXT
        )
        """,
        
        # Table sales
        """
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            article_id INTEGER,
            quantite INTEGER NOT NULL,
            prix_unitaire REAL NOT NULL,
            total REAL NOT NULL,
            date_vente TEXT,
            FOREIGN KEY (article_id) REFERENCES articles (id)
        )
        """,
        
        # Table orders
        """
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER,
            statut TEXT DEFAULT 'en_attente',
            date_commande TEXT,
            date_livraison TEXT,
            total REAL,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
        """,
        
        # Table movements
        """
        CREATE TABLE IF NOT EXISTS movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            article_id INTEGER,
            type_mouvement TEXT NOT NULL,
            quantite INTEGER NOT NULL,
            date_mouvement TEXT,
            commentaire TEXT,
            FOREIGN KEY (article_id) REFERENCES articles (id)
        )
        """
    ]
    
    # Exécuter la création des tables
    for table_sql in tables:
        cursor.execute(table_sql)
    
    # Insérer des données d'exemple si les tables sont vides
    cursor.execute("SELECT COUNT(*) FROM suppliers")
    if cursor.fetchone()[0] == 0:
        sample_suppliers = [
            ("TechSupply", "Jean Dupont", "01-23-45-67-89", "<EMAIL>", "123 Rue de la Tech"),
            ("Bureau Plus", "Marie Martin", "01-98-76-54-32", "<EMAIL>", "456 Avenue du Bureau"),
            ("Informatique Pro", "Pierre Durand", "01-11-22-33-44", "<EMAIL>", "789 Boulevard Info")
        ]
        
        for supplier in sample_suppliers:
            cursor.execute("""
                INSERT INTO suppliers (nom, contact, telephone, email, adresse)
                VALUES (?, ?, ?, ?, ?)
            """, supplier)
    
    cursor.execute("SELECT COUNT(*) FROM categories")
    if cursor.fetchone()[0] == 0:
        sample_categories = [
            ("Électronique", "Appareils électroniques"),
            ("Bureau", "Fournitures de bureau"),
            ("Informatique", "Matériel informatique")
        ]
        
        for category in sample_categories:
            cursor.execute("INSERT INTO categories (nom, description) VALUES (?, ?)", category)
    
    cursor.execute("SELECT COUNT(*) FROM articles")
    if cursor.fetchone()[0] == 0:
        from datetime import datetime
        now = datetime.now().isoformat()
        
        sample_articles = [
            ("Ordinateur Portable", "Laptop 15 pouces", 899.99, 15, 5, 3, 3, now, now),
            ("Souris Optique", "Souris USB optique", 25.50, 50, 10, 1, 1, now, now),
            ("Clavier Mécanique", "Clavier gaming RGB", 129.99, 20, 5, 1, 1, now, now),
            ("Écran 24 pouces", "Moniteur Full HD", 199.99, 8, 3, 1, 1, now, now),
            ("Stylos Bille", "Lot de 10 stylos", 5.99, 100, 20, 2, 2, now, now)
        ]
        
        for article in sample_articles:
            cursor.execute("""
                INSERT INTO articles (
                    nom, description, prix_unitaire, quantite_stock, 
                    seuil_alerte, categorie_id, fournisseur_id, 
                    date_creation, date_modification
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, article)
    
    # Valider les changements
    conn.commit()
    conn.close()
    
    print(f"✅ Base de données initialisée: {db_path}")
    return True


if __name__ == "__main__":
    initialize_database()
'''
    
    init_file = "src/database/init_db.py"
    with open(init_file, 'w', encoding='utf-8') as f:
        f.write(init_db_content)
    
    print("✅ Script d'initialisation de la base de données créé")
    
    return True


def create_enhanced_suppliers_window():
    """Créer une fenêtre fournisseurs améliorée"""
    print("🔧 Création de la fenêtre fournisseurs améliorée...")
    
    suppliers_window_content = '''"""
Fenêtre Fournisseurs Améliorée - GSlim
Interface moderne pour la gestion des fournisseurs
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QFrame, QHeaderView, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

try:
    from controllers.supplier_controller import SupplierController
    from widgets.enhanced_widgets import EnhancedCard, EnhancedTable, StatusIndicator
    from utils.logger import setup_logger
except ImportError as e:
    print(f"Import error: {e}")


class EnhancedSuppliersWindow(QWidget):
    """Fenêtre fournisseurs améliorée"""
    
    def __init__(self, app_instance, parent=None):
        super().__init__(parent)
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        
        try:
            # Initialiser le contrôleur
            db_manager = app_instance.get_database_manager() if hasattr(app_instance, 'get_database_manager') else None
            if db_manager:
                self.controller = SupplierController(db_manager)
            else:
                self.controller = None
                self.logger.warning("Aucun gestionnaire de base de données disponible")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation du contrôleur: {e}")
            self.controller = None
        
        self.setup_ui()
        self.load_suppliers()
    
    def setup_ui(self):
        """Configurer l'interface utilisateur"""
        self.setWindowTitle("Gestion des Fournisseurs")
        self.setMinimumSize(1000, 700)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title = QLabel("Gestion des Fournisseurs")
        title.setFont(QFont("Segoe UI", 24, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        header_layout.addWidget(title)
        
        header_layout.addStretch()
        
        # Bouton nouveau fournisseur
        new_btn = QPushButton("➕ Nouveau Fournisseur")
        new_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        new_btn.clicked.connect(self.show_new_supplier_dialog)
        header_layout.addWidget(new_btn)
        
        main_layout.addLayout(header_layout)
        
        # Statistiques
        stats_layout = QHBoxLayout()
        
        try:
            if self.controller:
                stats = self.controller.get_statistics()
                total_suppliers = stats.get('total_suppliers', 0)
            else:
                total_suppliers = 0
            
            # Carte total fournisseurs
            total_card = self.create_stat_card("Total Fournisseurs", str(total_suppliers), "#3498db")
            stats_layout.addWidget(total_card)
            
            # Carte fournisseurs actifs
            active_card = self.create_stat_card("Fournisseurs Actifs", str(total_suppliers), "#27ae60")
            stats_layout.addWidget(active_card)
            
            # Carte nouvelles commandes
            orders_card = self.create_stat_card("Commandes en Cours", "0", "#f39c12")
            stats_layout.addWidget(orders_card)
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création des statistiques: {e}")
            # Carte d'erreur
            error_card = self.create_stat_card("Erreur", "N/A", "#e74c3c")
            stats_layout.addWidget(error_card)
        
        stats_layout.addStretch()
        main_layout.addLayout(stats_layout)
        
        # Barre de recherche
        search_layout = QHBoxLayout()
        
        search_label = QLabel("Rechercher:")
        search_label.setFont(QFont("Segoe UI", 12))
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Nom du fournisseur, contact, email...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        self.search_input.textChanged.connect(self.filter_suppliers)
        search_layout.addWidget(self.search_input)
        
        search_layout.addStretch()
        main_layout.addLayout(search_layout)
        
        # Table des fournisseurs
        self.create_suppliers_table()
        main_layout.addWidget(self.suppliers_table)
        
        # Appliquer le style général
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
    
    def create_stat_card(self, title, value, color):
        """Créer une carte de statistique"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                padding: 20px;
            }}
        """)
        card.setFixedSize(200, 100)
        
        layout = QVBoxLayout(card)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 28, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # Titre
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 12))
        title_label.setStyleSheet("color: #7f8c8d;")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        return card
    
    def create_suppliers_table(self):
        """Créer la table des fournisseurs"""
        self.suppliers_table = QTableWidget()
        
        # Colonnes
        headers = ["ID", "Nom", "Contact", "Téléphone", "Email", "Adresse", "Actions"]
        self.suppliers_table.setColumnCount(len(headers))
        self.suppliers_table.setHorizontalHeaderLabels(headers)
        
        # Style de la table
        self.suppliers_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                gridline-color: #e1e8ed;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #e1e8ed;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 12px;
                border: none;
                border-bottom: 2px solid #3498db;
                font-weight: bold;
            }
        """)
        
        # Configuration de la table
        self.suppliers_table.setAlternatingRowColors(True)
        self.suppliers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_table.horizontalHeader().setStretchLastSection(True)
        self.suppliers_table.verticalHeader().setVisible(False)
        
        # Ajuster les colonnes
        header = self.suppliers_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Contact
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Téléphone
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # Email
        header.setSectionResizeMode(5, QHeaderView.Stretch)  # Adresse
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions
    
    def load_suppliers(self):
        """Charger les fournisseurs"""
        try:
            if not self.controller:
                self.show_error("Contrôleur non disponible")
                return
            
            suppliers = self.controller.get_all_suppliers()
            self.populate_table(suppliers)
            
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des fournisseurs: {e}")
            self.show_error(f"Erreur de chargement: {e}")
    
    def populate_table(self, suppliers):
        """Remplir la table avec les fournisseurs"""
        self.suppliers_table.setRowCount(len(suppliers))
        
        for row, supplier in enumerate(suppliers):
            # ID
            self.suppliers_table.setItem(row, 0, QTableWidgetItem(str(supplier.get('id', ''))))
            
            # Nom
            self.suppliers_table.setItem(row, 1, QTableWidgetItem(supplier.get('nom', '')))
            
            # Contact
            self.suppliers_table.setItem(row, 2, QTableWidgetItem(supplier.get('contact', '')))
            
            # Téléphone
            self.suppliers_table.setItem(row, 3, QTableWidgetItem(supplier.get('telephone', '')))
            
            # Email
            self.suppliers_table.setItem(row, 4, QTableWidgetItem(supplier.get('email', '')))
            
            # Adresse
            self.suppliers_table.setItem(row, 5, QTableWidgetItem(supplier.get('adresse', '')))
            
            # Actions
            actions_btn = QPushButton("⚙️ Actions")
            actions_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            self.suppliers_table.setCellWidget(row, 6, actions_btn)
    
    def filter_suppliers(self):
        """Filtrer les fournisseurs selon la recherche"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.suppliers_table.rowCount()):
            show_row = False
            
            for col in range(self.suppliers_table.columnCount() - 1):  # Exclure la colonne Actions
                item = self.suppliers_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            
            self.suppliers_table.setRowHidden(row, not show_row)
    
    def show_new_supplier_dialog(self):
        """Afficher le dialogue de nouveau fournisseur"""
        QMessageBox.information(self, "Nouveau Fournisseur", 
                              "Fonctionnalité de création de fournisseur à implémenter.")
    
    def show_error(self, message):
        """Afficher un message d'erreur"""
        QMessageBox.critical(self, "Erreur", message)
'''
    
    suppliers_file = "src/views/enhanced_suppliers_window.py"
    with open(suppliers_file, 'w', encoding='utf-8') as f:
        f.write(suppliers_window_content)
    
    print("✅ Fenêtre fournisseurs améliorée créée")
    
    return True


def run_fixes():
    """Exécuter tous les correctifs"""
    print("🔧 CORRECTIFS INTERFACE GSLIM")
    print("="*40)
    
    fixes = [
        ("Création des contrôleurs manquants", create_missing_controllers),
        ("Correction de l'initialisation DB", fix_database_initialization),
        ("Création fenêtre fournisseurs", create_enhanced_suppliers_window)
    ]
    
    success_count = 0
    for fix_name, fix_func in fixes:
        print(f"\n📋 {fix_name}...")
        try:
            result = fix_func()
            if result:
                success_count += 1
                print(f"✅ {fix_name} - Succès")
            else:
                print(f"❌ {fix_name} - Échec")
        except Exception as e:
            print(f"❌ {fix_name} - Erreur: {e}")
    
    print(f"\n📊 Correctifs appliqués: {success_count}/{len(fixes)}")
    
    if success_count == len(fixes):
        print("\n🎉 TOUS LES CORRECTIFS APPLIQUÉS !")
        print("\n📋 Prochaines étapes:")
        print("1. Initialiser la DB: python src/database/init_db.py")
        print("2. Relancer l'application: python launch_enhanced.py")
        print("3. Tester les modules articles et fournisseurs")
        
        return True
    else:
        print("\n⚠️  Correctifs partiels appliqués")
        return False


def main():
    """Fonction principale"""
    try:
        success = run_fixes()
        return 0 if success else 1
    except Exception as e:
        print(f"💥 Erreur critique: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
