#!/usr/bin/env python3
"""
Test de la méthode initialize_database
Vérifie que la méthode manquante a été correctement ajoutée
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_initialize_database():
    """Tester la méthode initialize_database"""
    print("🧪 TEST DE LA MÉTHODE initialize_database")
    print("="*50)
    
    try:
        from database.manager import DatabaseManager
        
        print("✅ Import DatabaseManager réussi")
        
        # Créer une instance
        db_manager = DatabaseManager()
        print("✅ Instance DatabaseManager créée")
        
        # Vérifier que la méthode existe
        if hasattr(db_manager, 'initialize_database'):
            print("✅ Méthode initialize_database trouvée")
        else:
            print("❌ Méthode initialize_database manquante")
            return False
        
        # Tester l'initialisation
        print("\n🔧 Test d'initialisation de la base de données...")
        db_manager.initialize_database()
        print("✅ Initialisation réussie")
        
        # Vérifier que les tables sont créées
        print("\n🔍 Vérification des tables créées...")
        cursor = db_manager.cursor
        
        if cursor:
            # Lister les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            table_names = [table[0] for table in tables]
            expected_tables = ['articles', 'suppliers', 'categories', 'sales', 'orders', 'movements']
            
            print(f"Tables trouvées: {table_names}")
            
            for table in expected_tables:
                if table in table_names:
                    print(f"✅ Table {table} créée")
                else:
                    print(f"❌ Table {table} manquante")
            
            # Tester une requête simple
            cursor.execute("SELECT COUNT(*) FROM articles")
            count = cursor.fetchone()[0]
            print(f"✅ {count} articles dans la base")
            
        else:
            print("❌ Impossible d'obtenir un curseur")
            return False
        
        # Fermer la connexion
        db_manager.disconnect()
        print("✅ Connexion fermée proprement")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_app_compatibility():
    """Tester la compatibilité avec l'application"""
    print("\n🧪 TEST DE COMPATIBILITÉ AVEC L'APPLICATION")
    print("="*50)
    
    try:
        # Simuler ce que fait l'application
        from database.manager import DatabaseManager
        
        print("🔧 Simulation du démarrage de l'application...")
        
        # Créer le gestionnaire comme dans l'app
        db_manager = DatabaseManager()
        print("✅ DatabaseManager créé")
        
        # Initialiser comme dans l'app
        db_manager.initialize_database()
        print("✅ Base de données initialisée")
        
        # Tester les opérations de base
        cursor = db_manager.cursor
        if cursor:
            # Test d'insertion simple
            cursor.execute("""
                INSERT OR IGNORE INTO categories (name, description) 
                VALUES ('Test', 'Catégorie de test')
            """)
            db_manager.connection.commit()
            print("✅ Insertion test réussie")
            
            # Test de lecture
            cursor.execute("SELECT COUNT(*) FROM categories")
            count = cursor.fetchone()[0]
            print(f"✅ {count} catégories dans la base")
        
        db_manager.disconnect()
        print("✅ Test de compatibilité réussi")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur de compatibilité: {e}")
        return False


def main():
    """Fonction principale"""
    print("🔧 VÉRIFICATION DE LA CORRECTION initialize_database")
    print("="*60)
    
    tests = [
        ("Méthode initialize_database", test_initialize_database),
        ("Compatibilité application", test_app_compatibility)
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                success_count += 1
                print(f"\n✅ {test_name} - RÉUSSI")
            else:
                print(f"\n❌ {test_name} - ÉCHEC")
        except Exception as e:
            print(f"\n❌ {test_name} - ERREUR: {e}")
    
    print(f"\n📊 RÉSULTATS FINAUX")
    print("="*30)
    print(f"Tests réussis: {success_count}/{len(tests)}")
    
    if success_count == len(tests):
        print("\n🎉 CORRECTION RÉUSSIE !")
        print("✅ La méthode initialize_database fonctionne parfaitement")
        print("✅ L'application devrait maintenant démarrer sans erreur")
        print("\n🚀 Pour tester avec l'interface graphique:")
        print("   1. Activez l'environnement virtuel: venv\\Scripts\\activate.bat")
        print("   2. Lancez l'application: python main.py")
        print("   3. Ou utilisez l'interface améliorée: python launch_enhanced.py")
        
        return True
    else:
        print("\n⚠️  Certains tests ont échoué")
        print("Des corrections supplémentaires peuvent être nécessaires")
        return False


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
