"""
Thème Futuriste Avancé pour GSlim
Design cyberpunk avec néons, hologrammes et animations sci-fi
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor, QPalette, QFont


class FuturisticTheme:
    """Thème futuriste avec design cyberpunk et néons"""
    
    # === PALETTE CYBERPUNK ===
    NEON_CYAN = "#00FFFF"
    NEON_PINK = "#FF00FF"
    NEON_GREEN = "#00FF41"
    NEON_BLUE = "#0080FF"
    NEON_PURPLE = "#8000FF"
    NEON_ORANGE = "#FF8000"
    NEON_YELLOW = "#FFFF00"
    
    # === COULEURS DE BASE ===
    DARK_VOID = "#0A0A0F"
    DEEP_SPACE = "#0F0F1A"
    COSMIC_BLUE = "#1A1A2E"
    NEBULA_PURPLE = "#16213E"
    PLASMA_GLOW = "#0F3460"
    
    # === COULEURS DE TEXTE ===
    HOLOGRAM_WHITE = "#E8F4FD"
    CYBER_BLUE = "#64FFDA"
    MATRIX_GREEN = "#00FF41"
    PLASMA_PINK = "#FF6EC7"
    ENERGY_YELLOW = "#FFD700"
    
    # === EFFETS SPÉCIAUX ===
    GLOW_SHADOW = "0 0 20px"
    NEON_SHADOW = "0 0 30px"
    HOLOGRAM_SHADOW = "0 0 40px"
    ENERGY_SHADOW = "0 0 50px"
    
    @classmethod
    def get_futuristic_stylesheet(cls):
        """Retourner le stylesheet futuriste complet"""
        return f"""
        /* === IMPORTATION DES POLICES FUTURISTES === */
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400; */ */ */700; */ */ */900&display=swap'); */ */ */
        @import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300; */ */ */400; */ */ */500; */ */ */600; */ */ */700&display=swap'); */ */ */
        
        /* === STYLES GÉNÉRAUX === */
        * {{
            font-family: 'Rajdhani', 'Orbitron', 'Consolas', monospace; */ */ */
            outline: none; */ */ */
            border: none; */ */ */
        }}
        
        QWidget {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {cls.DARK_VOID}, 
                stop:0.3 {cls.DEEP_SPACE}, 
                stop:0.7 {cls.COSMIC_BLUE}, 
                stop:1 {cls.NEBULA_PURPLE}); */ */ */
            color: {cls.HOLOGRAM_WHITE}; */ */ */
            font-size: 14px; */ */ */
            font-weight: 500; */ */ */
            selection-background-color: {cls.NEON_CYAN}; */ */ */
            selection-color: {cls.DARK_VOID}; */ */ */
        }}
        
        /* === FENÊTRE PRINCIPALE FUTURISTE === */
        QMainWindow {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {cls.DARK_VOID}, 
                stop:0.2 {cls.DEEP_SPACE}, 
                stop:0.5 {cls.COSMIC_BLUE}, 
                stop:0.8 {cls.NEBULA_PURPLE}, 
                stop:1 {cls.PLASMA_GLOW}); */ */ */
            border: 2px solid {cls.NEON_CYAN}; */ */ */
            border-radius: 0px; */ */ */
            /* box-shadow: {cls.HOLOGRAM_SHADOW} {cls.NEON_CYAN}; */ */ */
        }}
        
        /* === CONTENEURS HOLOGRAPHIQUES === */
        QWidget[class="holo-container"] {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(0, 255, 255, 0.05),
                stop:0.5 rgba(0, 128, 255, 0.03),
                stop:1 rgba(128, 0, 255, 0.05)); */ */ */
            border: 1px solid rgba(0, 255, 255, 0.3); */ */ */
            border-radius: 15px; */ */ */
            /* backdrop-/* filter: blur(20px); */ */ */
            /* box-shadow: inset 0 0 20px rgba(0, 255, 255, 0.1); */ */ */
        }}
        
        /* === CARTES CYBERPUNK === */
        QFrame[class="cyber-card"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(15, 15, 26, 0.9),
                stop:0.5 rgba(26, 26, 46, 0.8),
                stop:1 rgba(22, 33, 62, 0.9)); */ */ */
            border: 2px solid transparent; */ */ */
            border-image: linear-gradient(45deg, {cls.NEON_CYAN}, {cls.NEON_PURPLE}, {cls.NEON_PINK}) 1; */ */ */
            border-radius: 20px; */ */ */
            padding: 25px; */ */ */
            margin: 15px; */ */ */
            /* box-shadow: 
                {cls.GLOW_SHADOW} rgba(0, 255, 255, 0.3),
                inset 0 0 30px rgba(0, 255, 255, 0.05); */ */ */
        }}
        
        QFrame[class="cyber-card"]:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(22, 33, 62, 0.95),
                stop:0.5 rgba(15, 52, 96, 0.9),
                stop:1 rgba(26, 26, 46, 0.95)); */ */ */
            border-image: linear-gradient(45deg, {cls.NEON_PINK}, {cls.NEON_CYAN}, {cls.NEON_GREEN}) 1; */ */ */
            /* transform: translateY(-5px) scale(1.02); */ */ */
            /* box-shadow: 
                {cls.HOLOGRAM_SHADOW} rgba(255, 0, 255, 0.4),
                {cls.NEON_SHADOW} rgba(0, 255, 255, 0.3),
                inset 0 0 40px rgba(255, 0, 255, 0.1); */ */ */
            animation: hologram-flicker 2s infinite; */ */ */
        }}
        
        /* === CARTES DE STATISTIQUES NÉON === */
        QFrame[class="neon-stat-card"] {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(15, 15, 26, 0.85),
                stop:1 rgba(26, 26, 46, 0.85)); */ */ */
            border: 2px solid rgba(0, 255, 255, 0.6); */ */ */
            border-radius: 25px; */ */ */
            padding: 30px; */ */ */
            margin: 18px; */ */ */
            min-height: 180px; */ */ */
            /* backdrop-/* filter: blur(25px); */ */ */
            /* box-shadow: 
                {cls.GLOW_SHADOW} rgba(0, 255, 255, 0.4),
                inset 0 0 25px rgba(0, 255, 255, 0.08); */ */ */
        }}
        
        QFrame[class="neon-stat-card"]:hover {{
            border-color: rgba(255, 0, 255, 0.8); */ */ */
            /* transform: translateY(-8px) scale(1.03); */ */ */
            /* box-shadow: 
                {cls.ENERGY_SHADOW} rgba(255, 0, 255, 0.5),
                {cls.HOLOGRAM_SHADOW} rgba(0, 255, 255, 0.3),
                inset 0 0 35px rgba(255, 0, 255, 0.12); */ */ */
            /* transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94); */ */ */
        }}
        
        /* === CARTES SPÉCIALISÉES AVEC NÉONS === */
        QFrame[class="neon-primary"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 128, 255, 0.2),
                stop:1 rgba(0, 255, 255, 0.15)); */ */ */
            border: 2px solid {cls.NEON_CYAN}; */ */ */
            /* box-shadow: 
                {cls.NEON_SHADOW} {cls.NEON_CYAN},
                inset 0 0 30px rgba(0, 255, 255, 0.1); */ */ */
        }}
        
        QFrame[class="neon-success"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 255, 65, 0.2),
                stop:1 rgba(0, 255, 128, 0.15)); */ */ */
            border: 2px solid {cls.NEON_GREEN}; */ */ */
            /* box-shadow: 
                {cls.NEON_SHADOW} {cls.NEON_GREEN},
                inset 0 0 30px rgba(0, 255, 65, 0.1); */ */ */
        }}
        
        QFrame[class="neon-warning"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 128, 0, 0.2),
                stop:1 rgba(255, 255, 0, 0.15)); */ */ */
            border: 2px solid {cls.NEON_ORANGE}; */ */ */
            /* box-shadow: 
                {cls.NEON_SHADOW} {cls.NEON_ORANGE},
                inset 0 0 30px rgba(255, 128, 0, 0.1); */ */ */
        }}
        
        QFrame[class="neon-error"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 0, 255, 0.2),
                stop:1 rgba(255, 0, 128, 0.15)); */ */ */
            border: 2px solid {cls.NEON_PINK}; */ */ */
            /* box-shadow: 
                {cls.NEON_SHADOW} {cls.NEON_PINK},
                inset 0 0 30px rgba(255, 0, 255, 0.1); */ */ */
        }}
        
        QFrame[class="neon-info"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(128, 0, 255, 0.2),
                stop:1 rgba(0, 128, 255, 0.15)); */ */ */
            border: 2px solid {cls.NEON_PURPLE}; */ */ */
            /* box-shadow: 
                {cls.NEON_SHADOW} {cls.NEON_PURPLE},
                inset 0 0 30px rgba(128, 0, 255, 0.1); */ */ */
        }}
        
        /* === BOUTONS CYBERPUNK === */
        QPushButton {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 255, 255, 0.15),
                stop:0.5 rgba(0, 128, 255, 0.2),
                stop:1 rgba(128, 0, 255, 0.15)); */ */ */
            color: {cls.HOLOGRAM_WHITE}; */ */ */
            border: 2px solid {cls.NEON_CYAN}; */ */ */
            border-radius: 15px; */ */ */
            padding: 15px 30px; */ */ */
            font-size: 16px; */ */ */
            font-weight: 600; */ */ */
            font-family: 'Orbitron', monospace; */ */ */
            text-/* transform: uppercase; */ */ */
            letter-spacing: 1px; */ */ */
            min-height: 25px; */ */ */
            /* box-shadow: 
                {cls.GLOW_SHADOW} rgba(0, 255, 255, 0.4),
                inset 0 0 20px rgba(0, 255, 255, 0.05); */ */ */
        }}
        
        QPushButton:hover {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(255, 0, 255, 0.2),
                stop:0.5 rgba(0, 255, 255, 0.25),
                stop:1 rgba(0, 255, 65, 0.2)); */ */ */
            border-color: {cls.NEON_PINK}; */ */ */
            color: {cls.ENERGY_YELLOW}; */ */ */
            /* transform: translateY(-3px); */ */ */
            /* box-shadow: 
                {cls.ENERGY_SHADOW} rgba(255, 0, 255, 0.5),
                {cls.NEON_SHADOW} rgba(0, 255, 255, 0.3),
                inset 0 0 25px rgba(255, 0, 255, 0.1); */ */ */
            text-shadow: 0 0 10px {cls.ENERGY_YELLOW}; */ */ */
        }}
        
        QPushButton:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(0, 255, 65, 0.3),
                stop:1 rgba(255, 0, 255, 0.25)); */ */ */
            border-color: {cls.NEON_GREEN}; */ */ */
            /* transform: translateY(0px) scale(0.98); */ */ */
            /* box-shadow: 
                {cls.GLOW_SHADOW} rgba(0, 255, 65, 0.6),
                inset 0 0 30px rgba(0, 255, 65, 0.15); */ */ */
        }}
        
        QPushButton:disabled {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(64, 64, 64, 0.3),
                stop:1 rgba(32, 32, 32, 0.3)); */ */ */
            color: rgba(232, 244, 253, 0.3); */ */ */
            border-color: rgba(100, 255, 218, 0.2); */ */ */
            /* box-shadow: none; */ */ */
            text-shadow: none; */ */ */
        }}
        
        /* === BOUTONS SPÉCIALISÉS === */
        QPushButton[class="cyber-primary"] {{
            border-color: {cls.NEON_CYAN}; */ */ */
            /* box-shadow: {cls.NEON_SHADOW} {cls.NEON_CYAN}; */ */ */
        }}
        
        QPushButton[class="cyber-success"] {{
            border-color: {cls.NEON_GREEN}; */ */ */
            /* box-shadow: {cls.NEON_SHADOW} {cls.NEON_GREEN}; */ */ */
        }}
        
        QPushButton[class="cyber-warning"] {{
            border-color: {cls.NEON_ORANGE}; */ */ */
            /* box-shadow: {cls.NEON_SHADOW} {cls.NEON_ORANGE}; */ */ */
        }}
        
        QPushButton[class="cyber-danger"] {{
            border-color: {cls.NEON_PINK}; */ */ */
            /* box-shadow: {cls.NEON_SHADOW} {cls.NEON_PINK}; */ */ */
        }}
        
        QPushButton[class="cyber-ghost"] {{
            background: transparent; */ */ */
            border: 1px solid rgba(0, 255, 255, 0.3); */ */ */
            /* box-shadow: none; */ */ */
        }}
        
        QPushButton[class="cyber-ghost"]:hover {{
            background: rgba(0, 255, 255, 0.1); */ */ */
            border-color: {cls.NEON_CYAN}; */ */ */
            /* box-shadow: {cls.GLOW_SHADOW} rgba(0, 255, 255, 0.3); */ */ */
        }}
        
        /* === TYPOGRAPHIE FUTURISTE === */
        QLabel[class="cyber-title"] {{
            color: {cls.CYBER_BLUE}; */ */ */
            font-size: 36px; */ */ */
            font-weight: 900; */ */ */
            font-family: 'Orbitron', monospace; */ */ */
            text-/* transform: uppercase; */ */ */
            letter-spacing: 2px; */ */ */
            margin-bottom: 15px; */ */ */
            text-shadow: 
                0 0 10px {cls.CYBER_BLUE},
                0 0 20px {cls.CYBER_BLUE},
                0 0 30px {cls.CYBER_BLUE}; */ */ */
        }}
        
        QLabel[class="cyber-subtitle"] {{
            color: {cls.PLASMA_PINK}; */ */ */
            font-size: 22px; */ */ */
            font-weight: 600; */ */ */
            font-family: 'Rajdhani', monospace; */ */ */
            letter-spacing: 1px; */ */ */
            margin-bottom: 20px; */ */ */
            text-shadow: 0 0 15px {cls.PLASMA_PINK}; */ */ */
        }}
        
        QLabel[class="cyber-body"] {{
            color: {cls.HOLOGRAM_WHITE}; */ */ */
            font-size: 16px; */ */ */
            font-weight: 400; */ */ */
            font-family: 'Rajdhani', monospace; */ */ */
            line-height: 1.6; */ */ */
            letter-spacing: 0.5px; */ */ */
        }}
        
        QLabel[class="cyber-caption"] {{
            color: {cls.CYBER_BLUE}; */ */ */
            font-size: 13px; */ */ */
            font-weight: 300; */ */ */
            font-family: 'Rajdhani', monospace; */ */ */
            letter-spacing: 1px; */ */ */
            opacity: 0.8; */ */ */
        }}
        
        QLabel[class="neon-value"] {{
            color: {cls.ENERGY_YELLOW}; */ */ */
            font-size: 42px; */ */ */
            font-weight: 900; */ */ */
            font-family: 'Orbitron', monospace; */ */ */
            margin-bottom: 10px; */ */ */
            letter-spacing: -1px; */ */ */
            text-shadow: 
                0 0 10px {cls.ENERGY_YELLOW},
                0 0 20px {cls.ENERGY_YELLOW},
                0 0 30px {cls.ENERGY_YELLOW}; */ */ */
        }}
        
        QLabel[class="neon-label"] {{
            color: {cls.CYBER_BLUE}; */ */ */
            font-size: 16px; */ */ */
            font-weight: 500; */ */ */
            font-family: 'Rajdhani', monospace; */ */ */
            text-/* transform: uppercase; */ */ */
            letter-spacing: 1px; */ */ */
            opacity: 0.9; */ */ */
            text-shadow: 0 0 8px {cls.CYBER_BLUE}; */ */ */
        }}
        
        /* === CHAMPS DE SAISIE HOLOGRAPHIQUES === */
        QLineEdit, QTextEdit {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(15, 15, 26, 0.8),
                stop:1 rgba(26, 26, 46, 0.8)); */ */ */
            border: 2px solid rgba(0, 255, 255, 0.4); */ */ */
            border-radius: 12px; */ */ */
            padding: 16px 20px; */ */ */
            font-size: 16px; */ */ */
            font-family: 'Rajdhani', monospace; */ */ */
            color: {cls.HOLOGRAM_WHITE}; */ */ */
            /* backdrop-/* filter: blur(15px); */ */ */
            /* box-shadow: 
                inset 0 0 20px rgba(0, 255, 255, 0.05),
                {cls.GLOW_SHADOW} rgba(0, 255, 255, 0.2); */ */ */
        }}
        
        QLineEdit:focus, QTextEdit:focus {{
            border-color: {cls.NEON_CYAN}; */ */ */
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(22, 33, 62, 0.9),
                stop:1 rgba(15, 52, 96, 0.9)); */ */ */
            /* box-shadow: 
                0 0 0 3px rgba(0, 255, 255, 0.3),
                inset 0 0 25px rgba(0, 255, 255, 0.1),
                {cls.NEON_SHADOW} {cls.NEON_CYAN}; */ */ */
            color: {cls.ENERGY_YELLOW}; */ */ */
        }}
        
        QLineEdit:hover, QTextEdit:hover {{
            border-color: rgba(255, 0, 255, 0.6); */ */ */
            /* box-shadow: 
                inset 0 0 25px rgba(255, 0, 255, 0.08),
                {cls.GLOW_SHADOW} rgba(255, 0, 255, 0.3); */ */ */
        }}
        """
    
    @classmethod
    def get_animations_css(cls):
        """Retourner les animations CSS avancées"""
        return """
        /* === ANIMATIONS CYBERPUNK === */
        @keyframes hologram-flicker {
            0%, 100% { opacity: 1; */ */ */ }
            50% { opacity: 0.8; */ */ */ }
        }
        
        @keyframes neon-pulse {
            0%, 100% { 
                /* box-shadow: 0 0 20px rgba(0, 255, 255, 0.4); */ */ */
            }
            50% { 
                /* box-shadow: 0 0 40px rgba(0, 255, 255, 0.8); */ */ */
            }
        }
        
        @keyframes energy-flow {
            0% { 
                border-image: linear-gradient(45deg, #00FFFF, #FF00FF, #00FF41) 1; */ */ */
            }
            33% { 
                border-image: linear-gradient(45deg, #FF00FF, #00FF41, #0080FF) 1; */ */ */
            }
            66% { 
                border-image: linear-gradient(45deg, #00FF41, #0080FF, #00FFFF) 1; */ */ */
            }
            100% { 
                border-image: linear-gradient(45deg, #0080FF, #00FFFF, #FF00FF) 1; */ */ */
            }
        }
        
        @keyframes matrix-rain {
            0% { /* transform: translateY(-100%); */ */ */ opacity: 0; */ */ */ }
            10% { opacity: 1; */ */ */ }
            90% { opacity: 1; */ */ */ }
            100% { /* transform: translateY(100vh); */ */ */ opacity: 0; */ */ */ }
        }
        
        @keyframes cyber-glow {
            0%, 100% { 
                text-shadow: 
                    0 0 10px currentColor,
                    0 0 20px currentColor,
                    0 0 30px currentColor; */ */ */
            }
            50% { 
                text-shadow: 
                    0 0 20px currentColor,
                    0 0 30px currentColor,
                    0 0 40px currentColor,
                    0 0 50px currentColor; */ */ */
            }
        }
        """
    
    @classmethod
    def get_complete_futuristic_theme(cls):
        """Retourner le thème futuriste complet avec animations"""
        return cls.get_futuristic_stylesheet() + cls.get_animations_css()
    
    @classmethod
    def get_color_palette(cls):
        """Retourner la palette de couleurs futuriste"""
        return {
            'neon_cyan': cls.NEON_CYAN,
            'neon_pink': cls.NEON_PINK,
            'neon_green': cls.NEON_GREEN,
            'neon_blue': cls.NEON_BLUE,
            'neon_purple': cls.NEON_PURPLE,
            'neon_orange': cls.NEON_ORANGE,
            'dark_void': cls.DARK_VOID,
            'deep_space': cls.DEEP_SPACE,
            'cosmic_blue': cls.COSMIC_BLUE,
            'hologram_white': cls.HOLOGRAM_WHITE,
            'cyber_blue': cls.CYBER_BLUE,
            'matrix_green': cls.MATRIX_GREEN,
            'plasma_pink': cls.PLASMA_PINK,
            'energy_yellow': cls.ENERGY_YELLOW
        }
