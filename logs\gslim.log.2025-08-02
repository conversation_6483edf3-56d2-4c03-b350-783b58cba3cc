2025-08-02 10:11:36 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 10:11:36 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 10:11:45 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 10:11:45 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 10:11:46 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 10:11:46 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 10:11:46 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 10:11:46 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 10:11:46 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:46 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:46 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:46 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:46 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 10:11:46 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 10:11:46 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 10:11:46 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 10:11:49 - src.app - INFO - Thème changé vers: light
2025-08-02 10:11:51 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 10:11:51 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 10:11:53 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 10:11:53 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:11:53 - views.main_window - INFO - Module articles affiché
2025-08-02 10:11:55 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 10:11:55 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:55 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 10:11:55 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 10:11:55 - views.main_window - INFO - Module mouvements affiché
2025-08-02 10:11:56 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 10:11:56 - views.main_window - INFO - Module mouvements affiché
2025-08-02 10:11:57 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 10:11:57 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 10:11:57 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:11:57 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 10:11:58 - views.main_window - INFO - Changement vers la page: reports
2025-08-02 10:11:58 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 10:11:58 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 10:11:58 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 10:11:58 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 10:11:59 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 10:11:59 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 10:11:59 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 10:12:02 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 10:12:02 - views.main_window - INFO - Module articles affiché
2025-08-02 10:12:08 - src.app - INFO - Fermeture de l'application
2025-08-02 10:21:00 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 10:21:00 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 10:21:00 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 10:21:07 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 10:21:07 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 10:21:09 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 10:21:09 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 10:21:09 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 10:21:09 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 10:21:09 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:21:09 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:21:09 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:21:09 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 10:21:09 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 10:21:09 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 10:21:09 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 10:21:10 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 10:21:14 - src.app - INFO - Thème changé vers: light
2025-08-02 10:21:17 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 10:21:17 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 10:21:27 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 10:21:27 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 10:21:28 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 10:22:37 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 10:22:37 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:37 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 10:22:37 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 10:22:37 - views.main_window - INFO - Module mouvements affiché
2025-08-02 10:22:42 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 10:22:42 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 10:22:42 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 10:22:42 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 15:47:59 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 15:47:59 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 15:47:59 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 15:48:03 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 15:48:03 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 15:48:04 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 15:48:04 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 15:48:04 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 15:48:04 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 15:48:04 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:48:04 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:48:04 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:48:05 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:48:05 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 15:48:05 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 15:48:05 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 15:48:05 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 15:48:08 - src.app - INFO - Thème changé vers: light
2025-08-02 15:48:11 - src.app - INFO - Thème changé vers: dark
2025-08-02 15:48:14 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 15:48:14 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 15:48:15 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 15:48:15 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 15:48:16 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 15:48:16 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 15:48:16 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 15:48:16 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:48:16 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 15:48:17 - views.main_window - INFO - Changement vers la page: reports
2025-08-02 15:48:17 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 15:48:17 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 15:48:17 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 15:48:17 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 15:48:20 - src.app - INFO - Thème changé vers: light
2025-08-02 15:48:20 - src.app - INFO - Thème changé vers: dark
2025-08-02 15:48:20 - src.app - INFO - Déconnexion de l'utilisateur: admin
2025-08-02 15:48:21 - src.app - INFO - Fermeture de l'application
2025-08-02 15:58:45 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 15:58:45 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 15:58:45 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 15:58:49 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 15:58:49 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 15:58:50 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 15:58:50 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 15:58:50 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 15:58:50 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 15:58:50 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:58:50 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:58:50 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 15:58:51 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 15:58:51 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 15:58:51 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 15:58:51 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 15:58:51 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 16:03:50 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:03:50 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:08:50 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:08:50 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:09:02 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 16:09:02 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 16:09:02 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 16:09:05 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 16:09:05 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 16:09:08 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 16:09:08 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 16:09:08 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 16:09:08 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 16:09:08 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:09:08 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:08 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:08 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:09:08 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:09:08 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 16:09:08 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 16:09:09 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 16:09:12 - src.app - INFO - Thème changé vers: light
2025-08-02 16:09:13 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 16:09:13 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 16:09:13 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 16:09:13 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 16:09:13 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 16:09:13 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 16:09:14 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 16:09:14 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 16:09:14 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 16:09:14 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 16:09:14 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 16:09:14 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 16:09:14 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 16:09:14 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 16:09:16 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 16:09:16 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 16:09:17 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 16:09:17 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:17 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:09:17 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 16:09:17 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 16:09:18 - views.main_window - INFO - Module mouvements affiché
2025-08-02 16:09:24 - src.app - INFO - Thème changé vers: dark
2025-08-02 16:09:25 - src.app - INFO - Fermeture de l'application
2025-08-02 16:23:09 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 16:23:09 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 16:23:09 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 16:23:12 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 16:23:12 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 16:23:15 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 16:23:15 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 16:23:15 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 16:23:15 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 16:23:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:23:15 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:15 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:15 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:23:15 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 16:23:15 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 16:23:15 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 16:23:16 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 16:23:18 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 16:23:18 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 16:23:18 - views.main_window - INFO - Module articles affiché
2025-08-02 16:23:20 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 16:23:20 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 16:23:22 - src.app - INFO - Thème changé vers: light
2025-08-02 16:23:24 - src.app - INFO - Thème changé vers: dark
2025-08-02 16:23:25 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 16:23:25 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 16:23:26 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 16:23:26 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 16:23:29 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 16:23:29 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 16:23:29 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 16:23:29 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 16:23:29 - views.main_window - INFO - Module mouvements affiché
2025-08-02 16:23:29 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 16:23:29 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 16:23:30 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 16:23:30 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 16:23:30 - views.main_window - INFO - Module articles affiché
2025-08-02 16:23:54 - src.app - INFO - Fermeture de l'application
2025-08-02 17:29:46 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 17:29:46 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 17:29:46 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 17:29:49 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 17:29:49 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 17:29:51 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 17:29:51 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 17:29:51 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 17:29:51 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 17:29:51 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:29:51 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:29:51 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:29:52 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:29:52 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 17:29:52 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 17:29:53 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 17:29:53 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 17:30:19 - views.main_window - INFO - Thème changé: modern (dark)
2025-08-02 17:30:28 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 17:30:28 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:30:28 - views.main_window - INFO - Module articles affiché
2025-08-02 17:30:29 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 17:30:29 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:29 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:29 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 17:30:29 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 17:30:29 - views.main_window - INFO - Module mouvements affiché
2025-08-02 17:30:35 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 17:30:35 - views.main_window - INFO - Module mouvements affiché
2025-08-02 17:30:35 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 17:30:35 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 17:30:35 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:30:35 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 17:30:36 - views.main_window - INFO - Changement vers la page: reports
2025-08-02 17:30:36 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 17:30:36 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 17:30:36 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 17:30:37 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 17:30:37 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 17:30:37 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 17:30:37 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 17:50:25 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 17:50:25 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 17:50:25 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 17:50:28 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 17:50:28 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 17:50:29 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 17:50:29 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 17:50:29 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 17:50:29 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 17:50:29 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:50:29 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:29 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:30 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:50:30 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 17:50:30 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 17:50:30 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 17:50:30 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 17:50:33 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 17:50:33 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 17:50:40 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-02 17:50:43 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 17:50:43 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 17:50:43 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 17:50:43 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 17:50:43 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 17:50:47 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 17:50:47 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 17:50:48 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 17:50:48 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 17:50:48 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 17:50:48 - views.main_window - INFO - Module mouvements affiché
2025-08-02 17:50:48 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 17:50:48 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 17:50:48 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:50:48 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 17:50:49 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 17:50:49 - views.main_window - INFO - Module mouvements affiché
2025-08-02 17:51:01 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-02 17:51:04 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 17:51:04 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 17:51:05 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 17:51:05 - ArticleController - ERROR - Erreur lors du calcul des statistiques: Cannot operate on a closed database.
2025-08-02 17:51:05 - views.main_window - INFO - Module articles affiché
2025-08-02 17:51:15 - views.main_window - INFO - Thème changé: classic (light)
2025-08-02 17:51:15 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-02 17:51:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:51:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:51:48 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 17:52:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:52:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:52:48 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 17:53:04 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 17:53:04 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 17:53:08 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 17:53:08 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 17:53:16 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 17:53:16 - views.main_window - INFO - Module mouvements affiché
2025-08-02 17:53:19 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 17:53:19 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 17:53:19 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:53:19 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_reset_filters'
2025-08-02 17:53:19 - views.main_window - INFO - Changement vers la page: reports
2025-08-02 17:53:19 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 17:53:19 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 17:53:19 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 17:53:20 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'PIE'
2025-08-02 17:53:48 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:53:48 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'DatabaseManager' object has no attribute 'cursor'
2025-08-02 17:53:48 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 17:54:13 - src.app - INFO - Fermeture de l'application
2025-08-02 18:03:48 - utils.error_handler - ERROR - Exception non capturée: Traceback (most recent call last):
  File "D:\projects\GSlim\demo_articles.py", line 210, in <module>
    sys.exit(main())
             ~~~~^^
  File "D:\projects\GSlim\demo_articles.py", line 203, in main
    window = ArticlesDemoWindow()
  File "D:\projects\GSlim\demo_articles.py", line 26, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "D:\projects\GSlim\demo_articles.py", line 42, in setup_ui
    self.articles_window = EnhancedArticlesWindow(self)
                           ~~~~~~~~~~~~~~~~~~~~~~^^^^^^
  File "D:\projects\GSlim\src\views\enhanced_articles_window.py", line 321, in __init__
    self.controller = ArticleController(app_instance.get_database_manager())
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "D:\projects\GSlim\demo_articles.py", line 173, in get_database_manager
    return self.db_manager
           ^^^^^^^^^^^^^^^
AttributeError: 'ArticlesDemoWindow' object has no attribute 'db_manager'

2025-08-02 18:05:19 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:10:07 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 18:10:07 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 18:10:07 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 18:10:10 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 18:10:10 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 18:10:11 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 18:10:11 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:10:11 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 18:10:11 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 18:10:11 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 18:10:11 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:10:11 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:10:11 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 18:10:11 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 18:10:12 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 18:10:19 - views.main_window - INFO - Thème changé: classic (dark)
2025-08-02 18:10:19 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-02 18:10:23 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 18:10:23 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:10:33 - views.main_window - INFO - Thème changé: professional (dark)
2025-08-02 18:10:33 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-02 18:10:35 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 18:10:35 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 18:10:35 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 18:10:36 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 18:10:36 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:10:37 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 18:10:37 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 18:10:38 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 18:10:38 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 18:10:58 - views.main_window - INFO - Thème changé: classic (dark)
2025-08-02 18:10:58 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-02 18:11:00 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 18:11:00 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:11:01 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 18:11:01 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 18:11:08 - views.main_window - INFO - Thème changé: modern (dark)
2025-08-02 18:11:18 - views.main_window - INFO - Thème changé via le sélecteur
2025-08-02 18:11:19 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 18:11:19 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 18:11:20 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 18:11:20 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 18:11:20 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'ArticleController' object has no attribute 'get_all'
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'sqlite3.Cursor' object is not callable
2025-08-02 18:11:20 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 18:11:20 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 18:11:20 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 18:11:20 - views.main_window - INFO - Module mouvements affiché
2025-08-02 18:11:23 - src.app - INFO - Fermeture de l'application
2025-08-02 18:23:42 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 18:23:42 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 18:23:42 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 18:23:45 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 18:23:45 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 18:23:46 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 18:23:46 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:23:46 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 18:23:46 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 18:23:46 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 18:23:46 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:46 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:46 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 18:23:46 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 18:23:46 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 18:23:49 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 18:23:49 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:50 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour TechSupply Pro: 5/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Bureau Plus: 5/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Matériel Express: 3/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Fournisseur Test Controller: 5/5
2025-08-02 18:23:50 - Supplier - INFO - Note mise à jour pour Fournisseur Intégration: 0/5
2025-08-02 18:23:51 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 18:23:53 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 18:23:53 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 18:23:54 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 18:23:54 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:23:57 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 18:23:57 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 18:23:59 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 18:23:59 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 18:24:19 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 18:24:19 - views.main_window - INFO - Module fournisseurs affiché
2025-08-02 18:24:38 - src.app - INFO - Fermeture de l'application
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:47:58 - controllers.supplier_controller - ERROR - Erreur lors du calcul des statistiques: 'NoneType' object is not subscriptable
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:47:58 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 18:49:30 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 18:49:30 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 18:49:37 - database.manager - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 18:49:37 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: name 'bcrypt' is not defined
2025-08-02 18:53:58 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 18:53:59 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:02:00 - src.app - ERROR - Erreur lors de l'initialisation de la base de données: 'DatabaseManager' object has no attribute 'initialize_database'
2025-08-02 19:06:10 - database.manager - INFO - Initialisation de la base de données...
2025-08-02 19:06:10 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:06:10 - database.manager - INFO - Tables créées avec succès
2025-08-02 19:06:10 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 19:06:10 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:06:11 - database.manager - INFO - Initialisation de la base de données...
2025-08-02 19:06:11 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:06:11 - database.manager - INFO - Tables créées avec succès
2025-08-02 19:06:11 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 19:06:11 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:07:52 - database.manager - INFO - Initialisation de la base de données...
2025-08-02 19:07:52 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:07:52 - database.manager - INFO - Tables créées avec succès
2025-08-02 19:07:52 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 19:07:52 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 19:07:52 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 19:07:54 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:08 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:15 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:30 - views.login_window - ERROR - Erreur lors de l'authentification: 'DatabaseManager' object has no attribute 'authenticate_user'
2025-08-02 19:08:41 - src.app - INFO - Fermeture de l'application
2025-08-02 19:08:41 - src.app - ERROR - Erreur lors de la fermeture: 'DatabaseManager' object has no attribute 'close'
2025-08-02 19:11:12 - database.manager - WARNING - Tentative d'authentification échouée pour: invalid
2025-08-02 19:11:12 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:11:12 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:11:12 - database.manager - WARNING - Tentative d'authentification échouée pour: wrong
2025-08-02 19:11:12 - database.manager - WARNING - Tentative d'authentification échouée pour: admin
2025-08-02 19:11:12 - database.manager - WARNING - Tentative d'authentification échouée pour: 
2025-08-02 19:16:41 - database.manager - INFO - Initialisation de la base de données...
2025-08-02 19:16:41 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:16:41 - database.manager - INFO - Tables créées avec succès
2025-08-02 19:16:41 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 19:16:41 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 19:16:41 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 19:16:44 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 19:16:44 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 19:16:45 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 19:16:45 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:16:45 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:16:45 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 19:16:45 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:16:45 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:16:45 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 19:16:45 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 19:16:45 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 19:16:55 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 19:16:55 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:16:57 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 19:16:57 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: 'SupplierController' object has no attribute 'count'
2025-08-02 19:16:58 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 19:16:58 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:16:58 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'ArticleController' object has no attribute 'get_all'
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'sqlite3.Cursor' object is not callable
2025-08-02 19:16:58 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:16:58 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 19:16:58 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 19:16:58 - views.main_window - INFO - Module mouvements affiché
2025-08-02 19:17:01 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 19:17:01 - views.main_window - INFO - Module mouvements affiché
2025-08-02 19:17:01 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 19:17:01 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:17:01 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:17:01 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:17:02 - views.main_window - INFO - Changement vers la page: reports
2025-08-02 19:17:02 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 19:17:02 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:17:02 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:17:02 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-02 19:17:03 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 19:17:03 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:17:04 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 19:17:04 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 19:17:04 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 19:17:04 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'ArticleController' object has no attribute 'get_stock_statistics'
2025-08-02 19:17:05 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 19:17:05 - views.main_window - INFO - Module mouvements affiché
2025-08-02 19:17:06 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 19:17:06 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:17:06 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:17:06 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:17:07 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 19:17:07 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: 'SupplierController' object has no attribute 'count'
2025-08-02 19:17:14 - src.app - INFO - Fermeture de l'application
2025-08-02 19:17:14 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:20:32 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:20:32 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:26:16 - database.manager - INFO - Initialisation de la base de données...
2025-08-02 19:26:16 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:26:16 - database.manager - INFO - Tables créées avec succès
2025-08-02 19:26:16 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 19:26:16 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 19:26:16 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 19:26:18 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 19:26:18 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 19:26:19 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 19:26:19 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:26:19 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:26:19 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 19:26:19 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-02 19:26:19 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 19:26:19 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 19:26:19 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 19:26:19 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 19:26:21 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 19:26:21 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 19:26:24 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 19:26:24 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-02 19:26:25 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 19:26:25 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:25 - controllers.article_controller - ERROR - Erreur lors de la récupération des articles: no such column: c.nom
2025-08-02 19:26:25 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'SupplierController' object has no attribute 'get_all'
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors de la récupération des mouvements récents: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:25 - controllers.stock_movement_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:25 - views.movements_window - INFO - 0 mouvements chargés
2025-08-02 19:26:25 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 19:26:25 - views.main_window - INFO - Module mouvements affiché
2025-08-02 19:26:26 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 19:26:26 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:26:26 - controllers.order_controller - ERROR - Erreur lors du calcul des statistiques: 'sqlite3.Cursor' object is not callable
2025-08-02 19:26:26 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:26:26 - views.main_window - INFO - Changement vers la page: reports
2025-08-02 19:26:26 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 19:26:26 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:26:26 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:26:27 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-02 19:26:30 - src.app - INFO - Fermeture de l'application
2025-08-02 19:26:30 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:40:44 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:40:44 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:40:44 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 19:41:32 - database.manager - INFO - Initialisation de la base de données...
2025-08-02 19:41:32 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 19:41:32 - database.manager - INFO - Tables créées avec succès
2025-08-02 19:41:32 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 19:41:32 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 19:41:32 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 19:41:34 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 19:41:34 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 19:41:35 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 19:41:35 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:41:35 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:41:35 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 19:41:35 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-02 19:41:35 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 19:41:35 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 19:41:35 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 19:41:35 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 19:41:38 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 19:41:38 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 19:41:39 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 19:41:39 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 19:41:41 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 19:41:41 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-02 19:41:42 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 19:41:42 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors de la création des cartes de stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 19:41:42 - controllers.article_controller - ERROR - Erreur lors de la récupération des articles: no such column: c.nom
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'SupplierController' object has no attribute 'get_all'
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors du chargement des statistiques: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 19:41:42 - views.movements_window - ERROR - Erreur lors de la mise à jour des stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 19:41:42 - views.movements_window - INFO - 5 mouvements chargés
2025-08-02 19:41:42 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 19:41:42 - views.main_window - INFO - Module mouvements affiché
2025-08-02 19:41:43 - views.main_window - INFO - Changement vers la page: orders
2025-08-02 19:41:43 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:41:43 - views.orders_window - ERROR - Erreur lors de la création des cartes de stats: 'OrderController' object has no attribute 'get_order_statistics'
2025-08-02 19:41:43 - views.main_window - ERROR - Erreur lors de l'affichage du module commandes: 'OrdersWindow' object has no attribute '_on_order_selected'
2025-08-02 19:41:44 - views.main_window - INFO - Changement vers la page: reports
2025-08-02 19:41:44 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 19:41:44 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 19:41:44 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 19:41:44 - views.main_window - ERROR - Erreur lors de l'affichage du module rapports: type object 'FluentIcon' has no attribute 'CHART'
2025-08-02 19:41:46 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 19:41:46 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 19:42:07 - src.app - INFO - Fermeture de l'application
2025-08-02 19:42:07 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 20:25:41 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 20:25:41 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 20:25:41 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 20:25:41 - database.manager - INFO - Connexion à la base de données fermée
2025-08-02 20:28:26 - database.manager - INFO - Initialisation de la base de données...
2025-08-02 20:28:26 - database.manager - INFO - Connexion à la base de données établie
2025-08-02 20:28:26 - database.manager - INFO - Tables créées avec succès
2025-08-02 20:28:26 - database.manager - INFO - Base de données initialisée avec succès
2025-08-02 20:28:26 - src.app - INFO - Base de données initialisée avec succès
2025-08-02 20:28:26 - src.app - INFO - Démarrage de GSlim v1.0.0
2025-08-02 20:28:28 - views.login_window - INFO - Connexion réussie pour: admin
2025-08-02 20:28:28 - src.app - INFO - Connexion réussie pour l'utilisateur: admin
2025-08-02 20:28:29 - views.main_window - INFO - Changement vers la page: dashboard
2025-08-02 20:28:29 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 20:28:29 - controllers.order_controller - INFO - OrderController initialisé
2025-08-02 20:28:29 - controllers.report_controller - INFO - ReportController initialisé
2025-08-02 20:28:29 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'SupplierController' object has no attribute 'get_supplier_statistics'
2025-08-02 20:28:29 - views.dashboard_window - ERROR - Erreur lors du chargement des données: 'DashboardWindow' object has no attribute 'total_articles_card'
2025-08-02 20:28:29 - views.dashboard_window - INFO - DashboardWindow initialisé
2025-08-02 20:28:29 - views.main_window - INFO - Module tableau de bord affiché
2025-08-02 20:28:29 - src.app - INFO - Fenêtre principale ouverte
2025-08-02 20:28:31 - views.main_window - INFO - Changement vers la page: articles
2025-08-02 20:28:31 - views.main_window - ERROR - Erreur lors de l'affichage du module articles: 'low_stock'
2025-08-02 20:28:32 - views.main_window - INFO - Changement vers la page: suppliers
2025-08-02 20:28:32 - views.main_window - ERROR - Erreur lors de l'affichage du module fournisseurs: SupplierController.count() takes 1 positional argument but 2 were given
2025-08-02 20:28:33 - views.main_window - INFO - Changement vers la page: movements
2025-08-02 20:28:33 - controllers.stock_movement_controller - INFO - StockMovementController initialisé
2025-08-02 20:28:33 - views.movements_window - ERROR - Erreur lors de la création des cartes de stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 20:28:34 - views.movements_window - ERROR - Erreur lors du chargement des données du formulaire: 'name'
2025-08-02 20:28:34 - views.movements_window - ERROR - Erreur lors du chargement des statistiques: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 20:28:34 - views.movements_window - ERROR - Erreur lors de la mise à jour des stats: 'StockMovementController' object has no attribute 'get_movement_statistics'
2025-08-02 20:28:34 - views.movements_window - INFO - 5 mouvements chargés
2025-08-02 20:28:34 - views.movements_window - INFO - MovementsWindow initialisé
2025-08-02 20:28:34 - views.main_window - INFO - Module mouvements affiché
2025-08-02 20:28:37 - src.app - INFO - Fermeture de l'application
2025-08-02 20:28:37 - database.manager - INFO - Connexion à la base de données fermée
