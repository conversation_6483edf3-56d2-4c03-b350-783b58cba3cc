@echo off
echo.
echo ========================================
echo   🔧 ACTIVATION ENVIRONNEMENT VIRTUEL
echo ========================================
echo.

REM Vérifier si le dossier venv existe
if not exist "venv" (
    echo ❌ Le dossier venv n'existe pas
    echo 💡 Créez d'abord un environnement virtuel avec:
    echo    python -m venv venv
    pause
    exit /b 1
)

REM Vérifier si le script d'activation existe
if not exist "venv\Scripts\activate.bat" (
    echo ❌ Script d'activation non trouvé
    echo 💡 L'environnement virtuel semble corrompu
    pause
    exit /b 1
)

echo 🔄 Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

echo.
echo ✅ Environnement virtuel activé !
echo 📋 Vous êtes maintenant dans l'environnement virtuel
echo.
echo 🚀 Commandes disponibles:
echo    python main.py          - Lancer GSlim
echo    pip list                - Voir les packages installés
echo    deactivate              - Désactiver l'environnement
echo.

REM Ouvrir un nouveau shell avec l'environnement activé
cmd /k
