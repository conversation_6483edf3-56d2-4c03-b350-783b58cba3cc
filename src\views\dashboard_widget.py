"""
Widget Dashboard conforme au cahier des charges
Affiche les KPIs et graphiques selon les spécifications
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
    QLabel, QFrame, QScrollArea
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette

try:
    from qfluentwidgets import (
        CardWidget, TitleLabel, CaptionLabel, BodyLabel,
        FluentIcon, IconWidget, PushButton
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from config.settings import config
from utils.logger import setup_logger
from ui.modern_components import StatsCard, ModernProgressBar, NotificationToast
from ui.theme_manager import theme_manager


class DashboardWidget(QWidget):
    """
    Widget Dashboard avec KPIs et graphiques
    Conforme aux spécifications du cahier des charges
    """
    
    # Signaux
    error_occurred = pyqtSignal(str)
    success_message = pyqtSignal(str)
    info_message = pyqtSignal(str)

    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.current_user = None
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # Actualisation toutes les 30 secondes
        
        # Initialiser l'interface
        self._init_ui()
        
        # Charger les données initiales
        self.refresh_data()
        
        # Conteneur pour les notifications
        self.notifications_container = QWidget(self)
        self.notifications_layout = QVBoxLayout(self.notifications_container)
        self.notifications_layout.setContentsMargins(0, 0, 0, 0)
        self.notifications_container.setFixedWidth(350)
        self.notifications_container.move(self.width() - 370, 20)

        self.logger.info("Dashboard initialisé")

    def _init_ui(self):
        """Initialiser l'interface utilisateur"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Titre du dashboard
        self._create_header(main_layout)
        
        # Zone de contenu avec scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        
        # Section KPIs
        self._create_kpi_section(content_layout)
        
        # Section graphiques
        self._create_charts_section(content_layout)
        
        # Section actions rapides
        self._create_quick_actions_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)

    def _create_header(self, layout):
        """Créer l'en-tête du dashboard"""
        header_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            title = TitleLabel("Tableau de Bord")
            subtitle = CaptionLabel("Vue d'ensemble de votre activité")
        else:
            title = QLabel("Tableau de Bord")
            title.setFont(QFont("Segoe UI", 24, QFont.Bold))
            subtitle = QLabel("Vue d'ensemble de votre activité")
            subtitle.setFont(QFont("Segoe UI", 12))
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # Bouton d'actualisation
        if FLUENT_AVAILABLE:
            refresh_btn = PushButton("Actualiser")
            refresh_btn.setIcon(FluentIcon.SYNC)
        else:
            refresh_btn = PushButton("Actualiser")
        
        refresh_btn.clicked.connect(self.refresh_data)
        header_layout.addWidget(refresh_btn)
        
        layout.addLayout(header_layout)
        layout.addWidget(subtitle)

    def _create_kpi_section(self, layout):
        """Créer la section des KPIs selon les spécifications"""
        if FLUENT_AVAILABLE:
            section_title = TitleLabel("Indicateurs Clés")
        else:
            section_title = QLabel("Indicateurs Clés")
            section_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        
        layout.addWidget(section_title)
        
        # Grid pour les cartes KPI
        kpi_grid = QGridLayout()
        kpi_grid.setSpacing(15)
        
        # KPIs selon les spécifications du cahier des charges
        self.kpi_cards = {}

        # Ligne 1
        self.kpi_cards['total_products'] = self._create_kpi_card(
            "Produits Référencés", "0", FluentIcon.SHOPPING_CART if FLUENT_AVAILABLE else None, "#2196F3"
        )
        self.kpi_cards['stock_value'] = self._create_kpi_card(
            "Valeur du Stock", "0 €", FluentIcon.SHOPPING_CART if FLUENT_AVAILABLE else None, "#4CAF50"
        )
        self.kpi_cards['low_stock'] = self._create_kpi_card(
            "Stock Bas", "0", FluentIcon.INFO if FLUENT_AVAILABLE else None, "#FF9800"
        )
        self.kpi_cards['out_of_stock'] = self._create_kpi_card(
            "Ruptures", "0", FluentIcon.CANCEL if FLUENT_AVAILABLE else None, "#F44336"
        )
        
        # Ligne 2
        self.kpi_cards['daily_sales'] = self._create_kpi_card(
            "Ventes du Jour", "0 €", FluentIcon.SHOPPING_CART if FLUENT_AVAILABLE else None, "#9C27B0"
        )
        self.kpi_cards['monthly_sales'] = self._create_kpi_card(
            "Ventes du Mois", "0 €", FluentIcon.CALENDAR if FLUENT_AVAILABLE else None, "#673AB7"
        )
        self.kpi_cards['total_clients'] = self._create_kpi_card(
            "Clients", "0", FluentIcon.PEOPLE if FLUENT_AVAILABLE else None, "#3F51B5"
        )
        self.kpi_cards['total_suppliers'] = self._create_kpi_card(
            "Fournisseurs", "0", FluentIcon.PEOPLE if FLUENT_AVAILABLE else None, "#607D8B"
        )
        
        # Ajouter les cartes au grid
        kpi_grid.addWidget(self.kpi_cards['total_products'], 0, 0)
        kpi_grid.addWidget(self.kpi_cards['stock_value'], 0, 1)
        kpi_grid.addWidget(self.kpi_cards['low_stock'], 0, 2)
        kpi_grid.addWidget(self.kpi_cards['out_of_stock'], 0, 3)
        
        kpi_grid.addWidget(self.kpi_cards['daily_sales'], 1, 0)
        kpi_grid.addWidget(self.kpi_cards['monthly_sales'], 1, 1)
        kpi_grid.addWidget(self.kpi_cards['total_clients'], 1, 2)
        kpi_grid.addWidget(self.kpi_cards['total_suppliers'], 1, 3)
        
        layout.addLayout(kpi_grid)

    def _create_kpi_card(self, title, value, icon, color):
        """Créer une carte KPI moderne avec animations"""
        # Utiliser notre composant StatsCard moderne
        card = StatsCard(title, value, "")

        # Personnaliser les couleurs selon le thème
        current_colors = theme_manager.get_current_theme_colors()

        # Appliquer le style moderne avec animations
        card.setStyleSheet(f"""
            StatsCard {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {current_colors.get('card', '#ffffff')},
                    stop:1 {current_colors.get('surface', '#f9f9f9')});
                border: 2px solid {current_colors.get('border', '#e1dfdd')};
                border-radius: 16px;
                padding: 20px;
            }}
            StatsCard:hover {{
                border-color: {color};
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {current_colors.get('card', '#ffffff')},
                    stop:1 white);
                box-shadow: 0 8px 32px rgba(0, 120, 212, 0.2);
            }}
        """)

        # Ajouter une barre de progression pour certains KPIs
        if "Stock" in title or "Ventes" in title:
            progress_bar = ModernProgressBar()
            progress_bar.setMaximum(100)

            # Calculer un pourcentage basé sur la valeur
            try:
                # Extraire la valeur numérique
                numeric_str = value.replace('€', '').replace(' ', '').replace(',', '.')
                numeric_value = float(numeric_str)

                if "Stock" in title:
                    # Pour le stock, utiliser une échelle de 0-50000€
                    progress_value = min(int((numeric_value / 50000) * 100), 100)
                else:
                    # Pour les ventes, utiliser une échelle de 0-10000€
                    progress_value = min(int((numeric_value / 10000) * 100), 100)

                progress_bar.animate_to_value(progress_value)
            except:
                progress_bar.animate_to_value(75)  # Valeur par défaut

            # Ajouter la barre de progression à la carte
            card.layout().addWidget(progress_bar)

        # Stocker la référence pour mise à jour
        card.value_label = card.value_label  # Référence déjà dans StatsCard

        return card

    def _create_charts_section(self, layout):
        """Créer la section des graphiques"""
        if FLUENT_AVAILABLE:
            section_title = TitleLabel("Graphiques")
        else:
            section_title = QLabel("Graphiques")
            section_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        
        layout.addWidget(section_title)
        
        # Placeholder pour les graphiques (à implémenter avec matplotlib)
        charts_layout = QHBoxLayout()
        
        # Graphique 1: Évolution des ventes
        chart1_card = self._create_chart_placeholder("Évolution des Ventes (7 jours)")
        charts_layout.addWidget(chart1_card)
        
        # Graphique 2: Top 5 produits
        chart2_card = self._create_chart_placeholder("Top 5 Produits")
        charts_layout.addWidget(chart2_card)
        
        layout.addLayout(charts_layout)

    def _create_chart_placeholder(self, title):
        """Créer un placeholder pour graphique"""
        if FLUENT_AVAILABLE:
            card = CardWidget()
        else:
            card = QFrame()
            card.setFrameStyle(QFrame.Box)
        
        card.setFixedHeight(300)
        
        card_layout = QVBoxLayout(card)
        card_layout.setContentsMargins(15, 15, 15, 15)
        
        if FLUENT_AVAILABLE:
            title_label = BodyLabel(title)
        else:
            title_label = QLabel(title)
            title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        placeholder_label = QLabel("Graphique à implémenter\n(matplotlib/pyqtgraph)")
        placeholder_label.setAlignment(Qt.AlignCenter)
        placeholder_label.setStyleSheet("color: gray; font-style: italic;")
        
        card_layout.addWidget(title_label)
        card_layout.addWidget(placeholder_label)
        
        return card

    def _create_quick_actions_section(self, layout):
        """Créer la section des actions rapides"""
        if FLUENT_AVAILABLE:
            section_title = TitleLabel("Actions Rapides")
        else:
            section_title = QLabel("Actions Rapides")
            section_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        
        layout.addWidget(section_title)
        
        actions_layout = QHBoxLayout()
        
        # Boutons d'actions rapides
        if FLUENT_AVAILABLE:
            new_sale_btn = PushButton("Nouvelle Vente")
            new_sale_btn.setIcon(FluentIcon.ADD)
            
            new_product_btn = PushButton("Nouveau Produit")
            new_product_btn.setIcon(FluentIcon.SHOPPING_CART)
            
            new_client_btn = PushButton("Nouveau Client")
            new_client_btn.setIcon(FluentIcon.PEOPLE)
        else:
            new_sale_btn = PushButton("Nouvelle Vente")
            new_product_btn = PushButton("Nouveau Produit")
            new_client_btn = PushButton("Nouveau Client")
        
        # Connecter les signaux (à implémenter)
        # new_sale_btn.clicked.connect(self._new_sale)
        # new_product_btn.clicked.connect(self._new_product)
        # new_client_btn.clicked.connect(self._new_client)
        
        actions_layout.addWidget(new_sale_btn)
        actions_layout.addWidget(new_product_btn)
        actions_layout.addWidget(new_client_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)

    def refresh_data(self):
        """Actualiser les données du dashboard"""
        try:
            db_manager = self.app_instance.get_database_manager()
            
            # Récupérer les statistiques depuis la base de données
            stats = self._get_dashboard_stats(db_manager)
            
            # Mettre à jour les KPIs
            self._update_kpis(stats)
            
            self.logger.info("Données du dashboard actualisées")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'actualisation: {e}")
            self.error_occurred.emit(f"Erreur d'actualisation: {e}")

    def show_notification(self, message: str, notification_type: str = "info"):
        """Afficher une notification moderne"""
        try:
            # Créer la notification
            notification = NotificationToast(message, notification_type, self)

            # Positionner la notification
            notification.setFixedWidth(340)
            notification.move(self.width() - 360, 20 + len(self.notifications_layout) * 70)

            # Ajouter au conteneur
            self.notifications_layout.addWidget(notification)

            # Afficher avec animation
            notification.show_animated()

            # Nettoyer après disparition
            QTimer.singleShot(5000, lambda: self._cleanup_notification(notification))

        except Exception as e:
            self.logger.error(f"Erreur lors de l'affichage de la notification: {e}")

    def _cleanup_notification(self, notification):
        """Nettoyer une notification expirée"""
        try:
            if notification:
                self.notifications_layout.removeWidget(notification)
                notification.deleteLater()
        except Exception as e:
            self.logger.error(f"Erreur lors du nettoyage de la notification: {e}")

    def resizeEvent(self, event):
        """Repositionner les notifications lors du redimensionnement"""
        super().resizeEvent(event)
        if hasattr(self, 'notifications_container'):
            self.notifications_container.move(self.width() - 370, 20)

    def _get_dashboard_stats(self, db_manager):
        """Récupérer les statistiques pour le dashboard"""
        try:
            return db_manager.get_dashboard_stats()
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des statistiques: {e}")
            return {
                'total_products': 0,
                'stock_value': 0.0,
                'low_stock': 0,
                'out_of_stock': 0,
                'daily_sales': 0.0,
                'monthly_sales': 0.0,
                'total_clients': 0,
                'total_suppliers': 0
            }

    def _update_kpis(self, stats):
        """Mettre à jour les valeurs des KPIs"""
        try:
            self.kpi_cards['total_products'].value_label.setText(str(stats['total_products']))
            self.kpi_cards['stock_value'].value_label.setText(f"{stats['stock_value']:.2f} €")
            self.kpi_cards['low_stock'].value_label.setText(str(stats['low_stock']))
            self.kpi_cards['out_of_stock'].value_label.setText(str(stats['out_of_stock']))
            self.kpi_cards['daily_sales'].value_label.setText(f"{stats['daily_sales']:.2f} €")
            self.kpi_cards['monthly_sales'].value_label.setText(f"{stats['monthly_sales']:.2f} €")
            self.kpi_cards['total_clients'].value_label.setText(str(stats['total_clients']))
            self.kpi_cards['total_suppliers'].value_label.setText(str(stats['total_suppliers']))
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour des KPIs: {e}")

    def set_current_user(self, user_data):
        """Définir l'utilisateur actuel"""
        self.current_user = user_data
        self.refresh_data()
