# 🔧 GUIDE D'ACTIVATION DE L'ENVIRONNEMENT VIRTUEL

## 🎯 PROBLÈME IDENTIFIÉ

Vous rencontrez l'erreur suivante :
```
Impossible de charger le fichier venv\Scripts\Activate.ps1, car l'exécution de scripts est désactivée sur ce système.
```

Cette erreur est due aux **politiques de sécurité PowerShell** de Windows.

---

## ✅ SOLUTIONS DISPONIBLES

### 🥇 **SOLUTION 1 : FICHIER BAT (RECOMMANDÉE)**

**La plus simple et la plus fiable :**

1. **Double-cliquez** sur le fichier : `activer_venv.bat`
2. Ou dans le terminal Command Prompt (cmd) :
   ```cmd
   activer_venv.bat
   ```

### 🥈 **SOLUTION 2 : COMMANDE DIRECTE**

Dans **Command Prompt (cmd)** :
```cmd
venv\Scripts\activate.bat
```

Dans **PowerShell** (après résolution du problème) :
```powershell
venv\Scripts\Activate.ps1
```

### 🥉 **SOLUTION 3 : RÉSOUDRE LE PROBLÈME POWERSHELL**

#### Étape 1 : Ouvrir PowerShell en administrateur
1. Clic droit sur le menu Démarrer
2. Sélectionner "Windows PowerShell (Admin)"

#### Étape 2 : Modifier la politique d'exécution
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Étape 3 : Confirmer avec 'Y'

#### Étape 4 : Maintenant vous pouvez utiliser
```powershell
venv\Scripts\Activate.ps1
```

---

## 🚀 ACTIVATION RAPIDE

### Méthode 1 : Fichier automatique
```cmd
# Double-cliquez sur :
activer_venv.bat
```

### Méthode 2 : Terminal
```cmd
# Dans Command Prompt :
cd D:\projects\GSlim
venv\Scripts\activate.bat
python main.py
```

### Méthode 3 : PowerShell (après correction)
```powershell
# Dans PowerShell :
cd D:\projects\GSlim
venv\Scripts\Activate.ps1
python main.py
```

---

## 🔍 VÉRIFICATION DE L'ACTIVATION

Une fois l'environnement activé, vous devriez voir :

1. **Prompt modifié** : `(venv) D:\projects\GSlim>`
2. **Version Python** : `python --version`
3. **Packages installés** : `pip list`

---

## 🛠️ COMMANDES UTILES DANS L'ENVIRONNEMENT

```cmd
# Vérifier l'activation
python --version
pip --version

# Voir les packages installés
pip list

# Lancer GSlim
python main.py

# Désactiver l'environnement
deactivate
```

---

## 🆘 DÉPANNAGE

### Problème : "venv n'existe pas"
```cmd
# Créer un nouvel environnement virtuel
python -m venv venv
```

### Problème : "Script d'activation manquant"
```cmd
# Recréer l'environnement
rmdir /s venv
python -m venv venv
pip install -r requirements.txt
```

### Problème : "Permission refusée"
1. Exécuter en tant qu'administrateur
2. Ou utiliser la solution PowerShell ci-dessus

---

## 📋 FICHIERS CRÉÉS POUR VOUS

- `activer_venv.bat` - Activation simple via fichier .bat
- `activer_venv_powershell.ps1` - Script PowerShell avec gestion d'erreurs
- `resoudre_probleme_powershell.bat` - Guide interactif de résolution

---

## 🎉 RÉSUMÉ

**Pour activer rapidement votre environnement virtuel :**

1. **SIMPLE** : Double-cliquez sur `activer_venv.bat`
2. **TERMINAL** : `venv\Scripts\activate.bat`
3. **POWERSHELL** : Résolvez d'abord le problème de politique

**Une fois activé, lancez GSlim :**
```cmd
python main.py
```

**Connexion :**
- Utilisateur : `admin`
- Mot de passe : `admin123`

---

🎊 **Votre environnement virtuel sera parfaitement fonctionnel !**
