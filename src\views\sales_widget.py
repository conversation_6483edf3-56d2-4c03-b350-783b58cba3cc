"""
Widget de gestion des ventes conforme au cahier des charges
Système complet de ventes selon les spécifications
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QHeaderView, QAbstractItemView,
    QMessageBox, QFrame, QComboBox
)
from PyQt5.QtCore import pyqtSignal, QTimer
from PyQt5.QtGui import QFont

try:
    from qfluentwidgets import (
        TitleLabel, PushButton, LineEdit, TableWidget, FluentIcon,
        CardWidget, ComboBox
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from utils.logger import setup_logger


class SalesWidget(QWidget):
    """
    Widget de gestion des ventes selon les spécifications du cahier des charges
    Fonctionnalités: CRUD complet, calculs automatiques, gestion du stock
    """

    error_occurred = pyqtSignal(str)
    success_message = pyqtSignal(str)
    info_message = pyqtSignal(str)

    def __init__(self, app_instance):
        super().__init__()
        self.app_instance = app_instance
        self.logger = setup_logger(__name__)
        self.current_user = None

        # Variables d'instance
        self.sales_data = []
        self.filtered_sales = []

        # Initialiser l'interface
        self._init_ui()

        # Charger les données initiales
        self.refresh_data()

        self.logger.info("Widget ventes initialisé")

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # En-tête
        self._create_header(main_layout)

        # Statistiques rapides
        self._create_stats_section(main_layout)

        # Barre de recherche et filtres
        self._create_search_bar(main_layout)

        # Tableau des ventes
        self._create_sales_table(main_layout)

        # Barre d'actions
        self._create_action_bar(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du widget"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Gestion des Ventes")
        else:
            title = QLabel("Gestion des Ventes")
            title.setFont(QFont("Segoe UI", 20, QFont.Bold))

        subtitle = QLabel("Gérez vos ventes avec calculs automatiques et gestion du stock")
        subtitle.setFont(QFont("Segoe UI", 10))
        subtitle.setStyleSheet("color: gray;")

        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_stats_section(self, layout):
        """Créer la section des statistiques rapides"""
        if FLUENT_AVAILABLE:
            stats_card = CardWidget()
        else:
            stats_card = QFrame()
            stats_card.setFrameStyle(QFrame.Box)

        stats_layout = QHBoxLayout(stats_card)
        stats_layout.setContentsMargins(20, 15, 20, 15)

        # Ventes du jour
        self.daily_sales_label = QLabel("Ventes du jour: 0.00 €")
        self.daily_sales_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.daily_sales_label.setStyleSheet("color: #0078d4;")

        # Ventes du mois
        self.monthly_sales_label = QLabel("Ventes du mois: 0.00 €")
        self.monthly_sales_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.monthly_sales_label.setStyleSheet("color: #107c10;")

        # Ventes en attente
        self.pending_sales_label = QLabel("En attente: 0")
        self.pending_sales_label.setFont(QFont("Segoe UI", 11, QFont.Bold))
        self.pending_sales_label.setStyleSheet("color: #ff8c00;")

        stats_layout.addWidget(self.daily_sales_label)
        stats_layout.addWidget(self.monthly_sales_label)
        stats_layout.addWidget(self.pending_sales_label)
        stats_layout.addStretch()

        layout.addWidget(stats_card)

    def _create_search_bar(self, layout):
        """Créer la barre de recherche et filtres"""
        search_layout = QHBoxLayout()

        # Champ de recherche
        if FLUENT_AVAILABLE:
            self.search_input = LineEdit()
        else:
            self.search_input = QLineEdit()

        self.search_input.setPlaceholderText("Rechercher par client ou notes...")
        self.search_input.textChanged.connect(self._on_search_changed)

        # Filtre de statut
        if FLUENT_AVAILABLE:
            self.status_filter = ComboBox()
        else:
            self.status_filter = QComboBox()

        self.status_filter.addItem("Tous les statuts", "")
        self.status_filter.addItem("Brouillons", "draft")
        self.status_filter.addItem("Confirmées", "confirmed")
        self.status_filter.addItem("Annulées", "cancelled")
        self.status_filter.currentTextChanged.connect(self._on_filter_changed)

        # Bouton d'actualisation
        if FLUENT_AVAILABLE:
            refresh_btn = PushButton("Actualiser")
            refresh_btn.setIcon(FluentIcon.SYNC)
        else:
            refresh_btn = QPushButton("Actualiser")

        refresh_btn.clicked.connect(self.refresh_data)

        search_layout.addWidget(QLabel("Recherche:"))
        search_layout.addWidget(self.search_input, 1)
        search_layout.addWidget(QLabel("Statut:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(refresh_btn)

        layout.addLayout(search_layout)

    def _create_sales_table(self, layout):
        """Créer le tableau des ventes"""
        if FLUENT_AVAILABLE:
            self.sales_table = TableWidget()
        else:
            self.sales_table = QTableWidget()

        # Configuration du tableau selon spécifications
        columns = ["ID", "Date", "Client", "Montant HT", "TVA", "Total TTC", "Statut", "Notes"]

        self.sales_table.setColumnCount(len(columns))
        self.sales_table.setHorizontalHeaderLabels(columns)

        # Configuration de l'affichage
        self.sales_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.sales_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.sales_table.setAlternatingRowColors(True)

        # Ajustement des colonnes
        header = self.sales_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # Client

        # Double-clic pour modifier
        self.sales_table.doubleClicked.connect(self._on_sale_double_click)

        layout.addWidget(self.sales_table, 1)

    def _create_action_bar(self, layout):
        """Créer la barre d'actions"""
        actions_layout = QHBoxLayout()

        # Boutons d'action selon spécifications
        if FLUENT_AVAILABLE:
            self.add_btn = PushButton("Nouvelle Vente")
            self.add_btn.setIcon(FluentIcon.ADD)

            self.edit_btn = PushButton("Modifier")
            self.edit_btn.setIcon(FluentIcon.EDIT)

            self.status_btn = PushButton("Changer Statut")
            self.status_btn.setIcon(FluentIcon.SETTING)

            self.delete_btn = PushButton("Supprimer")
            self.delete_btn.setIcon(FluentIcon.DELETE)
        else:
            self.add_btn = QPushButton("Nouvelle Vente")
            self.edit_btn = QPushButton("Modifier")
            self.status_btn = QPushButton("Changer Statut")
            self.delete_btn = QPushButton("Supprimer")

        # État initial des boutons
        self.edit_btn.setEnabled(False)
        self.status_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)

        # Connecter les signaux
        self.add_btn.clicked.connect(self._on_add_sale)
        self.edit_btn.clicked.connect(self._on_edit_sale)
        self.status_btn.clicked.connect(self._on_change_status)
        self.delete_btn.clicked.connect(self._on_delete_sale)

        # Gestion de la sélection
        self.sales_table.itemSelectionChanged.connect(self._on_selection_changed)

        # Assemblage
        actions_layout.addWidget(self.add_btn)
        actions_layout.addWidget(self.edit_btn)
        actions_layout.addWidget(self.status_btn)
        actions_layout.addWidget(self.delete_btn)
        actions_layout.addStretch()

        layout.addLayout(actions_layout)

    def set_current_user(self, user_data):
        """Définir l'utilisateur actuel"""
        self.current_user = user_data

    def _on_search_changed(self):
        """Gérer le changement de recherche"""
        self._apply_filters()

    def _on_filter_changed(self):
        """Gérer le changement de filtre"""
        self._apply_filters()

    def _apply_filters(self):
        """Appliquer les filtres de recherche et statut"""
        search_term = self.search_input.text().lower()
        status_filter = self.status_filter.currentData()

        self.filtered_sales = []

        for sale in self.sales_data:
            # Filtre de recherche
            if search_term:
                client_name = sale.get('client_name', '').lower()
                notes = sale.get('notes', '').lower()
                if search_term not in client_name and search_term not in notes:
                    continue

            # Filtre de statut
            if status_filter and sale.get('status') != status_filter:
                continue

            self.filtered_sales.append(sale)

        self._update_table_display()

    def _update_table_display(self):
        """Mettre à jour l'affichage du tableau"""
        self.sales_table.setRowCount(len(self.filtered_sales))

        for row, sale in enumerate(self.filtered_sales):
            # Colonnes selon spécifications
            self.sales_table.setItem(row, 0, QTableWidgetItem(str(sale.get('id', ''))))

            # Date formatée
            sale_date = sale.get('sale_date', '')
            if sale_date:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(sale_date.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%d/%m/%Y')
                except:
                    formatted_date = sale_date
            else:
                formatted_date = ''
            self.sales_table.setItem(row, 1, QTableWidgetItem(formatted_date))

            # Client
            client_name = sale.get('client_name', 'Vente sans client')
            self.sales_table.setItem(row, 2, QTableWidgetItem(client_name))

            # Montant HT
            total_amount = float(sale.get('total_amount', 0))
            self.sales_table.setItem(row, 3, QTableWidgetItem(f"{total_amount:.2f} €"))

            # TVA
            tax_amount = float(sale.get('tax_amount', 0))
            self.sales_table.setItem(row, 4, QTableWidgetItem(f"{tax_amount:.2f} €"))

            # Total TTC
            final_amount = float(sale.get('final_amount', 0))
            self.sales_table.setItem(row, 5, QTableWidgetItem(f"{final_amount:.2f} €"))

            # Statut avec couleur
            status = sale.get('status', '')
            status_text = {
                'draft': 'Brouillon',
                'confirmed': 'Confirmée',
                'cancelled': 'Annulée'
            }.get(status, status)

            status_item = QTableWidgetItem(status_text)
            if status == 'confirmed':
                status_item.setBackground(status_item.background().color().lighter(150))
            elif status == 'cancelled':
                status_item.setBackground(status_item.background().color().darker(120))

            self.sales_table.setItem(row, 6, status_item)

            # Notes
            self.sales_table.setItem(row, 7, QTableWidgetItem(sale.get('notes', '')))

    def _update_stats(self):
        """Mettre à jour les statistiques"""
        try:
            db_manager = self.app_instance.get_database_manager()
            stats = db_manager.get_sales_statistics()

            self.daily_sales_label.setText(f"Ventes du jour: {stats['daily_sales']:.2f} €")
            self.monthly_sales_label.setText(f"Ventes du mois: {stats['monthly_sales']:.2f} €")
            self.pending_sales_label.setText(f"En attente: {stats['pending_sales']}")

        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour des stats: {e}")

    def _on_selection_changed(self):
        """Gérer le changement de sélection"""
        has_selection = len(self.sales_table.selectedItems()) > 0
        self.edit_btn.setEnabled(has_selection)
        self.status_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

    def _on_sale_double_click(self):
        """Gérer le double-clic sur une vente"""
        self._on_edit_sale()

    def _on_add_sale(self):
        """Ajouter une nouvelle vente"""
        try:
            from views.dialogs.sale_dialog import SaleDialog

            dialog = SaleDialog(self, self.app_instance)
            if dialog.exec_() == dialog.Accepted:
                sale_data = dialog.get_sale_data()

                # Séparer les items des données de vente
                items = sale_data.pop('items')

                # Ajouter l'utilisateur actuel
                if self.current_user:
                    sale_data['user_id'] = self.current_user.get('id')

                # Sauvegarder en base de données
                db_manager = self.app_instance.get_database_manager()
                sale_id = db_manager.create_sale(sale_data, items)

                if sale_id:
                    self.refresh_data()
                    self.success_message.emit("Vente créée avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la sauvegarde de la vente")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'ajout: {e}")
            self.error_occurred.emit(f"Erreur lors de l'ajout: {e}")

    def _on_edit_sale(self):
        """Modifier la vente sélectionnée"""
        try:
            selected_row = self.sales_table.currentRow()
            if selected_row < 0:
                return

            sale = self.filtered_sales[selected_row]

            # TODO: Implémenter l'édition de vente
            self.info_message.emit("Édition de vente à implémenter")

        except Exception as e:
            self.logger.error(f"Erreur lors de la modification: {e}")
            self.error_occurred.emit(f"Erreur lors de la modification: {e}")

    def _on_change_status(self):
        """Changer le statut de la vente sélectionnée"""
        try:
            selected_row = self.sales_table.currentRow()
            if selected_row < 0:
                return

            sale = self.filtered_sales[selected_row]
            current_status = sale.get('status', '')

            # Dialogue de sélection du nouveau statut
            from PyQt5.QtWidgets import QInputDialog

            statuses = [
                ("Brouillon", "draft"),
                ("Confirmée", "confirmed"),
                ("Annulée", "cancelled")
            ]

            status_labels = [label for label, value in statuses if value != current_status]

            if not status_labels:
                self.info_message.emit("Aucun changement de statut possible")
                return

            new_status_label, ok = QInputDialog.getItem(
                self,
                "Changer le statut",
                "Nouveau statut:",
                status_labels,
                0,
                False
            )

            if ok and new_status_label:
                # Trouver la valeur correspondante
                new_status = None
                for label, value in statuses:
                    if label == new_status_label:
                        new_status = value
                        break

                if new_status:
                    # Mettre à jour en base de données
                    db_manager = self.app_instance.get_database_manager()
                    success = db_manager.update_sale_status(sale['id'], new_status)

                    if success:
                        self.refresh_data()
                        self.success_message.emit(f"Statut mis à jour: {new_status_label}")
                    else:
                        self.error_occurred.emit("Erreur lors de la mise à jour du statut")

        except Exception as e:
            self.logger.error(f"Erreur lors du changement de statut: {e}")
            self.error_occurred.emit(f"Erreur lors du changement de statut: {e}")

    def _on_delete_sale(self):
        """Supprimer la vente sélectionnée"""
        try:
            selected_row = self.sales_table.currentRow()
            if selected_row < 0:
                return

            sale = self.filtered_sales[selected_row]

            # Dialogue de confirmation
            reply = QMessageBox.question(
                self,
                "Confirmer la suppression",
                f"Êtes-vous sûr de vouloir supprimer la vente #{sale.get('id', '')}?\n\n"
                f"Montant: {sale.get('final_amount', 0):.2f} €\n"
                f"Statut: {sale.get('status', '')}\n\n"
                "Cette action est irréversible.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # Supprimer de la base de données
                db_manager = self.app_instance.get_database_manager()
                success = db_manager.delete_sale(sale['id'])

                if success:
                    self.refresh_data()
                    self.success_message.emit("Vente supprimée avec succès")
                else:
                    self.error_occurred.emit("Erreur lors de la suppression de la vente")

        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression: {e}")
            self.error_occurred.emit(f"Erreur lors de la suppression: {e}")

    def refresh_data(self):
        """Actualiser les données depuis la base de données"""
        try:
            db_manager = self.app_instance.get_database_manager()
            self.sales_data = self._load_sales(db_manager)
            self._apply_filters()
            self._update_stats()

            self.logger.info(f"{len(self.sales_data)} ventes chargées")

        except Exception as e:
            self.logger.error(f"Erreur lors du chargement: {e}")
            self.error_occurred.emit(f"Erreur de chargement: {e}")

    def _load_sales(self, db_manager):
        """Charger les ventes depuis la base de données"""
        try:
            return db_manager.get_sales()
        except Exception as e:
            self.logger.error(f"Erreur lors du chargement des ventes: {e}")
            return []
