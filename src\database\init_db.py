"""
Script d'initialisation de la base de données GSlim
"""

import sqlite3
import os
from pathlib import Path


def initialize_database(db_path="data/gslim.db"):
    """Initialiser la base de données avec les tables nécessaires"""
    
    # Créer le répertoire data s'il n'existe pas
    data_dir = Path(db_path).parent
    data_dir.mkdir(exist_ok=True)
    
    # Connexion à la base de données
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Créer les tables
    tables = [
        # Table articles
        """
        CREATE TABLE IF NOT EXISTS articles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            description TEXT,
            prix_unitaire REAL NOT NULL,
            quantite_stock INTEGER NOT NULL,
            seuil_alerte INTEGER DEFAULT 0,
            categorie_id INTEGER,
            fournisseur_id INTEGER,
            date_creation TEXT,
            date_modification TEXT
        )
        """,
        
        # Table suppliers
        """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            contact TEXT,
            telephone TEXT,
            email TEXT,
            adresse TEXT,
            date_creation TEXT,
            date_modification TEXT
        )
        """,
        
        # Table categories
        """
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            description TEXT
        )
        """,
        
        # Table sales
        """
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            article_id INTEGER,
            quantite INTEGER NOT NULL,
            prix_unitaire REAL NOT NULL,
            total REAL NOT NULL,
            date_vente TEXT,
            FOREIGN KEY (article_id) REFERENCES articles (id)
        )
        """,
        
        # Table orders
        """
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER,
            statut TEXT DEFAULT 'en_attente',
            date_commande TEXT,
            date_livraison TEXT,
            total REAL,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
        """,
        
        # Table movements
        """
        CREATE TABLE IF NOT EXISTS movements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            article_id INTEGER,
            type_mouvement TEXT NOT NULL,
            quantite INTEGER NOT NULL,
            date_mouvement TEXT,
            commentaire TEXT,
            FOREIGN KEY (article_id) REFERENCES articles (id)
        )
        """
    ]
    
    # Exécuter la création des tables
    for table_sql in tables:
        cursor.execute(table_sql)
    
    # Insérer des données d'exemple si les tables sont vides
    cursor.execute("SELECT COUNT(*) FROM suppliers")
    if cursor.fetchone()[0] == 0:
        sample_suppliers = [
            ("TechSupply", "Jean Dupont", "01-23-45-67-89", "<EMAIL>", "123 Rue de la Tech"),
            ("Bureau Plus", "Marie Martin", "01-98-76-54-32", "<EMAIL>", "456 Avenue du Bureau"),
            ("Informatique Pro", "Pierre Durand", "01-11-22-33-44", "<EMAIL>", "789 Boulevard Info")
        ]
        
        for supplier in sample_suppliers:
            cursor.execute("""
                INSERT INTO suppliers (nom, contact, telephone, email, adresse)
                VALUES (?, ?, ?, ?, ?)
            """, supplier)
    
    cursor.execute("SELECT COUNT(*) FROM categories")
    if cursor.fetchone()[0] == 0:
        sample_categories = [
            ("Électronique", "Appareils électroniques"),
            ("Bureau", "Fournitures de bureau"),
            ("Informatique", "Matériel informatique")
        ]
        
        for category in sample_categories:
            cursor.execute("INSERT INTO categories (name, description) VALUES (?, ?)", category)
    
    cursor.execute("SELECT COUNT(*) FROM articles")
    if cursor.fetchone()[0] == 0:
        from datetime import datetime
        now = datetime.now().isoformat()
        
        sample_articles = [
            ("Ordinateur Portable", "Laptop 15 pouces", 899.99, 15, 5, 3, 3, now, now),
            ("Souris Optique", "Souris USB optique", 25.50, 50, 10, 1, 1, now, now),
            ("Clavier Mécanique", "Clavier gaming RGB", 129.99, 20, 5, 1, 1, now, now),
            ("Écran 24 pouces", "Moniteur Full HD", 199.99, 8, 3, 1, 1, now, now),
            ("Stylos Bille", "Lot de 10 stylos", 5.99, 100, 20, 2, 2, now, now)
        ]
        
        for article in sample_articles:
            cursor.execute("""
                INSERT INTO articles (
                    nom, description, prix_unitaire, quantite_stock, 
                    seuil_alerte, categorie_id, fournisseur_id, 
                    date_creation, date_modification
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, article)
    
    # Valider les changements
    conn.commit()
    conn.close()
    
    print(f"✅ Base de données initialisée: {db_path}")
    return True


if __name__ == "__main__":
    initialize_database()
