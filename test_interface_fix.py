#!/usr/bin/env python3
"""
Test des correctifs d'interface GSlim
Vérifie que tous les modules fonctionnent correctement
"""

import sys
import os

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def test_database_connection():
    """Tester la connexion à la base de données"""
    print("🔍 Test de la connexion à la base de données...")
    
    try:
        from database.manager import DatabaseManager
        
        db_manager = DatabaseManager()
        connection = db_manager.connect()
        
        if connection:
            print("✅ Connexion à la base de données réussie")
            
            # Tester une requête simple
            cursor = db_manager.cursor
            cursor.execute("SELECT COUNT(*) FROM articles")
            count = cursor.fetchone()[0]
            print(f"✅ {count} articles trouvés dans la base")
            
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            count = cursor.fetchone()[0]
            print(f"✅ {count} fournisseurs trouvés dans la base")
            
            db_manager.disconnect()
            return True
        else:
            print("❌ Échec de la connexion à la base de données")
            return False
            
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False


def test_article_controller():
    """Tester le contrôleur d'articles"""
    print("\n🔍 Test du contrôleur d'articles...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.article_controller import ArticleController
        
        db_manager = DatabaseManager()
        db_manager.connect()
        
        controller = ArticleController(db_manager)
        
        # Tester la récupération des articles
        articles = controller.get_all_articles()
        print(f"✅ {len(articles)} articles récupérés")
        
        # Tester les statistiques
        stats = controller.get_statistics()
        print(f"✅ Statistiques: {stats}")
        
        db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Erreur contrôleur articles: {e}")
        return False


def test_supplier_controller():
    """Tester le contrôleur de fournisseurs"""
    print("\n🔍 Test du contrôleur de fournisseurs...")
    
    try:
        from database.manager import DatabaseManager
        from controllers.supplier_controller import SupplierController
        
        db_manager = DatabaseManager()
        db_manager.connect()
        
        controller = SupplierController(db_manager)
        
        # Tester la récupération des fournisseurs
        suppliers = controller.get_all_suppliers()
        print(f"✅ {len(suppliers)} fournisseurs récupérés")
        
        # Tester les statistiques
        stats = controller.get_statistics()
        print(f"✅ Statistiques: {stats}")
        
        db_manager.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Erreur contrôleur fournisseurs: {e}")
        return False


def test_enhanced_widgets():
    """Tester les widgets améliorés"""
    print("\n🔍 Test des widgets améliorés...")
    
    try:
        from widgets.enhanced_widgets import EnhancedCard, EnhancedTable, StatusIndicator
        
        print("✅ Import EnhancedCard réussi")
        print("✅ Import EnhancedTable réussi")
        print("✅ Import StatusIndicator réussi")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur widgets améliorés: {e}")
        return False


def test_theme_manager():
    """Tester le gestionnaire de thèmes"""
    print("\n🔍 Test du gestionnaire de thèmes...")
    
    try:
        from styles.theme_manager import get_theme_manager, ThemeType, ThemeMode
        
        manager = get_theme_manager()
        print("✅ Gestionnaire de thèmes créé")
        
        # Tester les thèmes disponibles
        themes = manager.get_available_themes()
        print(f"✅ {len(themes)} thèmes disponibles")
        
        # Tester le changement de thème
        manager.set_theme(ThemeType.MODERN, ThemeMode.DARK)
        current = manager.get_current_theme()
        print(f"✅ Thème actuel: {current['type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur gestionnaire de thèmes: {e}")
        return False


def test_views():
    """Tester les vues"""
    print("\n🔍 Test des vues...")
    
    try:
        # Test des imports sans créer les widgets (pas de QApplication)
        import importlib.util
        
        # Test enhanced_articles_window
        spec = importlib.util.spec_from_file_location(
            "enhanced_articles_window", 
            "src/views/enhanced_articles_window.py"
        )
        if spec and spec.loader:
            print("✅ enhanced_articles_window importable")
        
        # Test enhanced_suppliers_window
        spec = importlib.util.spec_from_file_location(
            "enhanced_suppliers_window", 
            "src/views/enhanced_suppliers_window.py"
        )
        if spec and spec.loader:
            print("✅ enhanced_suppliers_window importable")
        
        # Test integrated_dashboard
        spec = importlib.util.spec_from_file_location(
            "integrated_dashboard", 
            "src/views/integrated_dashboard.py"
        )
        if spec and spec.loader:
            print("✅ integrated_dashboard importable")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur vues: {e}")
        return False


def run_all_tests():
    """Exécuter tous les tests"""
    print("🧪 TESTS DES CORRECTIFS INTERFACE GSLIM")
    print("="*50)
    
    tests = [
        ("Connexion base de données", test_database_connection),
        ("Contrôleur articles", test_article_controller),
        ("Contrôleur fournisseurs", test_supplier_controller),
        ("Widgets améliorés", test_enhanced_widgets),
        ("Gestionnaire de thèmes", test_theme_manager),
        ("Vues", test_views)
    ]
    
    success_count = 0
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                success_count += 1
        except Exception as e:
            print(f"❌ Erreur dans {test_name}: {e}")
    
    print(f"\n📊 RÉSULTATS DES TESTS")
    print("="*30)
    print(f"Tests réussis: {success_count}/{len(tests)}")
    
    if success_count == len(tests):
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ L'interface est prête à être utilisée")
        print("\n🚀 Vous pouvez maintenant lancer:")
        print("   python launch_enhanced.py")
        return True
    elif success_count >= len(tests) * 0.8:
        print("\n⚠️  TESTS MAJORITAIREMENT RÉUSSIS")
        print("La plupart des fonctionnalités sont opérationnelles")
        print("\n🚀 Vous pouvez essayer de lancer:")
        print("   python launch_enhanced.py")
        return True
    else:
        print("\n❌ PLUSIEURS TESTS ONT ÉCHOUÉ")
        print("Des corrections supplémentaires sont nécessaires")
        return False


def main():
    """Fonction principale"""
    try:
        success = run_all_tests()
        return 0 if success else 1
    except Exception as e:
        print(f"💥 Erreur critique: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
