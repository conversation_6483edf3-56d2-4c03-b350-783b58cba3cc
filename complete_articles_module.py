#!/usr/bin/env python3
"""
Module Articles Complet - GSlim
Finalise le module articles avec toutes les fonctionnalités
"""

import sys
import os
from pathlib import Path

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))


def create_complete_articles_controller():
    """Créer un contrôleur d'articles complet"""
    print("🔧 Création du contrôleur d'articles complet...")
    
    controller_content = '''"""
Contrôleur complet pour la gestion des articles
"""

import sqlite3
from datetime import datetime
from typing import List, Dict, Optional, Any
from utils.logger import setup_logger


class ArticleController:
    """Contrôleur complet pour la gestion des articles"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if not self.db_manager.connection:
            self.db_manager.connect()
    
    def get_all_articles(self) -> List[Dict[str, Any]]:
        """Récupérer tous les articles"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT a.*, c.nom as categorie_nom, s.nom as fournisseur_nom
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                ORDER BY a.nom
            """)
            
            articles = []
            for row in cursor.fetchall():
                article = dict(row) if hasattr(row, 'keys') else {
                    'id': row[0],
                    'nom': row[1],
                    'description': row[2],
                    'prix_unitaire': row[3],
                    'quantite_stock': row[4],
                    'seuil_alerte': row[5],
                    'categorie_id': row[6],
                    'fournisseur_id': row[7],
                    'date_creation': row[8],
                    'date_modification': row[9],
                    'categorie_nom': row[10] if len(row) > 10 else None,
                    'fournisseur_nom': row[11] if len(row) > 11 else None
                }
                articles.append(article)
            
            self.logger.info(f"{len(articles)} articles récupérés")
            return articles
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles: {e}")
            return []
    
    def get_article_by_id(self, article_id: int) -> Optional[Dict[str, Any]]:
        """Récupérer un article par son ID"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                return None
            
            cursor.execute("""
                SELECT a.*, c.nom as categorie_nom, s.nom as fournisseur_nom
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                WHERE a.id = ?
            """, (article_id,))
            
            row = cursor.fetchone()
            if row:
                return dict(row) if hasattr(row, 'keys') else {
                    'id': row[0],
                    'nom': row[1],
                    'description': row[2],
                    'prix_unitaire': row[3],
                    'quantite_stock': row[4],
                    'seuil_alerte': row[5],
                    'categorie_id': row[6],
                    'fournisseur_id': row[7],
                    'date_creation': row[8],
                    'date_modification': row[9],
                    'categorie_nom': row[10] if len(row) > 10 else None,
                    'fournisseur_nom': row[11] if len(row) > 11 else None
                }
            return None
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération de l'article {article_id}: {e}")
            return None
    
    def create_article(self, article_data: Dict[str, Any]) -> Optional[int]:
        """Créer un nouvel article"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Validation des données
            required_fields = ['nom', 'prix_unitaire', 'quantite_stock']
            for field in required_fields:
                if field not in article_data or article_data[field] is None:
                    raise ValueError(f"Le champ {field} est requis")
            
            # Insérer l'article
            query = """
                INSERT INTO articles (
                    nom, description, prix_unitaire, quantite_stock, 
                    seuil_alerte, categorie_id, fournisseur_id, 
                    date_creation, date_modification
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            now = datetime.now().isoformat()
            values = (
                article_data['nom'],
                article_data.get('description', ''),
                float(article_data['prix_unitaire']),
                int(article_data['quantite_stock']),
                int(article_data.get('seuil_alerte', 0)),
                article_data.get('categorie_id'),
                article_data.get('fournisseur_id'),
                now,
                now
            )
            
            cursor.execute(query, values)
            self.db_manager.connection.commit()
            
            article_id = cursor.lastrowid
            self.logger.info(f"Article créé avec l'ID {article_id}")
            return article_id
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la création de l'article: {e}")
            if self.db_manager.connection:
                self.db_manager.connection.rollback()
            raise
    
    def update_article(self, article_id: int, article_data: Dict[str, Any]) -> bool:
        """Mettre à jour un article"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Vérifier que l'article existe
            if not self.get_article_by_id(article_id):
                raise ValueError(f"Article avec l'ID {article_id} non trouvé")
            
            # Mettre à jour l'article
            query = """
                UPDATE articles 
                SET nom=?, description=?, prix_unitaire=?, quantite_stock=?, 
                    seuil_alerte=?, categorie_id=?, fournisseur_id=?, 
                    date_modification=?
                WHERE id=?
            """
            
            values = (
                article_data.get('nom'),
                article_data.get('description', ''),
                float(article_data.get('prix_unitaire', 0)),
                int(article_data.get('quantite_stock', 0)),
                int(article_data.get('seuil_alerte', 0)),
                article_data.get('categorie_id'),
                article_data.get('fournisseur_id'),
                datetime.now().isoformat(),
                article_id
            )
            
            cursor.execute(query, values)
            self.db_manager.connection.commit()
            
            success = cursor.rowcount > 0
            if success:
                self.logger.info(f"Article {article_id} mis à jour")
            return success
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la mise à jour de l'article {article_id}: {e}")
            if self.db_manager.connection:
                self.db_manager.connection.rollback()
            raise
    
    def delete_article(self, article_id: int) -> bool:
        """Supprimer un article"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                raise Exception("Impossible d'obtenir un curseur")
            
            # Vérifier que l'article existe
            if not self.get_article_by_id(article_id):
                raise ValueError(f"Article avec l'ID {article_id} non trouvé")
            
            cursor.execute("DELETE FROM articles WHERE id=?", (article_id,))
            self.db_manager.connection.commit()
            
            success = cursor.rowcount > 0
            if success:
                self.logger.info(f"Article {article_id} supprimé")
            return success
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la suppression de l'article {article_id}: {e}")
            if self.db_manager.connection:
                self.db_manager.connection.rollback()
            raise
    
    def search_articles(self, search_term: str) -> List[Dict[str, Any]]:
        """Rechercher des articles"""
        try:
            if not search_term.strip():
                return self.get_all_articles()
            
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            search_pattern = f"%{search_term}%"
            cursor.execute("""
                SELECT a.*, c.nom as categorie_nom, s.nom as fournisseur_nom
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                WHERE a.nom LIKE ? OR a.description LIKE ?
                ORDER BY a.nom
            """, (search_pattern, search_pattern))
            
            articles = []
            for row in cursor.fetchall():
                article = dict(row) if hasattr(row, 'keys') else {
                    'id': row[0],
                    'nom': row[1],
                    'description': row[2],
                    'prix_unitaire': row[3],
                    'quantite_stock': row[4],
                    'seuil_alerte': row[5],
                    'categorie_id': row[6],
                    'fournisseur_id': row[7],
                    'date_creation': row[8],
                    'date_modification': row[9],
                    'categorie_nom': row[10] if len(row) > 10 else None,
                    'fournisseur_nom': row[11] if len(row) > 11 else None
                }
                articles.append(article)
            
            self.logger.info(f"{len(articles)} articles trouvés pour '{search_term}'")
            return articles
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la recherche d'articles: {e}")
            return []
    
    def get_low_stock_articles(self) -> List[Dict[str, Any]]:
        """Récupérer les articles en rupture de stock"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                return []
            
            cursor.execute("""
                SELECT a.*, c.nom as categorie_nom, s.nom as fournisseur_nom
                FROM articles a
                LEFT JOIN categories c ON a.categorie_id = c.id
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                WHERE a.quantite_stock <= a.seuil_alerte
                ORDER BY a.quantite_stock ASC
            """)
            
            articles = []
            for row in cursor.fetchall():
                article = dict(row) if hasattr(row, 'keys') else {
                    'id': row[0],
                    'nom': row[1],
                    'description': row[2],
                    'prix_unitaire': row[3],
                    'quantite_stock': row[4],
                    'seuil_alerte': row[5],
                    'categorie_id': row[6],
                    'fournisseur_id': row[7],
                    'date_creation': row[8],
                    'date_modification': row[9],
                    'categorie_nom': row[10] if len(row) > 10 else None,
                    'fournisseur_nom': row[11] if len(row) > 11 else None
                }
                articles.append(article)
            
            return articles
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des articles en rupture: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """Récupérer les statistiques des articles"""
        try:
            if not self.db_manager.connection:
                self.db_manager.connect()
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {}
            
            stats = {}
            
            # Nombre total d'articles
            cursor.execute("SELECT COUNT(*) FROM articles")
            stats['total_articles'] = cursor.fetchone()[0]
            
            # Articles en rupture de stock
            cursor.execute("SELECT COUNT(*) FROM articles WHERE quantite_stock <= seuil_alerte")
            stats['low_stock_articles'] = cursor.fetchone()[0]
            
            # Valeur totale du stock
            cursor.execute("SELECT SUM(prix_unitaire * quantite_stock) FROM articles")
            result = cursor.fetchone()[0]
            stats['total_stock_value'] = float(result) if result else 0.0
            
            # Article le plus cher
            cursor.execute("SELECT nom, prix_unitaire FROM articles ORDER BY prix_unitaire DESC LIMIT 1")
            row = cursor.fetchone()
            if row:
                stats['most_expensive'] = {'nom': row[0], 'prix': row[1]}
            
            # Article avec le plus de stock
            cursor.execute("SELECT nom, quantite_stock FROM articles ORDER BY quantite_stock DESC LIMIT 1")
            row = cursor.fetchone()
            if row:
                stats['highest_stock'] = {'nom': row[0], 'quantite': row[1]}
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Erreur lors du calcul des statistiques: {e}")
            return {}
'''
    
    controller_file = "src/controllers/article_controller.py"
    with open(controller_file, 'w', encoding='utf-8') as f:
        f.write(controller_content)
    
    print("✅ Contrôleur d'articles complet créé")
    return True


def create_articles_demo():
    """Créer une démonstration du module articles"""
    print("🔧 Création de la démonstration articles...")
    
    demo_content = '''#!/usr/bin/env python3
"""
Démonstration du Module Articles - GSlim
Test complet du module articles amélioré
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from views.enhanced_articles_window import EnhancedArticlesWindow
from database.manager import DatabaseManager
from controllers.article_controller import ArticleController
from utils.error_handler import install_error_handler


class ArticlesDemoWindow(QMainWindow):
    """Fenêtre de démonstration du module articles"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_database()
        self.create_sample_data()
    
    def setup_ui(self):
        """Configurer l'interface"""
        self.setWindowTitle("GSlim - Démonstration Module Articles")
        self.setGeometry(100, 100, 1200, 800)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Module articles
        self.articles_window = EnhancedArticlesWindow(self)
        layout.addWidget(self.articles_window)
    
    def setup_database(self):
        """Configurer la base de données"""
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.connect()
            
            # Créer les tables si elles n'existent pas
            self.create_tables()
            
            # Créer le contrôleur
            self.article_controller = ArticleController(self.db_manager)
            
            # Connecter au module articles
            self.articles_window.set_controller(self.article_controller)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur de base de données: {e}")
    
    def create_tables(self):
        """Créer les tables nécessaires"""
        cursor = self.db_manager.cursor
        
        # Table articles
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS articles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                description TEXT,
                prix_unitaire REAL NOT NULL,
                quantite_stock INTEGER NOT NULL,
                seuil_alerte INTEGER DEFAULT 0,
                categorie_id INTEGER,
                fournisseur_id INTEGER,
                date_creation TEXT,
                date_modification TEXT
            )
        """)
        
        # Table categories
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL UNIQUE,
                description TEXT
            )
        """)
        
        # Table suppliers
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                contact TEXT,
                telephone TEXT,
                email TEXT,
                adresse TEXT
            )
        """)
        
        self.db_manager.connection.commit()
    
    def create_sample_data(self):
        """Créer des données d'exemple"""
        try:
            cursor = self.db_manager.cursor
            
            # Vérifier si des données existent déjà
            cursor.execute("SELECT COUNT(*) FROM articles")
            if cursor.fetchone()[0] > 0:
                return  # Des données existent déjà
            
            # Créer des catégories
            categories = [
                ("Électronique", "Appareils électroniques"),
                ("Bureau", "Fournitures de bureau"),
                ("Informatique", "Matériel informatique")
            ]
            
            for nom, desc in categories:
                cursor.execute("INSERT INTO categories (nom, description) VALUES (?, ?)", (nom, desc))
            
            # Créer des fournisseurs
            suppliers = [
                ("TechSupply", "<EMAIL>", "01-23-45-67-89", "<EMAIL>", "123 Rue Tech"),
                ("Bureau Plus", "<EMAIL>", "01-98-76-54-32", "<EMAIL>", "456 Ave Bureau"),
                ("Informatique Pro", "<EMAIL>", "01-11-22-33-44", "<EMAIL>", "789 Bd Info")
            ]
            
            for nom, contact, tel, email, adresse in suppliers:
                cursor.execute("""
                    INSERT INTO suppliers (nom, contact, telephone, email, adresse) 
                    VALUES (?, ?, ?, ?, ?)
                """, (nom, contact, tel, email, adresse))
            
            # Créer des articles d'exemple
            articles = [
                ("Ordinateur Portable", "Laptop 15 pouces", 899.99, 15, 5, 3, 3),
                ("Souris Optique", "Souris USB optique", 25.50, 50, 10, 1, 1),
                ("Clavier Mécanique", "Clavier gaming RGB", 129.99, 20, 5, 1, 1),
                ("Écran 24 pouces", "Moniteur Full HD", 199.99, 8, 3, 1, 1),
                ("Stylos Bille", "Lot de 10 stylos", 5.99, 100, 20, 2, 2),
                ("Cahiers A4", "Lot de 5 cahiers", 12.50, 75, 15, 2, 2),
                ("Imprimante Laser", "Imprimante noir et blanc", 299.99, 5, 2, 1, 3),
                ("Webcam HD", "Caméra 1080p", 79.99, 12, 3, 1, 1),
                ("Casque Audio", "Casque stéréo", 45.99, 25, 5, 1, 1),
                ("Disque Dur Externe", "1TB USB 3.0", 89.99, 18, 5, 3, 3)
            ]
            
            from datetime import datetime
            now = datetime.now().isoformat()
            
            for nom, desc, prix, stock, seuil, cat, fournisseur in articles:
                cursor.execute("""
                    INSERT INTO articles (
                        nom, description, prix_unitaire, quantite_stock, 
                        seuil_alerte, categorie_id, fournisseur_id, 
                        date_creation, date_modification
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (nom, desc, prix, stock, seuil, cat, fournisseur, now, now))
            
            self.db_manager.connection.commit()
            print("✅ Données d'exemple créées")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création des données: {e}")
    
    def get_database_manager(self):
        """Retourner le gestionnaire de base de données"""
        return self.db_manager
    
    def closeEvent(self, event):
        """Fermer proprement l'application"""
        if hasattr(self, 'db_manager'):
            self.db_manager.disconnect()
        event.accept()


def main():
    """Fonction principale"""
    # Installer le gestionnaire d'erreurs
    install_error_handler()
    
    app = QApplication(sys.argv)
    app.setApplicationName("GSlim Articles Demo")
    
    print("🚀 DÉMONSTRATION MODULE ARTICLES - GSLIM")
    print("="*50)
    print("✨ Fonctionnalités disponibles:")
    print("   📦 Gestion complète des articles")
    print("   🔍 Recherche et filtrage avancés")
    print("   📊 Statistiques en temps réel")
    print("   ✏️  Formulaires intelligents")
    print("   📋 Table interactive")
    print("   🎨 Interface moderne")
    print()
    print("🎯 Testez toutes les fonctionnalités !")
    print("="*50)
    
    window = ArticlesDemoWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
'''
    
    demo_file = "demo_articles.py"
    with open(demo_file, 'w', encoding='utf-8') as f:
        f.write(demo_content)
    
    print("✅ Démonstration articles créée")
    return True


def run_complete_articles():
    """Finaliser le module articles complet"""
    print("🔧 FINALISATION DU MODULE ARTICLES - GSLIM")
    print("="*60)
    
    tasks = [
        ("Création du contrôleur complet", create_complete_articles_controller),
        ("Création de la démonstration", create_articles_demo)
    ]
    
    success_count = 0
    for task_name, task_func in tasks:
        print(f"\\n📋 {task_name}...")
        try:
            result = task_func()
            if result is not False:
                success_count += 1
                print(f"✅ {task_name} terminé")
            else:
                print(f"❌ {task_name} échoué")
        except Exception as e:
            print(f"❌ Erreur dans {task_name}: {e}")
    
    print("\\n" + "="*60)
    print("📊 RÉSUMÉ DE LA FINALISATION")
    print("="*60)
    print(f"Tâches réussies: {success_count}/{len(tasks)}")
    
    if success_count == len(tasks):
        print("\\n🎉 MODULE ARTICLES FINALISÉ !")
        print("\\n📦 Fonctionnalités complètes:")
        print("   ✅ Contrôleur complet avec CRUD")
        print("   ✅ Recherche et filtrage avancés")
        print("   ✅ Statistiques en temps réel")
        print("   ✅ Gestion des stocks et alertes")
        print("   ✅ Interface moderne intégrée")
        print("   ✅ Démonstration interactive")
        
        print("\\n🎮 Tests disponibles:")
        print("1. Démonstration: python demo_articles.py")
        print("2. Interface complète: python launch_enhanced.py")
        print("3. Tests unitaires: python test_runner.py")
        
        print("\\n✨ Le module articles est maintenant complet et fonctionnel !")
    else:
        print("\\n⚠️  Finalisation partielle. Vérifiez les erreurs ci-dessus.")


def main():
    """Fonction principale"""
    try:
        run_complete_articles()
        return 0
    except KeyboardInterrupt:
        print("\\n⏹️  Finalisation interrompue par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\\n💥 Erreur critique: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
