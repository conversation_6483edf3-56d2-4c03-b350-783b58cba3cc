"""
Contrôleur pour la génération de rapports - Version simplifiée
Gère la création de rapports sans dépendances externes
"""

import os
from typing import List, Dict, Any, Optional
from datetime import datetime, date, timedelta
from decimal import Decimal

from utils.logger import setup_logger


class ReportController:
    """Contrôleur pour la génération de rapports - Version simplifiée"""
    
    def __init__(self, database_manager):
        self.db_manager = database_manager
        self.logger = setup_logger(__name__)
        
        # S'assurer que la connexion est établie
        if hasattr(self.db_manager, 'connect') and not self.db_manager.connection:
            self.db_manager.connect()
        
        self.logger.info("ReportController initialisé")
    
    def get_stock_report(self):
        """Générer un rapport de stock simplifié"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'articles': [], 'summary': {}}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'articles': [], 'summary': {}}
            
            # Articles avec détails
            cursor.execute("""
                SELECT a.*, s.nom as supplier_name, c.name as category_name
                FROM articles a
                LEFT JOIN suppliers s ON a.fournisseur_id = s.id
                LEFT JOIN categories c ON a.categorie_id = c.id
                ORDER BY a.nom
            """)
            
            articles = cursor.fetchall()
            articles_list = []
            if articles:
                columns = [desc[0] for desc in cursor.description]
                articles_list = [dict(zip(columns, row)) for row in articles]
            
            # Résumé
            cursor.execute("SELECT COUNT(*), SUM(quantite_stock), SUM(prix_unitaire * quantite_stock) FROM articles")
            summary_data = cursor.fetchone()
            
            summary = {
                'total_articles': summary_data[0] if summary_data[0] else 0,
                'total_quantity': summary_data[1] if summary_data[1] else 0,
                'total_value': summary_data[2] if summary_data[2] else 0.0
            }
            
            return {
                'articles': articles_list,
                'summary': summary
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport de stock: {e}")
            return {'articles': [], 'summary': {}}
    
    def get_sales_report(self, start_date=None, end_date=None):
        """Générer un rapport de ventes"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'sales': [], 'summary': {}}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'sales': [], 'summary': {}}
            
            # Construire la requête avec filtres de date
            query = """
                SELECT s.*, a.nom as article_name
                FROM sales s
                LEFT JOIN articles a ON s.article_id = a.id
            """
            params = []
            
            if start_date and end_date:
                query += " WHERE s.date_vente BETWEEN ? AND ?"
                params = [start_date, end_date]
            elif start_date:
                query += " WHERE s.date_vente >= ?"
                params = [start_date]
            elif end_date:
                query += " WHERE s.date_vente <= ?"
                params = [end_date]
            
            query += " ORDER BY s.date_vente DESC"
            
            cursor.execute(query, params)
            sales = cursor.fetchall()
            
            sales_list = []
            if sales:
                columns = [desc[0] for desc in cursor.description]
                sales_list = [dict(zip(columns, row)) for row in sales]
            
            # Résumé des ventes
            summary_query = "SELECT COUNT(*), SUM(total), SUM(quantite) FROM sales"
            if params:
                if len(params) == 2:
                    summary_query += " WHERE date_vente BETWEEN ? AND ?"
                elif start_date:
                    summary_query += " WHERE date_vente >= ?"
                else:
                    summary_query += " WHERE date_vente <= ?"
            
            cursor.execute(summary_query, params)
            summary_data = cursor.fetchone()
            
            summary = {
                'total_sales': summary_data[0] if summary_data[0] else 0,
                'total_revenue': summary_data[1] if summary_data[1] else 0.0,
                'total_quantity': summary_data[2] if summary_data[2] else 0
            }
            
            return {
                'sales': sales_list,
                'summary': summary
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport de ventes: {e}")
            return {'sales': [], 'summary': {}}
    
    def get_supplier_report(self):
        """Générer un rapport des fournisseurs"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'suppliers': [], 'summary': {}}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'suppliers': [], 'summary': {}}
            
            # Fournisseurs avec nombre d'articles
            cursor.execute("""
                SELECT s.*, COUNT(a.id) as articles_count,
                       SUM(a.quantite_stock * a.prix_unitaire) as total_value
                FROM suppliers s
                LEFT JOIN articles a ON s.id = a.fournisseur_id
                GROUP BY s.id
                ORDER BY s.nom
            """)
            
            suppliers = cursor.fetchall()
            suppliers_list = []
            if suppliers:
                columns = [desc[0] for desc in cursor.description]
                suppliers_list = [dict(zip(columns, row)) for row in suppliers]
            
            # Résumé
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            total_suppliers = cursor.fetchone()[0]
            
            summary = {
                'total_suppliers': total_suppliers,
                'active_suppliers': len([s for s in suppliers_list if s['articles_count'] > 0])
            }
            
            return {
                'suppliers': suppliers_list,
                'summary': summary
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport fournisseurs: {e}")
            return {'suppliers': [], 'summary': {}}
    
    def get_movement_report(self, start_date=None, end_date=None):
        """Générer un rapport des mouvements de stock"""
        try:
            if not hasattr(self.db_manager, 'connection') or not self.db_manager.connection:
                return {'movements': [], 'summary': {}}
            
            cursor = self.db_manager.cursor
            if not cursor:
                return {'movements': [], 'summary': {}}
            
            # Construire la requête avec filtres de date
            query = """
                SELECT m.*, a.nom as article_name
                FROM movements m
                LEFT JOIN articles a ON m.article_id = a.id
            """
            params = []
            
            if start_date and end_date:
                query += " WHERE m.date_mouvement BETWEEN ? AND ?"
                params = [start_date, end_date]
            elif start_date:
                query += " WHERE m.date_mouvement >= ?"
                params = [start_date]
            elif end_date:
                query += " WHERE m.date_mouvement <= ?"
                params = [end_date]
            
            query += " ORDER BY m.date_mouvement DESC"
            
            cursor.execute(query, params)
            movements = cursor.fetchall()
            
            movements_list = []
            if movements:
                columns = [desc[0] for desc in cursor.description]
                movements_list = [dict(zip(columns, row)) for row in movements]
            
            # Résumé des mouvements
            summary_query = """
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN type_mouvement = 'entrée' THEN quantite ELSE 0 END) as total_entries,
                    SUM(CASE WHEN type_mouvement = 'sortie' THEN quantite ELSE 0 END) as total_exits
                FROM movements
            """
            
            if params:
                if len(params) == 2:
                    summary_query += " WHERE date_mouvement BETWEEN ? AND ?"
                elif start_date:
                    summary_query += " WHERE date_mouvement >= ?"
                else:
                    summary_query += " WHERE date_mouvement <= ?"
            
            cursor.execute(summary_query, params)
            summary_data = cursor.fetchone()
            
            summary = {
                'total_movements': summary_data[0] if summary_data[0] else 0,
                'total_entries': summary_data[1] if summary_data[1] else 0,
                'total_exits': summary_data[2] if summary_data[2] else 0
            }
            
            return {
                'movements': movements_list,
                'summary': summary
            }
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport mouvements: {e}")
            return {'movements': [], 'summary': {}}
    
    def export_to_csv(self, data, filename):
        """Exporter des données vers un fichier CSV"""
        try:
            import csv
            
            if not data:
                return False
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                if isinstance(data, list) and data:
                    fieldnames = data[0].keys()
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Erreur lors de l'export CSV: {e}")
            return False
    
    def generate_simple_report(self, report_type='stock'):
        """Générer un rapport simple selon le type"""
        try:
            if report_type == 'stock':
                return self.get_stock_report()
            elif report_type == 'sales':
                return self.get_sales_report()
            elif report_type == 'suppliers':
                return self.get_supplier_report()
            elif report_type == 'movements':
                return self.get_movement_report()
            else:
                return {'error': f'Type de rapport non supporté: {report_type}'}
                
        except Exception as e:
            self.logger.error(f"Erreur lors de la génération du rapport {report_type}: {e}")
            return {'error': str(e)}
