#!/usr/bin/env python3
"""
Script de test pour les améliorations de l'interface utilisateur
Vérifie que tous les nouveaux composants fonctionnent correctement
"""

import sys
import os
import traceback

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Tester les imports des nouveaux modules"""
    print("🔍 Test des imports...")
    
    try:
        from styles.modern_theme import ModernTheme
        print("✅ ModernTheme importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur import ModernTheme: {e}")
        return False
    
    try:
        from widgets.modern_widgets import ModernStatCard, ModernProgressBar
        print("✅ Widgets modernes importés avec succès")
    except ImportError as e:
        print(f"❌ Erreur import widgets modernes: {e}")
        return False
    
    try:
        from widgets.advanced_ui import FloatingActionButton, NotificationToast, LoadingSpinner
        print("✅ Composants UI avancés importés avec succès")
    except ImportError as e:
        print(f"❌ Erreur import composants avancés: {e}")
        return False
    
    try:
        from widgets.modern_navigation import ModernNavigationPanel
        print("✅ Navigation moderne importée avec succès")
    except ImportError as e:
        print(f"❌ Erreur import navigation: {e}")
        return False
    
    return True

def test_theme_system():
    """Tester le système de thèmes"""
    print("\n🎨 Test du système de thèmes...")
    
    try:
        from styles.modern_theme import ModernTheme
        
        # Test des couleurs
        colors = ModernTheme.get_theme_colors(is_dark=False)
        assert 'background_primary' in colors
        assert 'text_primary' in colors
        print("✅ Couleurs du thème clair récupérées")
        
        colors_dark = ModernTheme.get_theme_colors(is_dark=True)
        assert colors_dark['background_primary'] != colors['background_primary']
        print("✅ Couleurs du thème sombre différentes du thème clair")
        
        # Test des stylesheets
        light_css = ModernTheme.get_light_stylesheet()
        assert len(light_css) > 1000  # Le CSS doit être substantiel
        print("✅ Stylesheet clair généré")
        
        dark_css = ModernTheme.get_dark_stylesheet()
        assert len(dark_css) > 1000
        print("✅ Stylesheet sombre généré")
        
        # Test des couleurs de statut
        status_colors = ModernTheme.get_status_colors()
        assert 'success' in status_colors
        assert 'error' in status_colors
        print("✅ Couleurs de statut disponibles")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test thème: {e}")
        traceback.print_exc()
        return False

def test_widgets_creation():
    """Tester la création des widgets"""
    print("\n🧩 Test de création des widgets...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from widgets.modern_widgets import ModernStatCard, ModernProgressBar
        from widgets.advanced_ui import FloatingActionButton, LoadingSpinner
        from widgets.modern_navigation import ModernNavigationPanel
        
        # Créer une application Qt temporaire
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test ModernStatCard
        card = ModernStatCard("Test", "123", "Subtitle", "primary")
        assert card.title == "Test"
        print("✅ ModernStatCard créée")
        
        # Test ModernProgressBar
        progress = ModernProgressBar(0, 100)
        progress.setValue(50, animate=False)
        print("✅ ModernProgressBar créée")
        
        # Test FloatingActionButton
        fab = FloatingActionButton("+")
        assert fab.text() == "+"
        print("✅ FloatingActionButton créé")
        
        # Test LoadingSpinner
        spinner = LoadingSpinner(32)
        assert spinner.size == 32
        print("✅ LoadingSpinner créé")
        
        # Test ModernNavigationPanel
        nav = ModernNavigationPanel()
        nav.add_navigation_item("test", "Test Item")
        assert "test" in nav.navigation_items
        print("✅ ModernNavigationPanel créé")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur création widgets: {e}")
        traceback.print_exc()
        return False

def test_animations():
    """Tester les animations"""
    print("\n🎬 Test des animations...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QPropertyAnimation
        from widgets.modern_widgets import ModernStatCard
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test animation de carte
        card = ModernStatCard("Test", "100", "", "primary")
        
        # Vérifier que les animations sont configurées
        assert hasattr(card, 'position_animation')
        assert isinstance(card.position_animation, QPropertyAnimation)
        print("✅ Animations de carte configurées")
        
        # Test mise à jour de valeur animée
        card.update_value("200", animate=True)
        assert hasattr(card, 'value_timer')
        print("✅ Animation de valeur configurée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test animations: {e}")
        traceback.print_exc()
        return False

def test_css_classes():
    """Tester les classes CSS"""
    print("\n🎨 Test des classes CSS...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from styles.modern_theme import ModernTheme
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # Test application du thème
        widget = QWidget()
        ModernTheme.apply_theme_to_widget(widget, "card")
        assert widget.property("class") == "card"
        print("✅ Classe CSS appliquée")
        
        # Test génération de dégradé
        gradient = ModernTheme.create_gradient_background("#ff0000", "#00ff00")
        assert "linear-gradient" in gradient
        print("✅ Dégradé généré")
        
        # Test effet d'ombre
        shadow = ModernTheme.create_shadow_effect("lg")
        assert "box-shadow" in shadow
        print("✅ Effet d'ombre généré")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test CSS: {e}")
        traceback.print_exc()
        return False

def test_demo_app():
    """Tester l'application de démonstration"""
    print("\n🚀 Test de l'application de démonstration...")
    
    try:
        # Vérifier que le fichier de démo existe
        demo_file = "demo_modern_ui.py"
        if not os.path.exists(demo_file):
            print(f"❌ Fichier de démo {demo_file} non trouvé")
            return False
        
        print("✅ Fichier de démonstration trouvé")
        
        # Tenter d'importer le module de démo
        import importlib.util
        spec = importlib.util.spec_from_file_location("demo_modern_ui", demo_file)
        demo_module = importlib.util.module_from_spec(spec)
        
        # Vérifier que la classe principale existe
        spec.loader.exec_module(demo_module)
        assert hasattr(demo_module, 'ModernUIDemo')
        print("✅ Classe ModernUIDemo trouvée")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur test démo: {e}")
        traceback.print_exc()
        return False

def test_documentation():
    """Tester la présence de la documentation"""
    print("\n📚 Test de la documentation...")
    
    docs = [
        "DESIGN_GUIDE.md",
        "UI_IMPROVEMENTS.md"
    ]
    
    all_found = True
    for doc in docs:
        if os.path.exists(doc):
            print(f"✅ {doc} trouvé")
        else:
            print(f"❌ {doc} manquant")
            all_found = False
    
    return all_found

def run_all_tests():
    """Exécuter tous les tests"""
    print("🧪 Démarrage des tests d'amélioration UI\n")
    
    tests = [
        ("Imports", test_imports),
        ("Système de thèmes", test_theme_system),
        ("Création de widgets", test_widgets_creation),
        ("Animations", test_animations),
        ("Classes CSS", test_css_classes),
        ("Application de démo", test_demo_app),
        ("Documentation", test_documentation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name}: {e}")
            results.append((test_name, False))
    
    # Résumé des résultats
    print("\n" + "="*50)
    print("📊 RÉSUMÉ DES TESTS")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSÉ" if result else "❌ ÉCHOUÉ"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print(f"\nRésultat: {passed}/{total} tests passés")
    
    if passed == total:
        print("🎉 Tous les tests sont passés! Les améliorations UI sont prêtes.")
        return True
    else:
        print("⚠️  Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        return False

def main():
    """Fonction principale"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n🚀 Pour tester l'interface graphique, exécutez:")
            print("   python demo_modern_ui.py")
            print("\n📖 Consultez la documentation:")
            print("   - DESIGN_GUIDE.md")
            print("   - UI_IMPROVEMENTS.md")
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrompus par l'utilisateur")
        return 1
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
