"""
Application principale GSlim - Version 1.0.2
Conforme au cahier des charges avec architecture moderne
Interface PyQt5 + Fluent Widgets selon spécifications
"""

import sys
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon

try:
    from qfluentwidgets import setTheme, Theme, qconfig
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from config.settings import config
from database.manager import DatabaseManager
from views.login_window import LoginWindow
from views.modern_main_window import ModernMainWindow
from utils.logger import setup_logger

class GSlimApp:
    """
    Application principale GSlim conforme au cahier des charges
    Version 1.0.2 avec architecture moderne
    """

    def __init__(self):
        """Initialiser l'application selon les spécifications"""
        self.logger = setup_logger(__name__)
        self.db_manager = None
        self.current_user = None
        self.app = None
        self.login_window = None
        self.main_window = None

        # Initialiser la base de données avec le nouveau schéma
        self._init_database()
        
    def _init_database(self):
        """Initialiser la base de données selon le nouveau schéma"""
        try:
            # Utiliser le pattern Singleton du DatabaseManager
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()
            self.logger.info("Base de données initialisée avec le nouveau schéma")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'initialisation de la base de données: {e}")
            # Affichage d'erreur sera géré après l'initialisation de QApplication
            raise
    
    def run(self):
        """Lancer l'application PyQt5"""
        try:
            self.logger.info(f"Démarrage de {config.APP_NAME} v{config.APP_VERSION}")

            # Créer l'application PyQt5
            self.app = QApplication(sys.argv)

            # Configuration de l'application
            self.app.setApplicationName(config.APP_NAME)
            self.app.setApplicationVersion(config.APP_VERSION)
            self.app.setOrganizationName("GSlim Team")

            # Configurer la police par défaut
            font = QFont("Segoe UI", 10)
            self.app.setFont(font)

            # Appliquer le thème selon les spécifications
            if FLUENT_AVAILABLE:
                if config.DEFAULT_THEME == "dark":
                    setTheme(Theme.DARK)
                else:
                    setTheme(Theme.LIGHT)
            else:
                # Fallback CSS pour PyQt5 standard
                self._apply_fallback_theme()

            # Afficher la fenêtre de connexion
            self.show_login()

            # Démarrer la boucle principale
            return self.app.exec_()

        except Exception as e:
            self.logger.error(f"Erreur lors du démarrage de l'application: {e}")
            self.logger.error(traceback.format_exc())

            if hasattr(self, 'app') and self.app:
                QMessageBox.critical(
                    None,
                    "Erreur critique",
                    f"Une erreur critique s'est produite:\n{e}"
                )
            else:
                print(f"Erreur critique: {e}")

            sys.exit(1)
    
    def show_login(self):
        """Afficher la fenêtre de connexion"""
        self.login_window = LoginWindow(self)
        self.login_window.show()

    def on_login_success(self, user):
        """Callback appelé lors d'une connexion réussie"""
        self.current_user = user
        self.logger.info(f"Connexion réussie pour l'utilisateur: {user['username']}")

        # Fermer la fenêtre de connexion et ouvrir la fenêtre principale
        if self.login_window:
            self.login_window.close()
        self.show_main_window()
    
    def show_main_window(self):
        """Afficher la fenêtre principale moderne"""
        try:
            # Créer la fenêtre principale moderne selon les spécifications
            self.main_window = ModernMainWindow(self)
            self.main_window.set_current_user(self.current_user)

            # Connecter les signaux
            self.main_window.user_logout.connect(self.logout)

            self.main_window.show()
            self.logger.info("Fenêtre principale moderne ouverte")

        except Exception as e:
            self.logger.error(f"Erreur lors de l'ouverture de la fenêtre principale: {e}")
            QMessageBox.critical(
                None,
                "Erreur",
                f"Impossible d'ouvrir la fenêtre principale:\n{e}"
            )
            sys.exit(1)
    
    def on_closing(self):
        """Gérer la fermeture de l'application"""
        try:
            reply = QMessageBox.question(
                None,
                "Quitter",
                "Voulez-vous vraiment quitter l'application ?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.logger.info("Fermeture de l'application")
                if self.db_manager:
                    self.db_manager.close()
                self.app.quit()

        except Exception as e:
            self.logger.error(f"Erreur lors de la fermeture: {e}")
            self.app.quit()

    def logout(self):
        """Déconnecter l'utilisateur"""
        try:
            self.logger.info(f"Déconnexion de l'utilisateur: {self.current_user['username']}")
            self.current_user = None

            # Fermer la fenêtre principale et retourner à la connexion
            if self.main_window:
                self.main_window.close()

            self.show_login()

        except Exception as e:
            self.logger.error(f"Erreur lors de la déconnexion: {e}")
            QMessageBox.critical(None, "Erreur", f"Erreur lors de la déconnexion:\n{e}")

    def _apply_fallback_theme(self):
        """Appliquer un thème de fallback pour PyQt5 standard"""
        fallback_css = """
        QMainWindow {
            background-color: #f0f0f0;
            color: #333333;
        }
        QWidget {
            font-family: 'Segoe UI', Arial, sans-serif;
            font-size: 10pt;
        }
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #106ebe;
        }
        QTableWidget {
            gridline-color: #e0e0e0;
            background-color: white;
            alternate-background-color: #f8f8f8;
        }
        """
        self.app.setStyleSheet(fallback_css)

    def switch_theme(self, theme_name):
        """Basculer entre les thèmes selon les spécifications"""
        try:
            if FLUENT_AVAILABLE:
                if theme_name.lower() == "dark":
                    setTheme(Theme.DARK)
                elif theme_name.lower() == "light":
                    setTheme(Theme.LIGHT)
                else:
                    setTheme(Theme.AUTO)
            else:
                self._apply_fallback_theme()

            self.logger.info(f"Thème changé vers: {theme_name}")
        except Exception as e:
            self.logger.error(f"Erreur lors du changement de thème: {e}")
    
    def get_database_manager(self):
        """Retourner le gestionnaire de base de données"""
        return self.db_manager
    
    def get_current_user(self):
        """Retourner l'utilisateur actuel"""
        return self.current_user
