"""
Dialogue de produit conforme au cahier des charges
Formulaire complet avec validation selon les spécifications
"""

import sys
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QPushButton,
    QLabel, QMessageBox, QFrame
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QDoubleValidator, QIntValidator

try:
    from qfluentwidgets import (
        Dialog, LineEdit, TextEdit, ComboBox, SpinBox, DoubleSpinBox,
        PushButton, TitleLabel, BodyLabel, CardWidget, FluentIcon
    )
    FLUENT_AVAILABLE = True
except ImportError:
    FLUENT_AVAILABLE = False

from config.settings import config
from utils.logger import setup_logger


class ProductDialog(QDialog):
    """
    Dialogue de produit selon les spécifications du cahier des charges
    Champs: Nom (requis), Description, Prix (requis, > 0), Quantité (requis, >= 0), Catégorie
    """

    def __init__(self, parent=None, categories_data=None, product_data=None):
        super().__init__(parent)
        self.categories_data = categories_data or []
        self.product_data = product_data or {}
        self.logger = setup_logger(__name__)
        
        # Mode édition ou création
        self.is_edit_mode = bool(product_data)
        
        # Initialiser l'interface
        self._init_ui()
        
        # Pré-remplir si mode édition
        if self.is_edit_mode:
            self._populate_fields()
        
        self.logger.info(f"Dialogue produit ouvert ({'édition' if self.is_edit_mode else 'création'})")

    def _init_ui(self):
        """Initialiser l'interface utilisateur selon les spécifications"""
        # Configuration de la fenêtre
        title = "Modifier le produit" if self.is_edit_mode else "Ajouter un produit"
        self.setWindowTitle(title)
        self.setModal(True)
        self.setFixedSize(500, 600)
        
        # Layout principal
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # En-tête
        self._create_header(main_layout)
        
        # Formulaire principal
        self._create_form(main_layout)
        
        # Boutons d'action
        self._create_buttons(main_layout)

    def _create_header(self, layout):
        """Créer l'en-tête du dialogue"""
        if FLUENT_AVAILABLE:
            title = TitleLabel("Modifier le produit" if self.is_edit_mode else "Nouveau produit")
            subtitle = BodyLabel("Modifiez les informations du produit" if self.is_edit_mode else "Saisissez les informations du nouveau produit")
        else:
            title = QLabel("Modifier le produit" if self.is_edit_mode else "Nouveau produit")
            title.setFont(QFont("Segoe UI", 16, QFont.Bold))
            subtitle = QLabel("Modifiez les informations du produit" if self.is_edit_mode else "Saisissez les informations du nouveau produit")
            subtitle.setFont(QFont("Segoe UI", 10))
        
        layout.addWidget(title)
        layout.addWidget(subtitle)

    def _create_form(self, layout):
        """Créer le formulaire selon les spécifications"""
        if FLUENT_AVAILABLE:
            form_card = CardWidget()
        else:
            form_card = QFrame()
            form_card.setFrameStyle(QFrame.Box)
        
        form_layout = QFormLayout(form_card)
        form_layout.setContentsMargins(20, 20, 20, 20)
        form_layout.setSpacing(15)
        
        # Nom (requis) selon spécifications
        if FLUENT_AVAILABLE:
            self.name_input = LineEdit()
        else:
            self.name_input = QLineEdit()
        
        self.name_input.setPlaceholderText("Nom du produit (requis)")
        form_layout.addRow("Nom *:", self.name_input)
        
        # Description selon spécifications
        if FLUENT_AVAILABLE:
            self.description_input = TextEdit()
        else:
            self.description_input = QTextEdit()
        
        self.description_input.setPlaceholderText("Description du produit")
        self.description_input.setMaximumHeight(80)
        form_layout.addRow("Description:", self.description_input)
        
        # Prix (requis, numérique > 0) selon spécifications
        if FLUENT_AVAILABLE:
            self.price_input = DoubleSpinBox()
        else:
            self.price_input = QDoubleSpinBox()
        
        self.price_input.setMinimum(0.01)
        self.price_input.setMaximum(999999.99)
        self.price_input.setDecimals(2)
        self.price_input.setSuffix(" €")
        self.price_input.setValue(0.01)
        form_layout.addRow("Prix de vente * (€):", self.price_input)
        
        # Quantité initiale (requis, numérique >= 0) selon spécifications
        if FLUENT_AVAILABLE:
            self.quantity_input = SpinBox()
        else:
            self.quantity_input = QSpinBox()
        
        self.quantity_input.setMinimum(0)
        self.quantity_input.setMaximum(999999)
        self.quantity_input.setValue(0)
        form_layout.addRow("Quantité en stock *:", self.quantity_input)
        
        # Seuil minimum
        if FLUENT_AVAILABLE:
            self.min_stock_input = SpinBox()
        else:
            self.min_stock_input = QSpinBox()
        
        self.min_stock_input.setMinimum(0)
        self.min_stock_input.setMaximum(999999)
        self.min_stock_input.setValue(config.DEFAULT_LOW_STOCK_THRESHOLD)
        form_layout.addRow("Seuil d'alerte:", self.min_stock_input)
        
        # Catégorie (ComboBox peuplé depuis la BD) selon spécifications
        if FLUENT_AVAILABLE:
            self.category_combo = ComboBox()
        else:
            self.category_combo = QComboBox()
        
        self._populate_categories()
        form_layout.addRow("Catégorie:", self.category_combo)
        
        # Code-barres
        if FLUENT_AVAILABLE:
            self.barcode_input = LineEdit()
        else:
            self.barcode_input = QLineEdit()
        
        self.barcode_input.setPlaceholderText("Code-barres (optionnel)")
        form_layout.addRow("Code-barres:", self.barcode_input)
        
        # Unité
        if FLUENT_AVAILABLE:
            self.unit_combo = ComboBox()
        else:
            self.unit_combo = QComboBox()
        
        units = ["pièce", "kg", "litre", "mètre", "boîte", "paquet"]
        self.unit_combo.addItems(units)
        form_layout.addRow("Unité:", self.unit_combo)
        
        layout.addWidget(form_card)

    def _create_buttons(self, layout):
        """Créer les boutons d'action"""
        buttons_layout = QHBoxLayout()
        
        if FLUENT_AVAILABLE:
            self.save_btn = PushButton("Enregistrer")
            self.save_btn.setIcon(FluentIcon.SAVE)
            
            self.cancel_btn = PushButton("Annuler")
            self.cancel_btn.setIcon(FluentIcon.CANCEL)
        else:
            self.save_btn = QPushButton("Enregistrer")
            self.cancel_btn = QPushButton("Annuler")
        
        # Connecter les signaux
        self.save_btn.clicked.connect(self._on_save)
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)
        
        layout.addLayout(buttons_layout)

    def _populate_categories(self):
        """Peupler le ComboBox des catégories"""
        self.category_combo.clear()
        self.category_combo.addItem("Aucune catégorie", None)
        
        for category in self.categories_data:
            self.category_combo.addItem(
                category.get('name', ''),
                category.get('id')
            )

    def _populate_fields(self):
        """Pré-remplir les champs en mode édition"""
        if not self.product_data:
            return
        
        self.name_input.setText(self.product_data.get('name', ''))
        
        if hasattr(self.description_input, 'setPlainText'):
            self.description_input.setPlainText(self.product_data.get('description', ''))
        else:
            self.description_input.setText(self.product_data.get('description', ''))
        
        self.price_input.setValue(float(self.product_data.get('price', 0)))
        self.quantity_input.setValue(int(self.product_data.get('stock_quantity', 0)))
        self.min_stock_input.setValue(int(self.product_data.get('minimum_stock', 0)))
        self.barcode_input.setText(self.product_data.get('barcode', ''))
        
        # Sélectionner la catégorie
        category_id = self.product_data.get('category_id')
        if category_id:
            for i in range(self.category_combo.count()):
                if self.category_combo.itemData(i) == category_id:
                    self.category_combo.setCurrentIndex(i)
                    break
        
        # Sélectionner l'unité
        unit = self.product_data.get('unit', 'pièce')
        index = self.unit_combo.findText(unit)
        if index >= 0:
            self.unit_combo.setCurrentIndex(index)

    def _on_save(self):
        """Gérer la sauvegarde avec validation selon spécifications"""
        try:
            # Validation selon spécifications
            if not self._validate_form():
                return
            
            # Accepter le dialogue
            self.accept()
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la sauvegarde: {e}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {e}")

    def _validate_form(self):
        """Valider le formulaire selon les spécifications"""
        # Nom requis
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "Validation", "Le nom du produit est requis.")
            self.name_input.setFocus()
            return False
        
        # Prix requis et > 0
        price = self.price_input.value()
        if price <= 0:
            QMessageBox.warning(self, "Validation", "Le prix doit être supérieur à 0.")
            self.price_input.setFocus()
            return False
        
        # Quantité >= 0
        quantity = self.quantity_input.value()
        if quantity < 0:
            QMessageBox.warning(self, "Validation", "La quantité doit être positive ou nulle.")
            self.quantity_input.setFocus()
            return False
        
        return True

    def get_product_data(self):
        """Récupérer les données du produit depuis le formulaire"""
        description = ""
        if hasattr(self.description_input, 'toPlainText'):
            description = self.description_input.toPlainText()
        else:
            description = self.description_input.text()
        
        return {
            'id': self.product_data.get('id') if self.is_edit_mode else None,
            'name': self.name_input.text().strip(),
            'description': description.strip(),
            'price': self.price_input.value(),
            'stock_quantity': self.quantity_input.value(),
            'minimum_stock': self.min_stock_input.value(),
            'category_id': self.category_combo.currentData(),
            'barcode': self.barcode_input.text().strip(),
            'unit': self.unit_combo.currentText()
        }
