#!/usr/bin/env python3
"""
Démonstration du Module Articles - GSlim
Test complet du module articles amélioré
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import Qt

# Ajouter le répertoire src au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from views.enhanced_articles_window import EnhancedArticlesWindow
from database.manager import DatabaseManager
from controllers.article_controller import ArticleController
from utils.error_handler import install_error_handler


class ArticlesDemoWindow(QMainWindow):
    """Fenêtre de démonstration du module articles"""
    
    def __init__(self):
        super().__init__()
        self.setup_database()
        self.setup_ui()
        self.create_sample_data()
    
    def setup_ui(self):
        """Configurer l'interface"""
        self.setWindowTitle("GSlim - Démonstration Module Articles")
        self.setGeometry(100, 100, 1200, 800)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # Module articles
        self.articles_window = EnhancedArticlesWindow(self)
        layout.addWidget(self.articles_window)
    
    def setup_database(self):
        """Configurer la base de données"""
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.connect()
            
            # Créer les tables si elles n'existent pas
            self.create_tables()
            
            # Créer le contrôleur
            self.article_controller = ArticleController(self.db_manager)
            
            # Connecter au module articles
            self.articles_window.set_controller(self.article_controller)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur de base de données: {e}")
    
    def create_tables(self):
        """Créer les tables nécessaires"""
        cursor = self.db_manager.cursor
        
        # Table articles
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS articles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                description TEXT,
                prix_unitaire REAL NOT NULL,
                quantite_stock INTEGER NOT NULL,
                seuil_alerte INTEGER DEFAULT 0,
                categorie_id INTEGER,
                fournisseur_id INTEGER,
                date_creation TEXT,
                date_modification TEXT
            )
        """)
        
        # Table categories
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL UNIQUE,
                description TEXT
            )
        """)
        
        # Table suppliers
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                contact TEXT,
                telephone TEXT,
                email TEXT,
                adresse TEXT
            )
        """)
        
        self.db_manager.connection.commit()
    
    def create_sample_data(self):
        """Créer des données d'exemple"""
        try:
            cursor = self.db_manager.cursor
            
            # Vérifier si des données existent déjà
            cursor.execute("SELECT COUNT(*) FROM articles")
            if cursor.fetchone()[0] > 0:
                return  # Des données existent déjà
            
            # Créer des catégories
            categories = [
                ("Électronique", "Appareils électroniques"),
                ("Bureau", "Fournitures de bureau"),
                ("Informatique", "Matériel informatique")
            ]
            
            for nom, desc in categories:
                cursor.execute("INSERT INTO categories (nom, description) VALUES (?, ?)", (nom, desc))
            
            # Créer des fournisseurs
            suppliers = [
                ("TechSupply", "<EMAIL>", "01-23-45-67-89", "<EMAIL>", "123 Rue Tech"),
                ("Bureau Plus", "<EMAIL>", "01-98-76-54-32", "<EMAIL>", "456 Ave Bureau"),
                ("Informatique Pro", "<EMAIL>", "01-11-22-33-44", "<EMAIL>", "789 Bd Info")
            ]
            
            for nom, contact, tel, email, adresse in suppliers:
                cursor.execute("""
                    INSERT INTO suppliers (nom, contact, telephone, email, adresse) 
                    VALUES (?, ?, ?, ?, ?)
                """, (nom, contact, tel, email, adresse))
            
            # Créer des articles d'exemple
            articles = [
                ("Ordinateur Portable", "Laptop 15 pouces", 899.99, 15, 5, 3, 3),
                ("Souris Optique", "Souris USB optique", 25.50, 50, 10, 1, 1),
                ("Clavier Mécanique", "Clavier gaming RGB", 129.99, 20, 5, 1, 1),
                ("Écran 24 pouces", "Moniteur Full HD", 199.99, 8, 3, 1, 1),
                ("Stylos Bille", "Lot de 10 stylos", 5.99, 100, 20, 2, 2),
                ("Cahiers A4", "Lot de 5 cahiers", 12.50, 75, 15, 2, 2),
                ("Imprimante Laser", "Imprimante noir et blanc", 299.99, 5, 2, 1, 3),
                ("Webcam HD", "Caméra 1080p", 79.99, 12, 3, 1, 1),
                ("Casque Audio", "Casque stéréo", 45.99, 25, 5, 1, 1),
                ("Disque Dur Externe", "1TB USB 3.0", 89.99, 18, 5, 3, 3)
            ]
            
            from datetime import datetime
            now = datetime.now().isoformat()
            
            for nom, desc, prix, stock, seuil, cat, fournisseur in articles:
                cursor.execute("""
                    INSERT INTO articles (
                        nom, description, prix_unitaire, quantite_stock, 
                        seuil_alerte, categorie_id, fournisseur_id, 
                        date_creation, date_modification
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (nom, desc, prix, stock, seuil, cat, fournisseur, now, now))
            
            self.db_manager.connection.commit()
            print("✅ Données d'exemple créées")
            
        except Exception as e:
            print(f"❌ Erreur lors de la création des données: {e}")
    
    def get_database_manager(self):
        """Retourner le gestionnaire de base de données"""
        return self.db_manager
    
    def closeEvent(self, event):
        """Fermer proprement l'application"""
        if hasattr(self, 'db_manager'):
            self.db_manager.disconnect()
        event.accept()


def main():
    """Fonction principale"""
    # Installer le gestionnaire d'erreurs
    install_error_handler()
    
    app = QApplication(sys.argv)
    app.setApplicationName("GSlim Articles Demo")
    
    print("🚀 DÉMONSTRATION MODULE ARTICLES - GSLIM")
    print("="*50)
    print("✨ Fonctionnalités disponibles:")
    print("   📦 Gestion complète des articles")
    print("   🔍 Recherche et filtrage avancés")
    print("   📊 Statistiques en temps réel")
    print("   ✏️  Formulaires intelligents")
    print("   📋 Table interactive")
    print("   🎨 Interface moderne")
    print()
    print("🎯 Testez toutes les fonctionnalités !")
    print("="*50)
    
    window = ArticlesDemoWindow()
    window.show()
    
    return app.exec_()


if __name__ == "__main__":
    sys.exit(main())
